using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;

namespace pmcrms_api.Application.DTOs.Applications
{
    public class GuestApplicationSubmitRequest
    {
        // Basic Information
        public string Position { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string MiddleName { get; set; } = string.Empty; // Added missing field
        public string LastName { get; set; } = string.Empty;
        public string MotherName { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string EmailId { get; set; } = string.Empty;
        public string BirthDate { get; set; } = string.Empty;
        public string BloodGroup { get; set; } = string.Empty;
        public string Height { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;

        // Address Details
        public AddressDto LocalAddress { get; set; } = new();
        public AddressDto PermanentAddress { get; set; } = new();
        public bool PermanentSameAsLocal { get; set; }

        // Identity Information
        public string PanNumber { get; set; } = string.Empty;
        public IFormFile? PanAttachment { get; set; } // Added missing field
        public string AadharNumber { get; set; } = string.Empty;
        public IFormFile? AadharAttachment { get; set; } // Added missing field

        // Document attachments from frontend
        public IFormFile? ElectricityBill { get; set; } // Added missing field
        public IFormFile? StructuralEngineerDoc { get; set; } // Added missing field

        // Essential Details (for Architect position only)
        public EssentialDetailsDto? EssentialDetails { get; set; }

        // Qualification
        public QualificationDto Qualification { get; set; } = new();

        // Experience
        public List<GuestExperienceDto> Experiences { get; set; } = new();

        // Documents
        public List<DocumentDto> Documents { get; set; } = new();

        // Additional Files
        public IFormFile? SelfDeclarationFile { get; set; }
        public IFormFile? ProfilePicture { get; set; }

        // Meta Information
        public string SubmittedAt { get; set; } = string.Empty;
    }

    public class AddressDto
    {
        public string FlatHouseNumber { get; set; } = string.Empty;
        public string StreetAddress { get; set; } = string.Empty;
        public string AddressLine { get; set; } = string.Empty; // Added missing field
        public string Address { get; set; } = string.Empty; // Added missing field (for permanent address)
        public string Country { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
    }

    public class EssentialDetailsDto
    {
        // COA (Council of Architecture) Details for Architect position
        public string? CoaCertificationNumber { get; set; } = string.Empty;
        public string? CoaValidity { get; set; } = string.Empty; // Date string
        public IFormFile? CoaCertificate { get; set; }
    }

    public class QualificationDto
    {
        public string InstituteName { get; set; } = string.Empty;
        public string UniversityName { get; set; } = string.Empty;
        public string CourseSpecialization { get; set; } = string.Empty;
        public string DegreeProgram { get; set; } = string.Empty;
        public string PassingMonth { get; set; } = string.Empty;
        public string PassingYear { get; set; } = string.Empty;
        public IFormFile? Certificate { get; set; } // Added missing field
        public IFormFile? LastYearMarksheet { get; set; } // Added missing field
    }

    public class GuestExperienceDto
    {
        public string CompanyName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string YearsOfExperience { get; set; } = string.Empty;
        public string FromDate { get; set; } = string.Empty;
        public string ToDate { get; set; } = string.Empty;
        public IFormFile? Certificate { get; set; }
    }

    public class DocumentDto
    {
        public string DocumentName { get; set; } = string.Empty;
        public IFormFile? Attachment { get; set; }
    }
}
