// Applications Analytics Cube
// This cube provides comprehensive analytics for application data

cube(`Applications`, {
  sql: `
    SELECT 
      a.Id,
      a.<PERSON>Number,
      CONCAT(a.FirstName, ' ', a.LastName) as ApplicantName,
      a.FirstName,
      a.LastName,
      a.Position,
      a.CurrentState as Status,
      a.SubmissionDate,
      a.ApprovalDate,
      a.AssignedToUserId,
      CONCAT(u.FirstName, ' ', u.LastName) as AssignedTo,
      a.MobileNumber,
      a.EmailAddress,
      DATEDIFF(day, a.SubmissionDate, COALESCE(a.ApprovalDate, GETUTCDATE())) as ProcessingDays,
      YEAR(a.SubmissionDate) as SubmissionYear,
      MONTH(a.SubmissionDate) as SubmissionMonth,
      DATEPART(quarter, a.SubmissionDate) as SubmissionQuarter,
      CASE 
        WHEN a.CurrentState = 5 THEN 'FINAL_APPROVE'
        WHEN a.CurrentState = 8 THEN 'REJECTED' 
        WHEN a.CurrentState = 1 THEN 'DOCUMENT_VERIFICATION_PENDING'
        ELSE 'IN_PROGRESS'
      END as StatusCategory
    FROM Applications a
    LEFT JOIN Users u ON a.AssignedToUserId = u.Id
  `,

  measures: {
    count: {
      type: `count`,
      title: `Total Applications`,
      description: `Total number of applications submitted`
    },
    
    averageProcessingTime: {
      type: `avg`,
      sql: `ProcessingDays`,
      title: `Average Processing Time (Days)`,
      description: `Average time taken to process applications`
    },

    completedApplications: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.Status = 5` } // FINAL_APPROVE
      ],
      title: `Completed Applications`,
      description: `Number of approved applications`
    },

    rejectedApplications: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.Status = 8` } // REJECTED
      ],
      title: `Rejected Applications`,
      description: `Number of rejected applications`
    },

    pendingApplications: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.Status NOT IN (5, 8)` }
      ],
      title: `Pending Applications`,
      description: `Number of applications in progress`
    },

    approvalRate: {
      type: `number`,
      sql: `
        CASE 
          WHEN COUNT(*) > 0 THEN 
            (COUNT(CASE WHEN ${CUBE}.Status = 5 THEN 1 END) * 100.0 / COUNT(*))
          ELSE 0 
        END
      `,
      title: `Approval Rate (%)`,
      description: `Percentage of applications that get approved`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `number`,
      primaryKey: true,
      shown: false
    },
    
    applicationNumber: {
      sql: `ApplicationNumber`,
      type: `string`,
      title: `Application Number`,
      description: `Unique application identifier`
    },

    applicantName: {
      sql: `ApplicantName`,
      type: `string`,
      title: `Applicant Name`,
      description: `Full name of the applicant`
    },
    
    position: {
      sql: `
        CASE Position
          WHEN 1 THEN 'Architect'
          WHEN 2 THEN 'License Engineer' 
          WHEN 3 THEN 'Structural Engineer'
          WHEN 4 THEN 'Supervisor1'
          WHEN 5 THEN 'Supervisor2'
          ELSE 'Unknown'
        END
      `,
      type: `string`,
      title: `Position Type`,
      description: `Type of position applied for`
    },
    
    status: {
      sql: `
        CASE Status
          WHEN 1 THEN 'DOCUMENT_VERIFICATION_PENDING'
          WHEN 2 THEN 'JUNIOR_ENGINEER_PENDING'
          WHEN 3 THEN 'ASSISTANT_ENGINEER_PENDING' 
          WHEN 4 THEN 'EXECUTIVE_ENGINEER_PENDING'
          WHEN 5 THEN 'FINAL_APPROVE'
          WHEN 6 THEN 'CITY_ENGINEER_PENDING'
          WHEN 7 THEN 'PAYMENT_PENDING'
          WHEN 8 THEN 'REJECTED'
          ELSE 'UNKNOWN'
        END
      `,
      type: `string`,
      title: `Workflow Status`,
      description: `Current status in the workflow`
    },

    statusCategory: {
      sql: `StatusCategory`,
      type: `string`,
      title: `Status Category`,
      description: `Simplified status categories`
    },

    assignedTo: {
      sql: `AssignedTo`,
      type: `string`,
      title: `Assigned Officer`,
      description: `Officer assigned to handle the application`
    },
    
    submittedDate: {
      sql: `SubmissionDate`,
      type: `time`,
      title: `Submitted Date`,
      description: `Date when application was submitted`
    },

    approvalDate: {
      sql: `ApprovalDate`,
      type: `time`,
      title: `Approval Date`,
      description: `Date when application was approved/rejected`
    },

    submissionYear: {
      sql: `SubmissionYear`,
      type: `number`,
      title: `Submission Year`,
      description: `Year of submission`
    },

    submissionMonth: {
      sql: `SubmissionMonth`, 
      type: `number`,
      title: `Submission Month`,
      description: `Month of submission`
    },

    submissionQuarter: {
      sql: `SubmissionQuarter`,
      type: `number`, 
      title: `Submission Quarter`,
      description: `Quarter of submission`
    },

    processingTimeRange: {
      sql: `
        CASE 
          WHEN ProcessingDays <= 7 THEN '0-7 days'
          WHEN ProcessingDays <= 15 THEN '8-15 days'  
          WHEN ProcessingDays <= 30 THEN '16-30 days'
          WHEN ProcessingDays <= 60 THEN '31-60 days'
          ELSE '60+ days'
        END
      `,
      type: `string`,
      title: `Processing Time Range`,
      description: `Categorized processing time ranges`
    }
  },

  segments: {
    recentApplications: {
      sql: `${CUBE}.SubmissionDate >= DATEADD(month, -3, GETUTCDATE())`,
      title: `Recent Applications`,
      description: `Applications from last 3 months`
    },

    fastProcessing: {
      sql: `${CUBE}.ProcessingDays <= 15`,
      title: `Fast Processing`,
      description: `Applications processed within 15 days`
    },

    slowProcessing: {
      sql: `${CUBE}.ProcessingDays > 30`,
      title: `Slow Processing`, 
      description: `Applications taking more than 30 days`
    },

    architectApplications: {
      sql: `${CUBE}.Position = 1`,
      title: `Architect Applications`,
      description: `Applications for Architect position`
    }
  },

  preAggregations: {
    // Main rollup for dashboard
    main: {
      measures: [
        CUBE.count,
        CUBE.averageProcessingTime,
        CUBE.completedApplications,
        CUBE.rejectedApplications,
        CUBE.pendingApplications
      ],
      dimensions: [CUBE.position, CUBE.status, CUBE.statusCategory],
      timeDimension: CUBE.submittedDate,
      granularity: `day`,
      partitionGranularity: `month`,
      refreshKey: {
        every: `1 hour`
      },
      buildRangeStart: {
        sql: `SELECT DATEADD(year, -2, GETUTCDATE())`
      },
      buildRangeEnd: {
        sql: `SELECT GETUTCDATE()`
      }
    },

    // Monthly aggregation for trends
    monthlyTrend: {
      measures: [CUBE.count, CUBE.averageProcessingTime],
      dimensions: [CUBE.position],
      timeDimension: CUBE.submittedDate,
      granularity: `month`,
      refreshKey: {
        every: `2 hours`
      }
    },

    // Position-wise breakdown
    positionBreakdown: {
      measures: [
        CUBE.count, 
        CUBE.completedApplications, 
        CUBE.rejectedApplications,
        CUBE.approvalRate
      ],
      dimensions: [CUBE.position, CUBE.statusCategory],
      refreshKey: {
        every: `30 minutes`
      }
    },

    // Officer performance
    officerPerformance: {
      measures: [CUBE.count, CUBE.averageProcessingTime],
      dimensions: [CUBE.assignedTo, CUBE.status],
      refreshKey: {
        every: `1 hour`
      }
    }
  }
});
