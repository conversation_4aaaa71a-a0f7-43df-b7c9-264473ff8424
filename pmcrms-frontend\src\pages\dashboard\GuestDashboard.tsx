import React, { useState, useEffect } from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { But<PERSON> } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { 
  Plus, 
  FileText, 
  Search, 
  Download, 
  Eye, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle, 
  ArrowUpDown,
  LogOut
} from 'lucide-react'
import { useGuestAuth } from '../../hooks/useGuestAuth'
import Logo from '../../components/Logo'

// Application interface for guest dashboard
interface GuestApplication {
  id: string
  applicationNumber: string
  applicationType: string
  status: string
  submissionDate: string
  lastUpdated: string
  description?: string
  estimatedCompletion?: string
}

// Status configuration
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'FINAL_APPROVE':
    case 'APPROVED':
      return { 
        icon: CheckCircle, 
        color: 'text-green-600 bg-green-100 border-green-200', 
        label: 'Approved',
        dotColor: 'bg-green-500'
      }
    case 'REJECTED':
      return { 
        icon: XCircle, 
        color: 'text-red-600 bg-red-100 border-red-200', 
        label: 'Rejected',
        dotColor: 'bg-red-500'
      }
    case 'UNDER_REVIEW':
    case 'IN_PROGRESS':
      return { 
        icon: Clock, 
        color: 'text-blue-600 bg-blue-100 border-blue-200', 
        label: 'Under Review',
        dotColor: 'bg-blue-500'
      }
    case 'PAYMENT_PENDING':
      return { 
        icon: AlertCircle, 
        color: 'text-amber-600 bg-amber-100 border-amber-200', 
        label: 'Payment Pending',
        dotColor: 'bg-amber-500'
      }
    case 'SUBMITTED':
    case 'PENDING':
    default:
      return { 
        icon: Clock, 
        color: 'text-gray-600 bg-gray-100 border-gray-200', 
        label: 'Pending',
        dotColor: 'bg-gray-500'
      }
  }
}

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const config = getStatusConfig(status)
  const Icon = config.icon
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  )
}

const GuestDashboard: React.FC = () => {
  const navigate = useNavigate()
  const { guestSession, clearGuestSession } = useGuestAuth()
  
  const [applications, setApplications] = useState<GuestApplication[]>([])
  const [filteredApplications, setFilteredApplications] = useState<GuestApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [isFetching, setIsFetching] = useState(false) // Prevent multiple API calls

  useEffect(() => {
    console.log('GuestDashboard useEffect - guestSession:', guestSession)
    
    // Check localStorage directly as backup
    const sessionData = localStorage.getItem('guest_session')
    const guestEmail = localStorage.getItem('guest_email')
    console.log('Direct localStorage check - sessionData:', sessionData)
    console.log('Direct localStorage check - guestEmail:', guestEmail)
    
    // Try to parse session data to get email
    let sessionEmail = null
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData)
        sessionEmail = parsedSession.email
        console.log('Parsed session email:', sessionEmail)
      } catch (error) {
        console.error('Error parsing session data:', error)
      }
    }
    
    // Only proceed if we have session data (either from hook, localStorage email, or parsed session)
    const hasValidSession = guestSession?.email || guestEmail || sessionEmail
    
    if (!hasValidSession) {
      console.log('No guest session found, navigating to guest login')
      navigate('/guest')
      return
    }
    
    // Add a small delay to allow session to load completely
    const timer = setTimeout(() => {
      console.log('Session found, fetching applications')
      fetchApplications()
    }, 500) // Increased delay to ensure session is fully loaded
    
    return () => clearTimeout(timer)
  }, [guestSession?.email, navigate]) // Only depend on email, not the isSessionValid function

  useEffect(() => {
    filterAndSortApplications()
  }, [applications, searchTerm, statusFilter, sortOrder])

  const fetchApplications = async () => {
    // Prevent multiple simultaneous calls
    if (isFetching) {
      console.log('Already fetching applications, skipping...')
      return
    }
    
    try {
      setIsFetching(true)
      setLoading(true)
      setError(null)
      
      // Get email from multiple sources
      let guestEmail = guestSession?.email || localStorage.getItem('guest_email')
      
      // Try to get email from parsed session if not found above
      if (!guestEmail) {
        const sessionData = localStorage.getItem('guest_session')
        if (sessionData) {
          try {
            const parsedSession = JSON.parse(sessionData)
            guestEmail = parsedSession.email
          } catch (error) {
            console.error('Error parsing session data:', error)
          }
        }
      }
      
      if (!guestEmail) {
        setError('No guest session found')
        return
      }
      
      console.log('Using guest email:', guestEmail)
      
      // Get access token from session data
      let accessToken = null
      const sessionData = localStorage.getItem('guest_session')
      if (sessionData) {
        try {
          const parsedSession = JSON.parse(sessionData)
          accessToken = parsedSession.accessToken
          console.log('Found access token:', accessToken ? 'Yes' : 'No')
        } catch (error) {
          console.error('Error parsing session for access token:', error)
        }
      }
      
      if (!accessToken) {
        setError('No valid access token found')
        setApplications([])
        return
      }
      
      // Fetch applications from the API
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://localhost:7249/api'
      
      console.log('Making API call to:', `${API_BASE_URL}/applications/guest/my-applications`)
      
      const response = await fetch(`${API_BASE_URL}/applications/guest/my-applications`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      })
      
      console.log('API Response status:', response.status)
      
      if (!response.ok) {
        if (response.status === 404) {
          // API endpoint not yet implemented - show informational message
          console.log('Applications list endpoint not yet implemented (404)')
          setError('Application list feature is currently under development. Please use the application status page to check individual applications.')
          setApplications([])
          return
        }
        if (response.status === 401) {
          // Authentication failed - for now, show empty state instead of erroring
          console.log('Authentication failed (401), showing empty state for guest')
          setApplications([])
          return
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('Fetched applications:', data)
      
      // Handle different response formats
      let applicationsArray = []
      if (Array.isArray(data)) {
        applicationsArray = data
      } else if (data.data && Array.isArray(data.data)) {
        applicationsArray = data.data
      } else if (data.applications && Array.isArray(data.applications)) {
        applicationsArray = data.applications
      }
      
      // Transform the API response to match our interface
      const transformedApplications: GuestApplication[] = applicationsArray.map((app: any) => ({
        id: app.id || app.applicationId,
        applicationNumber: app.applicationNumber || app.referenceNumber || `APP-${app.id}`,
        applicationType: app.position || app.applicationType || 'Professional Registration',
        status: app.status || 'SUBMITTED',
        submissionDate: app.submittedAt ? new Date(app.submittedAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        lastUpdated: app.updatedAt ? new Date(app.updatedAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        description: `${app.firstName || ''} ${app.lastName || ''} - ${app.position || 'Professional Registration'}`.trim(),
        estimatedCompletion: app.estimatedCompletion || null
      }))
      
      setApplications(transformedApplications)
      
    } catch (err: any) {
      console.error('Error fetching applications:', err)
      setError(`Failed to load applications: ${err.message}`)
      // Fall back to empty array instead of showing mock data
      setApplications([])
    } finally {
      setLoading(false)
      setIsFetching(false)
    }
  }

  const filterAndSortApplications = () => {
    let filtered = applications

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(app => 
        app.applicationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.applicationType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter)
    }

    // Sort by submission date
    filtered.sort((a, b) => {
      const dateA = new Date(a.submissionDate).getTime()
      const dateB = new Date(b.submissionDate).getTime()
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
    })

    setFilteredApplications(filtered)
  }

  const handleLogout = () => {
    clearGuestSession()
    navigate('/login')
  }

  const getStatusCounts = () => {
    return applications.reduce((counts, app) => {
      counts[app.status] = (counts[app.status] || 0) + 1
      counts.total = (counts.total || 0) + 1
      return counts
    }, {} as Record<string, number>)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your applications...</p>
        </div>
      </div>
    )
  }

  const statusCounts = getStatusCounts()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Logo size="sm" showText />
              <div className="hidden sm:block h-6 w-px bg-gray-300" />
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold text-gray-900">Guest Dashboard</h1>
                <p className="text-sm text-gray-500">{guestSession?.email}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span className="hidden sm:inline">Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome back!
          </h2>
          <p className="text-gray-600">
            Manage your applications and track their progress all in one place.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">{statusCounts.total || 0}</p>
                  <p className="text-sm text-gray-600">Total Applications</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {(statusCounts.APPROVED || 0) + (statusCounts.FINAL_APPROVE || 0)}
                  </p>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {(statusCounts.UNDER_REVIEW || 0) + (statusCounts.PENDING || 0) + (statusCounts.SUBMITTED || 0)}
                  </p>
                  <p className="text-sm text-gray-600">In Progress</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-8 w-8 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">{statusCounts.PAYMENT_PENDING || 0}</p>
                  <p className="text-sm text-gray-600">Action Required</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Bar */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">Quick Actions</h3>
              <p className="text-sm text-gray-600">Create applications or check existing application status</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
              <Link to="/application/new" className="flex-1 sm:flex-initial">
                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Create New Application
                </Button>
              </Link>
              <Link to="/status" className="flex-1 sm:flex-initial">
                <Button variant="outline" className="w-full">
                  <Search className="w-4 h-4 mr-2" />
                  Check Status
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>My Applications</span>
            </CardTitle>
            <CardDescription>
              Track and manage all your submitted applications
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search applications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="PENDING">Pending</option>
                <option value="UNDER_REVIEW">Under Review</option>
                <option value="APPROVED">Approved</option>
                <option value="PAYMENT_PENDING">Payment Pending</option>
                <option value="REJECTED">Rejected</option>
              </select>

              {/* Sort Order */}
              <Button
                variant="outline"
                onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                className="flex items-center space-x-2"
              >
                <ArrowUpDown className="w-4 h-4" />
                <span>{sortOrder === 'desc' ? 'Newest' : 'Oldest'}</span>
              </Button>
            </div>

            {/* Applications List */}
            {error ? (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Applications List Under Development</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">{error}</p>
                <div className="space-y-3">
                  <Link to="/status">
                    <Button className="w-full sm:w-auto">
                      <Search className="w-4 h-4 mr-2" />
                      Check Application Status
                    </Button>
                  </Link>
                  <div className="text-sm text-gray-600">
                    <p>In the meantime, you can:</p>
                    <ul className="mt-2 space-y-1">
                      <li>• Use the Application Status page to check individual applications</li>
                      <li>• Create new applications which will be saved to your account</li>
                      <li>• Check back later as we're implementing this feature</li>
                    </ul>
                  </div>
                </div>
              </div>
            ) : filteredApplications.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' ? 'No applications found' : 'No applications yet'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filter criteria'
                    : 'Get started by creating your first application'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <Link to="/application/new">
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Create New Application
                    </Button>
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredApplications.map((application) => (
                  <div
                    key={application.id}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow bg-white"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-semibold text-gray-900">
                            {application.applicationNumber}
                          </h4>
                          <StatusBadge status={application.status} />
                        </div>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600">
                          <div>
                            <p><span className="font-medium">Type:</span> {application.applicationType}</p>
                            <p><span className="font-medium">Submitted:</span> {new Date(application.submissionDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p><span className="font-medium">Last Updated:</span> {new Date(application.lastUpdated).toLocaleDateString()}</p>
                            {application.estimatedCompletion && (
                              <p><span className="font-medium">Est. Completion:</span> {new Date(application.estimatedCompletion).toLocaleDateString()}</p>
                            )}
                          </div>
                        </div>
                        
                        {application.description && (
                          <p className="text-sm text-gray-600 mt-2">{application.description}</p>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <Link to={`/application/view/${application.applicationNumber}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </Button>
                        </Link>
                        
                        {application.status === 'APPROVED' && (
                          <Button variant="outline" size="sm" className="text-green-600 border-green-200 hover:bg-green-50">
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default GuestDashboard
