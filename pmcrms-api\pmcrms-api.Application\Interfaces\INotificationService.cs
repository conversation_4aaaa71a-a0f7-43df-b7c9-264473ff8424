using pmcrms_api.Domain.Entities;
using DomainEntities = pmcrms_api.Domain.Entities;

namespace pmcrms_api.Application.Interfaces
{
    public interface INotificationService
    {
        Task NotifyApplicationSubmissionAsync(DomainEntities.Application application);
        Task NotifyAppointmentScheduledAsync(DomainEntities.Application application, DateTime appointmentDateTime);
        Task NotifyDocumentVerificationAsync(DomainEntities.Application application, bool isVerified, string? remarks = null);
        Task NotifyPaymentCompletionAsync(DomainEntities.Application application, Transaction transaction);
        Task NotifyCertificateIssuanceAsync(DomainEntities.Application application, byte[] certificatePdf);
    }
}
