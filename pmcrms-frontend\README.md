# PMCRMS Frontend

Professional Municipal Corporation Registration Management System - Frontend Application

## Overview

A modern, responsive web application for managing professional registrations of architects, engineers, and supervisors in municipal corporation projects.

## Features

- **Multi-role Support**: Different interfaces for public users, various engineer levels, clerks, and administrators
- **Real-time Updates**: WebSocket integration for live status updates
- **Digital Signatures**: HSM-based digital signature integration
- **Multi-language Support**: English and Marathi language support
- **Mobile Responsive**: Optimized for mobile devices
- **Progressive Web App**: PWA capabilities for better user experience

## Technology Stack

- **Framework**: React 18+ with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand
- **Routing**: React Router v6
- **Form Handling**: React Hook Form with Zod validation
- **HTTP Client**: Axios
- **Build Tool**: Vite
- **Testing**: Jest + React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pmcrms-frontend
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Update the API URL in `.env`:
```
VITE_API_URL=https://localhost:7243/api
```

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Building for Production

```bash
npm run build
```

### Testing

Run tests:
```bash
npm test
```

Run tests with coverage:
```bash
npm run test:coverage
```

## Project Structure

```
src/
├── components/          # Reusable components
│   ├── ui/             # Basic UI components (shadcn/ui)
│   ├── forms/          # Form components
│   ├── dashboard/      # Dashboard-specific components
│   └── common/         # Common components
├── pages/              # Page components
│   ├── auth/          # Authentication pages
│   ├── application/   # Application-related pages
│   └── dashboard/     # Dashboard pages
├── lib/               # Utilities and configurations
├── hooks/             # Custom React hooks
├── store/             # State management (Zustand)
├── types/             # TypeScript type definitions
└── assets/            # Static assets
```

## User Roles

### Public Users
- Application submission
- Status tracking
- Certificate download

### Officers
- **Junior Engineers**: Document verification, initial reviews
- **Assistant Engineers**: Application reviews, recommendations
- **Executive Engineers**: Final reviews, digital signatures
- **City Engineers**: Final approvals, system oversight
- **Clerks**: Post-payment processing
- **Admins**: System administration

## Workflow States

1. Document Verification Pending
2. Junior Engineer Pending
3. Assistant Engineer Pending
4. Executive Engineer Pending
5. City Engineer Pending
6. Payment Pending
7. Clerk Pending
8. Executive Digital Signature Pending
9. City Digital Signature Pending
10. Final Approve
11. Rejected

## API Integration

The frontend communicates with the .NET Core Web API backend:

- **Base URL**: Configured via `VITE_API_URL` environment variable
- **Authentication**: JWT token-based authentication
- **File Upload**: Multipart form data for document uploads
- **Real-time**: WebSocket connections for live updates

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_URL` | Backend API URL | `https://localhost:7243/api` |
| `VITE_APP_NAME` | Application name | `PMCRMS` |
| `VITE_ENABLE_OTP_LOGIN` | Enable OTP login | `true` |
| `VITE_ENABLE_NOTIFICATIONS` | Enable notifications | `true` |

## Deployment

### Docker Deployment

1. Build the Docker image:
```bash
docker build -t pmcrms-frontend .
```

2. Run the container:
```bash
docker run -p 80:80 pmcrms-frontend
```

### Manual Deployment

1. Build the project:
```bash
npm run build
```

2. Deploy the `dist` folder to your web server

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for your changes
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team.
