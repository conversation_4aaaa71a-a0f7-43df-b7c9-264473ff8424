using pmcrms_api.Domain.Entities;

namespace pmcrms_api.Application.Interfaces
{
    public interface IPDFService
    {
        Task<byte[]> GenerateRecommendedFormAsync(pmcrms_api.Domain.Entities.Application application, string currentSignatory);
        Task<byte[]> GenerateCertificateAsync(pmcrms_api.Domain.Entities.Application application);
        Task<byte[]> GenerateChallanAsync(pmcrms_api.Domain.Entities.Application application, Payment payment);
        Task<byte[]> GenerateQRCodeAsync(string content);
    }
}
