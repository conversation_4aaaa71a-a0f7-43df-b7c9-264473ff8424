# PMCRMS Cube.js Analytics Setup Guide

## 📋 Overview
This guide helps you set up Cube.js for advanced analytics in the PMCRMS system.

## 🚀 Quick Setup

### 1. Install Cube.js CLI
```bash
npm install -g @cubejs-cli/cli
```

### 2. Create Cube.js Project
```bash
# Navigate to your project root
cd d:\Pmcrms_Application

# Create Cube.js project
npx cubejs-cli create pmcrms-analytics -d mssql

# Navigate to the project
cd pmcrms-analytics
```

### 3. Install Dependencies
```bash
npm install
npm install mssql@8.1.0  # Specific version for compatibility
```

### 4. Environment Configuration
Create a `.env` file in the `pmcrms-analytics` directory:

```env
# Database Configuration
CUBEJS_DB_TYPE=mssql
CUBEJS_DB_HOST=localhost
CUBEJS_DB_NAME=pmcrms_database
CUBEJS_DB_USER=sa
CUBEJS_DB_PASS=your_password_here
CUBEJS_DB_PORT=1433

# Cube.js Configuration
CUBEJS_API_SECRET=your-secret-key-here-replace-with-strong-key
CUBEJS_DEV_MODE=true
CUBEJS_WEB_SOCKETS=true
PORT=4000

# Security Settings
CUBEJS_CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://localhost:7249
```

### 5. Copy Schema Files
Copy the schema files from this directory to your `pmcrms-analytics/schema/` directory:
- Applications.js
- Users.js
- WorkflowHistory.js

### 6. Start Development Server
```bash
npm run dev
```

Cube.js will be available at: `http://localhost:4000`

## 🔧 Schema Files Included

1. **Applications.js** - Main analytics cube for applications
2. **Users.js** - User performance analytics
3. **WorkflowHistory.js** - Workflow transition tracking

## 🌐 Frontend Integration

Add to your frontend `.env` file:
```env
REACT_APP_CUBEJS_TOKEN=your-cube-token-here
REACT_APP_CUBEJS_API_URL=http://localhost:4000/cubejs-api/v1
```

## 🔍 Testing the Setup

1. **Check Cube.js is running:**
   ```bash
   curl http://localhost:4000/cubejs-api/v1/meta
   ```

2. **Test Applications cube:**
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
   "http://localhost:4000/cubejs-api/v1/load?query={\"measures\":[\"Applications.count\"],\"dimensions\":[\"Applications.position\"]}"
   ```

## 📊 Available Metrics

### Applications Cube
- `Applications.count` - Total applications count
- `Applications.averageProcessingTime` - Average processing days
- `Applications.position` - Position dimension
- `Applications.status` - Status dimension
- `Applications.submittedDate` - Time dimension

### Users Cube
- `Users.count` - Total users count
- `Users.activeCount` - Active users count
- `Users.role` - Role dimension
- `Users.createdDate` - Time dimension

### Workflow History Cube
- `WorkflowHistory.transitionCount` - Total transitions
- `WorkflowHistory.averageStageTime` - Average time per stage
- `WorkflowHistory.fromStatus` - From status dimension
- `WorkflowHistory.toStatus` - To status dimension

## 🎯 Production Deployment

1. Set `CUBEJS_DEV_MODE=false`
2. Use a strong `CUBEJS_API_SECRET`
3. Configure proper CORS origins
4. Set up SSL/HTTPS
5. Use PM2 or similar for process management

## 📈 Performance Tips

1. **Pre-aggregations** are already configured for better performance
2. **Refresh keys** are set to refresh data every hour
3. **Indexes** should be created on your database for optimal performance

## 🔐 Security Notes

1. Never expose your `CUBEJS_API_SECRET`
2. Configure CORS properly for production
3. Use HTTPS in production
4. Implement proper authentication tokens

## 🐛 Troubleshooting

### Connection Issues:
- Check SQL Server is running and accessible
- Verify database credentials
- Ensure port 1433 is open

### Schema Compilation Errors:
- Check schema syntax
- Verify table/column names match your database
- Check data types compatibility

### Performance Issues:
- Add database indexes
- Configure pre-aggregations
- Optimize SQL queries

## 📞 Support

If you encounter issues:
1. Check Cube.js logs for error details
2. Verify database connection
3. Test schema compilation
4. Check network connectivity
