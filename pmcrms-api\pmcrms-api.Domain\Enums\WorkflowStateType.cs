namespace pmcrms_api.Domain.Enums;

/// <summary>
/// Workflow states as per PromptFile5.pdf - Complete application lifecycle
/// </summary>
public enum WorkflowStateType
{
    /// <summary>
    /// Application is submitted by user and routed to appropriate Junior Engineer based on position
    /// Junior Engineer's initial task is to schedule an appointment
    /// </summary>
    DOCUMENT_VERIFICATION_PENDING = 1,

    /// <summary>
    /// Junior Engineer schedules appointment and performs first document verification offline
    /// They confirm documents and apply digital signature to Recommended Form PDF
    /// </summary>
    JUNIOR_ENGINEER_PENDING = 2,

    /// <summary>
    /// Application moves to respective Assistant Engineer (based on position type) for approval
    /// Assistant Engineer reviews and approves, applying digital signature to Recommended Form PDF
    /// </summary>
    ASSISTANT_ENGINEER_PENDING = 3,

    /// <summary>
    /// Application sent to Executive Engineer for review and approval
    /// Executive Engineer applies digital signature to Recommended Form PDF
    /// </summary>
    EXECUTIVE_ENGINEER_PENDING = 4,

    /// <summary>
    /// City Engineer performs initial review and approval task
    /// Applies digital signature to Recommended Form PDF
    /// </summary>
    CITY_ENGINEER_PENDING = 5,

    /// <summary>
    /// Application status updated to indicate payment is required
    /// User redirected to payment gateway (Easebuzz)
    /// Upon successful payment, Certificate and Challan PDFs are generated
    /// </summary>
    PAYMENT_PENDING = 6,

    /// <summary>
    /// After successful payment, application goes to Clerk for approval
    /// Clerk approves and status update email is sent to user
    /// </summary>
    CLERK_PENDING = 7,

    /// <summary>
    /// Executive Engineer performs second task - applying digital signature to Certificate PDF
    /// Uses HSM system for digital signature
    /// </summary>
    EXECUTIVE_DIGITAL_SIGNATURE_PENDING = 8,

    /// <summary>
    /// City Engineer applies final digital signature to Certificate PDF
    /// </summary>
    CITY_DIGITAL_SIGNATURE_PENDING = 9,

    /// <summary>
    /// Certificate is finalized and issued
    /// User receives notification email and can download certificate using Guest login
    /// </summary>
    FINAL_APPROVE = 10,

    /// <summary>
    /// Application rejected at any stage
    /// </summary>
    REJECTED = 11
}
