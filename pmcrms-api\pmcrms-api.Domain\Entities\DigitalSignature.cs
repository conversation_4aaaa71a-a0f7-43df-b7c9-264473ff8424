using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

public class DigitalSignature
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(255)]
    public string SignatureData { get; set; } = string.Empty;

    public DateTime SignedAt { get; set; }

    [Required]
    [StringLength(50)]
    public string SignatureType { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Notes { get; set; }

    // Navigation properties
    public int DocumentId { get; set; }
    public Document Document { get; set; } = null!;

    public int UserId { get; set; }
    public User User { get; set; } = null!;
}
