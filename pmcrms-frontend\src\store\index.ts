import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, LoginResponse } from '../types/types'
import { api } from '../lib/api'

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (credentials: { email: string; password: string }) => Promise<void>
  loginWithOTP: (credentials: { phoneNumber: string; otp: string }) => Promise<void>
  logout: () => void
  setUser: (user: User) => void
  setTokens: (tokens: { token: string; refreshToken: string }) => void
  clearError: () => void
  initialize: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials) => {
        set({ isLoading: true, error: null })
        try {
          // Determine login endpoint based on email domain or user type
          let loginEndpoint = '/auth/guest/login' // default
          
          if (credentials.email.includes('admin') || credentials.email.endsWith('.gov.in')) {
            loginEndpoint = '/auth/admin/login'
          } else if (credentials.email.includes('officer')) {
            loginEndpoint = '/auth/officer/login'
          }
          
          // Make real API call to appropriate login endpoint
          const response = await api.post<any>(loginEndpoint, credentials)
          
          console.log('Login API Response:', response) // Debug log
          console.log('Response data:', JSON.stringify(response.data, null, 2)) // Detailed response log
          
          // Handle response data structure - use the full response, not just response.data
          const responseData = response  // Use the full response object
          
          console.log('Processed responseData:', JSON.stringify(responseData, null, 2)) // Debug processed data
          console.log('responseData.success:', responseData.success)
          console.log('Type of responseData.success:', typeof responseData.success)
          
          // Check for success 
          const isSuccess = responseData.success === true
          
          if (!isSuccess) {
            console.error('Login failed - response not successful:', responseData)
            console.error('Available properties:', Object.keys(responseData))
            throw new Error(responseData.message || 'Login failed')
          }
          
          let user, token, refreshToken
          
          // Handle different response structures based on endpoint
          if (loginEndpoint === '/auth/admin/login') {
            // Admin login response structure: { success: true, data: { token, user }, message }
            const adminData = responseData.data
            const userData = adminData.user
            user = {
              id: userData.id,
              email: userData.email,
              firstName: userData.firstName,
              lastName: userData.lastName,
              role: userData.role
            }
            token = adminData.token
            refreshToken = adminData.refreshToken || null
          } else {
            // Standard login response structure: { success: true, data: { user, token, refreshToken } }
            const data = responseData.data
            user = data.user
            token = data.token
            refreshToken = data.refreshToken || null
          }
          
          if (!user || !token) {
            throw new Error('Invalid response: missing user or token')
          }
          
          // Store tokens in localStorage
          localStorage.setItem('auth_token', token)
          if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken)
          }
          
          set({
            user: user,
            token: token,
            refreshToken: refreshToken || null,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error: any) {
          console.error('Login error:', error)
          set({
            error: error.response?.data?.message || error.message || 'Login failed. Please check your credentials.',
            isLoading: false,
            isAuthenticated: false,
          })
          throw error
        }
      },

      loginWithOTP: async (credentials) => {
        set({ isLoading: true, error: null })
        try {
          // Make real API call to OTP login endpoint
          const response = await api.post<LoginResponse>('/auth/login-otp', credentials)
          
          console.log('OTP Login API Response:', response) // Debug log
          
          // Handle response data structure
          const responseData = response.data || response
          
          if (!responseData.success) {
            throw new Error(responseData.message || 'OTP verification failed')
          }
          
          const { user, token, refreshToken } = responseData.data
          
          if (!user || !token) {
            throw new Error('Invalid response: missing user or token')
          }
          
          // Store tokens in localStorage
          localStorage.setItem('auth_token', token)
          if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken)
          }
          
          set({
            user: user,
            token: token,
            refreshToken: refreshToken || null,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error: any) {
          console.error('OTP Login error:', error)
          set({
            error: error.response?.data?.message || error.message || 'OTP verification failed. Please try again.',
            isLoading: false,
            isAuthenticated: false,
          })
          throw error
        }
      },

      logout: () => {
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          error: null,
        })
      },

      setUser: (user) => set({ user }),

      setTokens: (tokens) => {
        localStorage.setItem('auth_token', tokens.token)
        localStorage.setItem('refresh_token', tokens.refreshToken)
        set({
          token: tokens.token,
          refreshToken: tokens.refreshToken,
        })
      },

      clearError: () => set({ error: null }),
      
      // Initialize auth state (restore from persisted state)
      initialize: () => {
        const state = get()
        // Validate that if we have a user and token, we should be authenticated
        if (state.user && state.token) {
          set({ isAuthenticated: true })
        } else {
          set({ isAuthenticated: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Application Store
interface ApplicationState {
  currentApplication: any | null
  applications: any[]
  isLoading: boolean
  error: string | null
  filters: {
    status: string[]
    positionType: string[]
    dateRange: [Date | null, Date | null]
    searchQuery: string
  }
  
  // Actions
  setCurrentApplication: (application: any) => void
  setApplications: (applications: any[]) => void
  addApplication: (application: any) => void
  updateApplication: (id: string, updates: any) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setFilters: (filters: any) => void
  clearError: () => void
}

export const useApplicationStore = create<ApplicationState>((set) => ({
  currentApplication: null,
  applications: [],
  isLoading: false,
  error: null,
  filters: {
    status: [],
    positionType: [],
    dateRange: [null, null],
    searchQuery: '',
  },

  setCurrentApplication: (application) => set({ currentApplication: application }),
  
  setApplications: (applications) => set({ applications }),
  
  addApplication: (application) => 
    set((state) => ({ applications: [...state.applications, application] })),
  
  updateApplication: (id, updates) =>
    set((state) => ({
      applications: state.applications.map(app => 
        app.id === id ? { ...app, ...updates } : app
      ),
      currentApplication: state.currentApplication?.id === id 
        ? { ...state.currentApplication, ...updates }
        : state.currentApplication
    })),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error }),
  
  setFilters: (filters) => set((state) => ({ 
    filters: { ...state.filters, ...filters }
  })),
  
  clearError: () => set({ error: null }),
}))

// Notification Store
interface NotificationState {
  notifications: any[]
  unreadCount: number
  
  // Actions
  addNotification: (notification: any) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAll: () => void
}

export const useNotificationStore = create<NotificationState>((set) => ({
  notifications: [],
  unreadCount: 0,

  addNotification: (notification) =>
    set((state) => ({
      notifications: [notification, ...state.notifications],
      unreadCount: state.unreadCount + 1,
    })),

  markAsRead: (id) =>
    set((state) => ({
      notifications: state.notifications.map(n =>
        n.id === id ? { ...n, isRead: true } : n
      ),
      unreadCount: Math.max(0, state.unreadCount - 1),
    })),

  markAllAsRead: () =>
    set((state) => ({
      notifications: state.notifications.map(n => ({ ...n, isRead: true })),
      unreadCount: 0,
    })),

  removeNotification: (id) =>
    set((state) => ({
      notifications: state.notifications.filter(n => n.id !== id),
      unreadCount: state.notifications.find(n => n.id === id && !n.isRead)
        ? state.unreadCount - 1
        : state.unreadCount,
    })),

  clearAll: () => set({ notifications: [], unreadCount: 0 }),
}))
