using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

/// <summary>
/// Address Entity for LocalAddress [642fb55eb101005f73b02bd7] and PermanentAddress [642fb617b101005f73b02bd8] 
/// Collections as per PromptFile5.pdf
/// </summary>
public class Address
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string FlatHouseNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string StreetAddress { get; set; } = string.Empty;

    [Required]
    [StringLength(300)]
    public string AddressLine { get; set; } = string.Empty;

    [StringLength(300)]
    public string? AddressLine2 { get; set; }

    [Required]
    [StringLength(100)]
    public string Country { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string State { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string City { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string PostalCode { get; set; } = string.Empty;

    [Required]
    public bool IsLocal { get; set; } // true for LocalAddress, false for PermanentAddress

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;
}
