# PMCRMS Backend - Final Implementation Requirements

## 🎯 Overview
This document provides the complete backend requirements to support the PMCRMS Admin Dashboard with advanced analytics, drill-down reporting, and real-time data visualization using Cube.js.

## 📋 Implementation Checklist

### Phase 1: Core Admin APIs (Required for Basic Functionality)
- [ ] **Admin Authentication System**
- [ ] **Applications Management APIs**  
- [ ] **Dashboard Statistics API**
- [ ] **User Management APIs**

### Phase 2: Analytics & Reporting (Required for Advanced Features)
- [ ] **Cube.js Server Setup**
- [ ] **Database Schema Optimization**
- [ ] **Analytics APIs**
- [ ] **Real-time Data Sync**

### Phase 3: Advanced Features (Optional Enhancements)
- [ ] **Document Management APIs**
- [ ] **Workflow Management APIs**
- [ ] **Export & Reporting APIs**
- [ ] **System Health Monitoring**

---

## 🔐 1. Authentication System

### Admin Login Endpoint
```http
POST /api/auth/admin/login
Content-Type: application/json

Request:
{
  "email": "<EMAIL>",
  "password": "Admin@123!"
}

Response (CRITICAL - Must match this structure):
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "Admin",
      "firstName": "System",
      "lastName": "Administrator"
    }
  },
  "message": "Login successful"
}
```

### Token Verification
```http
GET /api/auth/admin/verify
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "Admin"
    }
  }
}
```

---

## 📊 2. Dashboard Statistics API

### Dashboard Data Endpoint (HIGH PRIORITY)
```http
GET /api/Reports/dashboard
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "totalApplications": 1250,
    "pending": 145,
    "approved": 980,
    "rejected": 125,
    "inProgress": 100,
    "monthlyTrend": [120, 135, 98, 156, 142, 180, 165],
    "positionBreakdown": {
      "Architect": 450,
      "License Engineer": 380,
      "Structural Engineer": 200,
      "Supervisor1": 120,
      "Supervisor2": 100
    },
    "recentApplications": [
      {
        "id": "1",
        "applicationNumber": "PMC-2025-0150",
        "applicantName": "John Doe",
        "position": "Architect",
        "status": "DOCUMENT_VERIFICATION_PENDING",
        "submittedDate": "2025-08-20T10:30:00Z",
        "assignedTo": "Jane Smith"
      }
    ],
    "systemHealth": {
      "status": "Good",
      "uptime": "99.9%",
      "lastBackup": "2025-08-25T02:00:00Z"
    }
  }
}
```

---

## 📄 3. Applications Management APIs

### Get Applications with Advanced Filtering
```http
GET /api/Applications?page=1&limit=20&status=DOCUMENT_VERIFICATION_PENDING&position=Architect&dateFrom=2025-01-01&dateTo=2025-08-25&search=john&assignedTo=5
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": [
    {
      "id": "1",
      "applicationNumber": "PMC-2025-0150",
      "applicantName": "John Doe",
      "position": "Architect",
      "status": "DOCUMENT_VERIFICATION_PENDING",
      "submittedDate": "2025-08-20T10:30:00Z",
      "assignedTo": "Jane Smith",
      "lastUpdated": "2025-08-22T15:45:00Z",
      "phoneNumber": "**********",
      "email": "<EMAIL>",
      "experience": "5 years",
      "currentStage": "Document Review",
      "nextAction": "Verify documents",
      "documentsCount": 8,
      "verifiedDocuments": 6,
      "processingDays": 5
    }
  ],
  "totalCount": 1250,
  "pageNumber": 1,
  "pageSize": 20,
  "totalPages": 63,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

### Update Application Status & Assignment
```http
PUT /api/Applications/{id}/status
Authorization: Bearer {token}

Request:
{
  "status": "JUNIOR_ENGINEER_PENDING",
  "remarks": "Documents verified successfully", 
  "assignedToUserId": 5
}

Response:
{
  "success": true,
  "data": {
    "id": "1",
    "status": "JUNIOR_ENGINEER_PENDING",
    "assignedTo": "Junior Engineer Name",
    "lastUpdated": "2025-08-25T14:30:00Z"
  },
  "message": "Application status updated successfully"
}
```

---

## 👥 4. User Management APIs

### Get All Users/Officers
```http
GET /api/Users?role=JUNIOR_ENGINEER&status=active&search=john&page=1&limit=50
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": [
    {
      "id": 5,
      "firstName": "Jane",
      "lastName": "Smith", 
      "email": "<EMAIL>",
      "phoneNumber": "**********",
      "role": "JUNIOR_ENGINEER",
      "isActive": true,
      "createdDate": "2025-01-15T10:00:00Z",
      "lastLogin": "2025-08-25T09:30:00Z",
      "department": "Technical Review",
      "assignedApplicationsCount": 12
    }
  ],
  "totalCount": 45
}
```

### Get All Roles
```http
GET /api/Roles
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "ADMIN",
      "displayName": "System Administrator"
    },
    {
      "id": 2,
      "name": "JUNIOR_ENGINEER",
      "displayName": "Junior Engineer"
    },
    {
      "id": 3,
      "name": "ASSISTANT_ENGINEER", 
      "displayName": "Assistant Engineer"
    },
    {
      "id": 4,
      "name": "EXECUTIVE_ENGINEER",
      "displayName": "Executive Engineer"
    },
    {
      "id": 5,
      "name": "CITY_ENGINEER",
      "displayName": "City Engineer"
    },
    {
      "id": 6,
      "name": "CLERK",
      "displayName": "Clerk"
    }
  ]
}
```

---

## 📈 5. Cube.js Analytics Setup (CRITICAL for Advanced Reports)

### Installation & Setup
```bash
# 1. Create Cube.js project
npx cubejs-cli create pmcrms-analytics -d mssql
cd pmcrms-analytics

# 2. Install dependencies
npm install

# 3. Configure environment
cp .env.example .env
```

### Environment Configuration (.env)
```env
CUBEJS_DB_TYPE=mssql
CUBEJS_DB_HOST=your-sql-server-host
CUBEJS_DB_NAME=pmcrms_database
CUBEJS_DB_USER=your-username
CUBEJS_DB_PASS=your-password
CUBEJS_DB_PORT=1433
CUBEJS_API_SECRET=your-secret-key-here
CUBEJS_DEV_MODE=true
CUBEJS_WEB_SOCKETS=true
PORT=4000
```

### Required Schema Files
Copy these files to `schema/` directory in your Cube.js project:

1. **`Applications.js`** (Main analytics cube)
2. **`Users.js`** (User performance analytics)
3. **`WorkflowHistory.js`** (Workflow transition analytics)

### Start Cube.js Server
```bash
npm run dev
```

Cube.js will be available at: `http://localhost:4000`

---

## 🗄️ 6. Database Schema Requirements

### Applications Table (Enhanced)
```sql
CREATE TABLE Applications (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ApplicationNumber NVARCHAR(50) UNIQUE NOT NULL,
    ApplicantName NVARCHAR(200) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    PhoneNumber NVARCHAR(20) NOT NULL,
    Position NVARCHAR(50) NOT NULL, -- Architect, License Engineer, etc.
    Status NVARCHAR(50) NOT NULL, -- WorkflowState values
    SubmittedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastUpdated DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    AssignedToUserId INT NULL,
    AssignedTo NVARCHAR(200) NULL,
    Experience NVARCHAR(100),
    Qualification NVARCHAR(200),
    RegistrationNumber NVARCHAR(100),
    Address NVARCHAR(500),
    DateOfBirth DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(100),
    IsDeleted BIT NOT NULL DEFAULT 0,
    
    -- Add indexes for analytics performance
    INDEX IX_Applications_Status (Status),
    INDEX IX_Applications_Position (Position),
    INDEX IX_Applications_SubmittedDate (SubmittedDate),
    INDEX IX_Applications_AssignedTo (AssignedToUserId),
    INDEX IX_Applications_Composite (Status, Position, SubmittedDate)
);
```

### Users Table (Enhanced)
```sql
CREATE TABLE Users (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) UNIQUE NOT NULL,
    PhoneNumber NVARCHAR(20),
    PasswordHash NVARCHAR(255) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastLogin DATETIME2 NULL,
    Department NVARCHAR(100),
    PasswordMustChange BIT NOT NULL DEFAULT 0,
    
    -- Add indexes for analytics
    INDEX IX_Users_Role (Role),
    INDEX IX_Users_IsActive (IsActive),
    INDEX IX_Users_CreatedDate (CreatedDate)
);
```

### Workflow History Table (Required for Analytics)
```sql
CREATE TABLE ApplicationWorkflowHistory (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ApplicationId UNIQUEIDENTIFIER NOT NULL,
    FromStatus NVARCHAR(50) NULL,
    ToStatus NVARCHAR(50) NOT NULL,
    ChangedBy NVARCHAR(200) NOT NULL,
    ChangedByUserId INT NULL,
    ChangedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    Remarks NVARCHAR(1000),
    IsAutomaticTransition BIT NOT NULL DEFAULT 0,
    
    FOREIGN KEY (ApplicationId) REFERENCES Applications(Id),
    FOREIGN KEY (ChangedByUserId) REFERENCES Users(Id),
    
    -- Analytics indexes
    INDEX IX_WorkflowHistory_ApplicationId (ApplicationId),
    INDEX IX_WorkflowHistory_Status (ToStatus),
    INDEX IX_WorkflowHistory_ChangedDate (ChangedDate)
);
```

---

## 🔄 7. Workflow States (Must Use These Exact Values)

### Required Status Values:
```typescript
enum WorkflowState {
  DOCUMENT_VERIFICATION_PENDING = 'DOCUMENT_VERIFICATION_PENDING',
  JUNIOR_ENGINEER_PENDING = 'JUNIOR_ENGINEER_PENDING', 
  ASSISTANT_ENGINEER_PENDING = 'ASSISTANT_ENGINEER_PENDING',
  EXECUTIVE_ENGINEER_PENDING = 'EXECUTIVE_ENGINEER_PENDING',
  CITY_ENGINEER_PENDING = 'CITY_ENGINEER_PENDING',
  PAYMENT_PENDING = 'PAYMENT_PENDING',
  FINAL_APPROVE = 'FINAL_APPROVE',
  REJECTED = 'REJECTED'
}
```

### Required Position Values:
```typescript
enum PositionType {
  ARCHITECT = 'Architect',
  LICENSE_ENGINEER = 'License Engineer',
  STRUCTURAL_ENGINEER = 'Structural Engineer', 
  SUPERVISOR1 = 'Supervisor1',
  SUPERVISOR2 = 'Supervisor2'
}
```

---

## 🌐 8. Frontend Environment Configuration

Add to your frontend `.env` file:
```env
# API Configuration
REACT_APP_API_URL=https://localhost:7249/api
REACT_APP_API_TIMEOUT=30000

# Cube.js Configuration  
REACT_APP_CUBEJS_TOKEN=your-cube-token-here
REACT_APP_CUBEJS_API_URL=http://localhost:4000/cubejs-api/v1

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_EXPORTS=true
REACT_APP_DEMO_MODE=false
```

---

## 🔧 9. Backend Server Configuration

### Required NuGet Packages
```xml
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.0" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
<PackageReference Include="Serilog.AspNetCore" Version="6.0.0" />
```

### CORS Configuration (Critical)
```csharp
// In Program.cs or Startup.cs
app.UseCors(policy => policy
    .WithOrigins("http://localhost:5173", "http://localhost:3000") // Vite dev server
    .AllowAnyMethod()
    .AllowAnyHeader()
    .AllowCredentials());
```

### JWT Configuration
```csharp
services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = "pmcrms-api",
            ValidAudience = "pmcrms-frontend",
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("your-secret-key"))
        };
    });
```

---

## 📊 10. Cube.js Schema Implementation

### Applications Cube (schema/Applications.js)
```javascript
cube(`Applications`, {
  sql: `
    SELECT 
      Id,
      ApplicationNumber,
      ApplicantName,
      Position,
      Status,
      SubmittedDate,
      LastUpdated,
      AssignedTo,
      AssignedToUserId,
      DATEDIFF(day, SubmittedDate, COALESCE(LastUpdated, GETUTCDATE())) as ProcessingDays
    FROM Applications 
    WHERE IsDeleted = 0
  `,

  measures: {
    count: {
      type: `count`,
      title: `Total Applications`
    },
    
    averageProcessingTime: {
      type: `avg`,
      sql: `ProcessingDays`,
      title: `Average Processing Time (Days)`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `string`,
      primaryKey: true
    },
    
    position: {
      sql: `Position`,
      type: `string`,
      title: `Position Type`
    },
    
    status: {
      sql: `Status`,
      type: `string`, 
      title: `Workflow Status`
    },
    
    submittedDate: {
      sql: `SubmittedDate`,
      type: `time`,
      title: `Submitted Date`
    }
  },

  preAggregations: {
    main: {
      measures: [CUBE.count],
      dimensions: [CUBE.position, CUBE.status],
      timeDimension: CUBE.submittedDate,
      granularity: `day`,
      refreshKey: {
        every: `1 hour`
      }
    }
  }
});
```

---

## 🔍 11. API Endpoints Summary

### Priority 1 (Essential for Basic Admin Functions)
```
✅ POST /api/auth/admin/login          - Admin authentication
❌ GET  /api/Reports/dashboard         - Dashboard statistics
❌ GET  /api/Applications              - Applications list with filters
❌ PUT  /api/Applications/{id}/status  - Update application status
❌ GET  /api/Users                     - Users list
❌ GET  /api/Roles                     - Available roles
```

### Priority 2 (Required for Analytics)
```
❌ GET  /api/Applications/{id}                    - Single application details
❌ GET  /api/Applications/{id}/history            - Workflow history
❌ POST /api/Applications/{id}/assign             - Assign to officer
❌ POST /api/Applications/bulk-update             - Bulk operations
❌ Cube.js Server Setup                           - Analytics backend
```

### Priority 3 (Advanced Features)
```
❌ GET  /api/Applications/{id}/documents          - Document management
❌ GET  /api/Documents/{id}/download              - File downloads
❌ GET  /api/Reports/export                       - Data export
❌ GET  /api/System/health                        - System monitoring
❌ POST /api/Users                                - Create users
```

---

## 🚀 12. Deployment Architecture

### Development Setup
```
Frontend (Vite) → Port 5173
Backend (ASP.NET) → Port 7249  
Cube.js Server → Port 4000
SQL Server → Port 1433
```

### Production Recommendations
```
Frontend → IIS/Nginx
Backend → IIS/Docker
Cube.js → PM2/Docker
Database → SQL Server cluster
```

---

## 📱 13. Frontend Features Ready for Backend

### ✅ Implemented & Working:
- **Admin Authentication Flow** - Login, logout, token management
- **Admin Dashboard** - Statistics cards, charts, navigation
- **Applications Management** - List, filter, search, pagination
- **Drill-down Reports** - Position pie charts, workflow stage charts
- **Interactive Analytics** - Click to drill down, export data
- **Professional UI** - Responsive design, loading states, error handling
- **Mock Data Fallbacks** - UI works without backend for testing

### 🔄 Waiting for Backend:
- **Real Data Display** - Switch from mock to actual database data
- **CRUD Operations** - Create, update, delete applications/users
- **Advanced Analytics** - Cube.js powered reporting
- **File Management** - Document upload/download functionality
- **Export Features** - PDF/Excel export with real data

---

## 🧪 14. Testing Strategy

### Frontend Testing (Available Now)
```bash
# Start frontend in demo mode
npm run dev

# Test admin login with demo credentials
Email: <EMAIL>
Password: Admin@123!

# Navigate to test all features:
/admin/dashboard     - Dashboard overview
/admin/applications  - Applications management  
/admin/reports      - Analytics & drill-down charts
```

### Backend Testing (After Implementation)
```bash
# Test authentication
curl -X POST https://localhost:7249/api/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin@123!"}'

# Test dashboard
curl -X GET https://localhost:7249/api/Reports/dashboard \
  -H "Authorization: Bearer {token}"

# Test Cube.js
curl -X GET http://localhost:4000/cubejs-api/v1/meta
```

---

## ⚡ 15. Implementation Priority & Timeline

### Week 1: Core Admin APIs
1. Fix admin login response structure
2. Implement dashboard statistics endpoint  
3. Implement applications list endpoint
4. Basic CRUD operations for applications

### Week 2: Analytics Setup
1. Set up Cube.js server
2. Implement schema files
3. Connect to SQL Server database
4. Test analytics queries

### Week 3: Advanced Features
1. User management APIs
2. Workflow management
3. Document handling
4. Export functionality

### Week 4: Production Deployment
1. Performance optimization
2. Security hardening
3. Production deployment
4. Load testing

---

## 🎯 16. Critical Success Factors

### Must-Have for Basic Functionality:
1. **Admin login must return exact response structure shown**
2. **Dashboard API must return all required statistics**
3. **Applications API must support all filtering parameters**
4. **Workflow states must use exact enum values**

### Must-Have for Analytics:
1. **Cube.js server must be running on port 4000**
2. **Database indexes must be created for performance**
3. **Schema files must be deployed correctly**
4. **CORS must allow frontend access**

### Performance Requirements:
- **API Response Time**: < 500ms for dashboard
- **Analytics Queries**: < 2 seconds for drill-down
- **File Operations**: < 5 seconds for exports
- **Database Queries**: Optimized with proper indexes

---

## 📞 17. Support & Validation

### Frontend Validation Points:
- Login redirects to `/admin/dashboard`
- Dashboard shows real statistics (not mock data)
- Applications page loads with real data
- Reports page shows interactive charts
- No 404 errors in browser console

### Backend Validation Points:
- All API endpoints return 200 status
- JWT tokens validate correctly
- Database queries execute efficiently
- Cube.js queries return results in < 2 seconds

### Integration Testing:
- Complete user workflow from login to application management
- Drill-down analytics work end-to-end
- Export functionality generates real data files
- Error handling works gracefully

---

## 🎉 Expected Results

Once implemented, the admin will have:

✅ **Complete Admin Dashboard** with real-time statistics
✅ **Interactive Analytics** with drill-down capabilities  
✅ **Position-wise Pie Charts** showing application distribution
✅ **Workflow Stage Analysis** with clickable drill-down
✅ **Advanced Filtering** by position, status, date, officer
✅ **Export Functionality** for filtered data sets
✅ **Real-time Updates** with refresh capabilities
✅ **Professional UI** with responsive design

The system will automatically detect when backend APIs are available and switch from demo mode to production mode with real database data!

---

## 📧 Questions or Issues?

If you encounter any issues during implementation:

1. **Check browser console** for API error details
2. **Verify CORS configuration** for cross-origin requests
3. **Test API endpoints** with Postman/curl first
4. **Check Cube.js logs** for schema compilation errors
5. **Validate database connections** and query performance

The frontend is production-ready and waiting for these backend implementations to provide a complete enterprise-grade admin system!
