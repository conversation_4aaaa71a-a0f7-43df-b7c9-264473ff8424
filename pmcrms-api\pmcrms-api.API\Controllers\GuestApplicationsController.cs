using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.DTOs.Applications;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using System.Net;
using System.Security.Claims;
using ApplicationEntity = pmcrms_api.Domain.Entities.Application;

namespace pmcrms_api.API.Controllers;

[ApiController]
[Route("api/applications/guest")]
[Tags("Guest Applications")]
public class GuestApplicationsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GuestApplicationsController> _logger;
    private readonly IApplicationRoutingService _routingService;

    public GuestApplicationsController(
        IUnitOfWork unitOfWork, 
        ILogger<GuestApplicationsController> logger,
        IApplicationRoutingService routingService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _routingService = routingService;
    }

    /// <summary>
    /// Get all applications for the authenticated guest user (Dashboard API)
    /// </summary>
    /// <returns>List of applications for the authenticated guest user</returns>
    [HttpGet("my-applications")]
    [Authorize(Roles = "Guest")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    public async Task<IActionResult> GetMyApplications()
    {
        try
        {
            // Get the current user's email from JWT token
            // Try multiple claim types as the JWT might use different claim names
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value ?? 
                           User.FindFirst("email")?.Value ?? 
                           User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress")?.Value;
            
            if (string.IsNullOrEmpty(userEmail))
            {
                // Log all available claims for debugging
                var availableClaims = User.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
                _logger.LogWarning("User email not found in token. Available claims: {Claims}", string.Join(", ", availableClaims));
                return BadRequest(new { success = false, message = "User email not found in token" });
            }

            _logger.LogInformation("Fetching applications for guest user: {Email}", userEmail);

            // Find all applications for this guest user
            var applications = await _unitOfWork.Repository<ApplicationEntity>()
                .FindAsync(a => a.EmailAddress == userEmail);

            var applicationsList = applications.ToList();

            var result = applicationsList.Select(app => new
            {
                id = app.Id.ToString(),
                applicationNumber = app.ApplicationNumber,
                position = app.Position.ToString(),
                firstName = app.FirstName,
                lastName = app.LastName,
                status = MapWorkflowStateToStatus(app.CurrentState),
                submittedAt = app.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                updatedAt = app.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ssZ"), // Use submission date as updated date for now
                estimatedCompletion = (string?)null // Can be calculated based on business rules
            }).ToList();

            return Ok(new
            {
                success = true,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching applications for guest user");
            return BadRequest(new
            {
                success = false,
                message = "Failed to fetch applications",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Test endpoint to validate form data binding
    /// </summary>
    [HttpPost("test-submission")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> TestSubmission([FromForm] GuestApplicationSubmitRequest request)
    {
        try
        {
            _logger.LogInformation("Test submission received for email: {Email}", request.EmailId);
            
            // Check model state validation
            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .ToDictionary(x => x.Key, x => x.Value?.Errors.Select(e => e.ErrorMessage).ToArray());
                
                return BadRequest(new { 
                    success = false,
                    message = "Model validation failed", 
                    errors = errors
                });
            }
            
            return Ok(new
            {
                success = true,
                message = "Form data received successfully",
                data = new
                {
                    email = request.EmailId,
                    firstName = request.FirstName,
                    lastName = request.LastName,
                    mobileNumber = request.MobileNumber,
                    position = request.Position,
                    hasLocalAddress = request.LocalAddress != null,
                    hasPermanentAddress = request.PermanentAddress != null,
                    hasQualification = request.Qualification != null,
                    experienceCount = request.Experiences?.Count ?? 0,
                    documentCount = request.Documents?.Count ?? 0
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in test submission");
            return BadRequest(new { 
                success = false,
                message = "Failed to process test submission", 
                error = ex.Message,
                details = ex.InnerException?.Message,
                stackTrace = ex.StackTrace
            });
        }
    }

    /// <summary>
    /// Submit a complete guest application (with file upload support)
    /// </summary>
    [HttpPost("submit")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> Submit([FromForm] GuestApplicationSubmitRequest request)
    {
        try
        {
            _logger.LogInformation("Guest application submission started for email: {Email}", request.EmailId);

            // Check model state validation
            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .ToDictionary(x => x.Key, x => x.Value?.Errors.Select(e => e.ErrorMessage).ToArray());
                
                _logger.LogWarning("Model validation failed: {@Errors}", errors);
                
                return BadRequest(new { 
                    success = false,
                    message = "Validation failed", 
                    errors = errors
                });
            }

            // Basic validation
            if (string.IsNullOrEmpty(request.EmailId))
            {
                _logger.LogWarning("Email is required but was null or empty");
                return BadRequest(new { message = "Email is required" });
            }

            if (string.IsNullOrEmpty(request.FirstName))
            {
                _logger.LogWarning("First name is required but was null or empty");
                return BadRequest(new { message = "First name is required" });
            }

            if (string.IsNullOrEmpty(request.LastName))
            {
                _logger.LogWarning("Last name is required but was null or empty");
                return BadRequest(new { message = "Last name is required" });
            }

            if (string.IsNullOrEmpty(request.MobileNumber))
            {
                _logger.LogWarning("Mobile number is required but was null or empty");
                return BadRequest(new { message = "Mobile number is required" });
            }

            // Create or find guest user
            var guestUser = await GetOrCreateGuestUser(request.EmailId);
            
            // Get the initial workflow state (first state by order)
            var initialWorkflowState = await _unitOfWork.Repository<WorkflowState>()
                .FindAsync(ws => ws.Order == 1 || ws.Name == "DOCUMENT_VERIFICATION_PENDING");
            var workflowState = initialWorkflowState.FirstOrDefault();
            
            if (workflowState == null)
            {
                _logger.LogError("Initial workflow state not found. Please ensure WorkflowStates are seeded in the database.");
                return BadRequest(new { message = "Initial workflow state not found. Please contact system administrator." });
            }

            // Generate unique application number
            var applicationNumber = $"APP{DateTime.UtcNow:yyyyMMdd}{DateTime.UtcNow.Ticks % 100000:D5}";

            // Map DTO to Application entity
            var application = new ApplicationEntity
            {
                ApplicationNumber = applicationNumber,
                FirstName = request.FirstName,
                MiddleName = request.MiddleName,
                LastName = request.LastName,
                MotherName = request.MotherName,
                MobileNumber = request.MobileNumber,
                EmailAddress = request.EmailId,
                BirthDate = ParseBirthDate(request.BirthDate),
                PanNumber = request.PanNumber,
                AadharNumber = request.AadharNumber,
                Place = request.LocalAddress?.City ?? "",
                RoomNumber = "N/A", // Default for guest applications
                BloodGroup = ParseBloodGroup(request.BloodGroup),
                Height = ParseHeight(request.Height),
                Gender = ParseGender(request.Gender),
                Position = ParsePosition(request.Position),
                PermanentSameAsLocal = request.PermanentSameAsLocal,
                // COA details for architects
                CoaCertificationNumber = request.EssentialDetails?.CoaCertificationNumber,
                CoaValidity = ParseDate(request.EssentialDetails?.CoaValidity),
                UserId = guestUser.Id,
                WorkflowStateId = workflowState.Id,
                CurrentState = WorkflowStateType.DOCUMENT_VERIFICATION_PENDING,
                SubmissionDate = DateTime.UtcNow,
                Amount = 0, // Set based on position if needed
                Password = GenerateRandomPassword()
            };

            // Add to repository
            await _unitOfWork.Repository<ApplicationEntity>().AddAsync(application);
            await _unitOfWork.SaveChangesAsync();

            // Update application with file paths
            application.PanAttachmentFilePath = request.PanAttachment != null ? await SaveFile(request.PanAttachment, "identity_documents") : null;
            application.AadharAttachmentFilePath = request.AadharAttachment != null ? await SaveFile(request.AadharAttachment, "identity_documents") : null;
            application.CoaCertificateFilePath = request.EssentialDetails?.CoaCertificate != null ? await SaveFile(request.EssentialDetails.CoaCertificate, "coa_certificates") : null;
            application.ElectricityBillFilePath = request.ElectricityBill != null ? await SaveFile(request.ElectricityBill, "utility_documents") : null;
            application.StructuralEngineerDocFilePath = request.StructuralEngineerDoc != null ? await SaveFile(request.StructuralEngineerDoc, "technical_documents") : null;
            application.ProfilePictureFilePath = request.ProfilePicture != null ? await SaveFile(request.ProfilePicture, "profile_pictures") : null;

            // Add addresses
            if (request.LocalAddress != null)
            {
                var localAddress = new Address
                {
                    ApplicationId = application.Id,
                    FlatHouseNumber = request.LocalAddress.FlatHouseNumber,
                    StreetAddress = request.LocalAddress.StreetAddress,
                    AddressLine = request.LocalAddress.AddressLine,
                    City = request.LocalAddress.City,
                    State = request.LocalAddress.State,
                    Country = request.LocalAddress.Country,
                    PostalCode = request.LocalAddress.PostalCode,
                    IsLocal = true
                };
                await _unitOfWork.Repository<Address>().AddAsync(localAddress);
            }

            if (request.PermanentAddress != null)
            {
                var permanentAddress = new Address
                {
                    ApplicationId = application.Id,
                    FlatHouseNumber = request.PermanentAddress.FlatHouseNumber,
                    StreetAddress = request.PermanentAddress.StreetAddress,
                    AddressLine = request.PermanentAddress.Address,
                    City = request.PermanentAddress.City,
                    State = request.PermanentAddress.State,
                    Country = request.PermanentAddress.Country,
                    PostalCode = request.PermanentAddress.PostalCode,
                    IsLocal = false
                };
                await _unitOfWork.Repository<Address>().AddAsync(permanentAddress);
            }

            // Add qualification
            if (request.Qualification != null)
            {
                var qualification = new Qualification
                {
                    ApplicationId = application.Id,
                    InstituteName = request.Qualification.InstituteName,
                    UniversityName = request.Qualification.UniversityName,
                    CourseSpecialization = request.Qualification.CourseSpecialization,
                    DegreeProgram = request.Qualification.DegreeProgram,
                    PassingMonth = request.Qualification.PassingMonth,
                    PassingYear = request.Qualification.PassingYear,
                    CertificateFilePath = await SaveFile(request.Qualification.Certificate, "qualification_certificates"),
                    LastYearMarksheetFilePath = await SaveFile(request.Qualification.LastYearMarksheet, "marksheets")
                };
                await _unitOfWork.Repository<Qualification>().AddAsync(qualification);
            }

            // Add experiences
            if (request.Experiences?.Any() == true)
            {
                foreach (var exp in request.Experiences)
                {
                    var experience = new Experience
                    {
                        ApplicationId = application.Id,
                        CompanyName = exp.CompanyName,
                        Position = exp.Position,
                        YearsOfExperience = int.TryParse(exp.YearsOfExperience, out var years) ? years : 0,
                        FromDate = DateTime.TryParse(exp.FromDate, out var fromDate) ? DateTime.SpecifyKind(fromDate, DateTimeKind.Utc) : DateTime.UtcNow.Date,
                        ToDate = DateTime.TryParse(exp.ToDate, out var toDate) ? DateTime.SpecifyKind(toDate, DateTimeKind.Utc) : DateTime.UtcNow.Date,
                        CertificateFilePath = await SaveFile(exp.Certificate, "experience_certificates"),
                        ExperienceRequired = true
                    };
                    await _unitOfWork.Repository<Experience>().AddAsync(experience);
                }
            }

            // Add documents (including identity documents)
            var documentsToAdd = new List<Document>();

            // Handle PAN document
            if (request.PanAttachment != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "PAN Card",
                    DocumentType = "PAN",
                    FilePath = await SaveFile(request.PanAttachment, "identity_documents") ?? "",
                    FileSize = request.PanAttachment.Length,
                    FileType = request.PanAttachment.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow,
                    Description = $"PAN Number: {request.PanNumber}"
                });
            }

            // Handle Aadhar document
            if (request.AadharAttachment != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "Aadhar Card",
                    DocumentType = "AADHAR",
                    FilePath = await SaveFile(request.AadharAttachment, "identity_documents") ?? "",
                    FileSize = request.AadharAttachment.Length,
                    FileType = request.AadharAttachment.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow,
                    Description = $"Aadhar Number: {request.AadharNumber}"
                });
            }

            // Handle Essential Details documents (for Architect)
            if (request.EssentialDetails?.CoaCertificate != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "COA Certificate",
                    DocumentType = "COA_CERTIFICATE",
                    FilePath = await SaveFile(request.EssentialDetails.CoaCertificate, "coa_certificates") ?? "",
                    FileSize = request.EssentialDetails.CoaCertificate.Length,
                    FileType = request.EssentialDetails.CoaCertificate.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow,
                    Description = $"COA Number: {request.EssentialDetails.CoaCertificationNumber}, Validity: {request.EssentialDetails.CoaValidity}"
                });
            }

            // Handle additional files
            if (request.ElectricityBill != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "Electricity Bill",
                    DocumentType = "ELECTRICITY_BILL",
                    FilePath = await SaveFile(request.ElectricityBill, "utility_documents") ?? "",
                    FileSize = request.ElectricityBill.Length,
                    FileType = request.ElectricityBill.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow
                });
            }

            if (request.StructuralEngineerDoc != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "Structural Engineer Document",
                    DocumentType = "STRUCTURAL_ENGINEER_DOC",
                    FilePath = await SaveFile(request.StructuralEngineerDoc, "technical_documents") ?? "",
                    FileSize = request.StructuralEngineerDoc.Length,
                    FileType = request.StructuralEngineerDoc.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow
                });
            }

            if (request.ProfilePicture != null)
            {
                documentsToAdd.Add(new Document
                {
                    ApplicationId = application.Id,
                    Title = "Profile Picture",
                    DocumentType = "PROFILE_PICTURE",
                    FilePath = await SaveFile(request.ProfilePicture, "profile_pictures") ?? "",
                    FileSize = request.ProfilePicture.Length,
                    FileType = request.ProfilePicture.ContentType ?? "application/octet-stream",
                    UploadDate = DateTime.UtcNow
                });
            }

            // Handle custom supporting documents
            if (request.Documents?.Any() == true)
            {
                foreach (var doc in request.Documents.Where(d => !string.IsNullOrEmpty(d.DocumentName)))
                {
                    documentsToAdd.Add(new Document
                    {
                        ApplicationId = application.Id,
                        Title = doc.DocumentName,
                        DocumentType = "SUPPORTING_DOCUMENT",
                        FilePath = await SaveFile(doc.Attachment, "supporting_documents") ?? "",
                        FileSize = doc.Attachment?.Length ?? 0,
                        FileType = doc.Attachment?.ContentType ?? "application/octet-stream",
                        UploadDate = DateTime.UtcNow
                    });
                }
            }

            // Add all documents to repository
            foreach (var document in documentsToAdd)
            {
                await _unitOfWork.Repository<Document>().AddAsync(document);
            }

            // Save additional files
            if (request.SelfDeclarationFile != null)
            {
                application.SelfDeclarationFormFilePath = await SaveFile(request.SelfDeclarationFile, "self_declarations");
            }

            // Save all changes
            await _unitOfWork.SaveChangesAsync();

            // Automatically route application to appropriate Junior Engineer
            try
            {
                var nextEngineer = await _routingService.GetNextEngineerForPositionAsync(application.Position, RoleType.JRENGG_ARCH);
                if (nextEngineer != null)
                {
                    await _routingService.AssignApplicationToEngineerAsync(application, nextEngineer);
                    _logger.LogInformation("Application {ApplicationId} automatically assigned to engineer {EngineerId}", 
                        application.Id, nextEngineer.Id);
                }
                else
                {
                    _logger.LogWarning("No available engineer found for position {Position}. Application {ApplicationId} remains unassigned", 
                        application.Position, application.Id);
                }
            }
            catch (Exception routingEx)
            {
                _logger.LogError(routingEx, "Failed to automatically route application {ApplicationId}, but application was saved successfully", 
                    application.Id);
                // Don't fail the entire operation if routing fails
            }

            return Ok(new {
                success = true,
                message = "Application submitted successfully",
                data = new {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    referenceNumber = application.ApplicationNumber,
                    status = "submitted",
                    submittedAt = application.SubmissionDate.ToString("o")
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting guest application for email: {Email}", request?.EmailId);
            return BadRequest(new { 
                success = false,
                message = "Failed to submit application", 
                error = ex.Message,
                details = ex.InnerException?.Message
            });
        }
    }

    /// <summary>
    /// Get all applications for a specific guest user by email (for dashboard)
    /// </summary>
    /// <param name="email">Guest user's email address</param>
    /// <returns>List of applications for the specified guest user</returns>
    [HttpGet("{email:regex(.*@.*)}")] // Simple email detection regex
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    public async Task<IActionResult> GetGuestApplicationsByEmail(string email)
    {
        try
        {
            _logger.LogInformation("Fetching applications for guest user: {Email}", email);

            // Log all request headers for debugging
            foreach (var header in Request.Headers)
            {
                _logger.LogInformation("Header: {Key} = {Value}", header.Key, string.Join(", ", header.Value.ToArray() ?? Array.Empty<string>()));
            }

            // Get guest session from headers
            var guestEmail = Request.Headers["X-Guest-Email"].FirstOrDefault();
            var guestSession = Request.Headers["X-Guest-Session"].FirstOrDefault();

            _logger.LogInformation("X-Guest-Email header: {GuestEmail}", guestEmail);
            _logger.LogInformation("X-Guest-Session header exists: {HasSession}", !string.IsNullOrEmpty(guestSession));

            // Validate guest session headers
            if (string.IsNullOrEmpty(guestEmail) || string.IsNullOrEmpty(guestSession))
            {
                _logger.LogWarning("Guest session headers missing for email: {Email}. GuestEmail: {GuestEmailHeader}, HasSession: {HasSession}", 
                    email, guestEmail, !string.IsNullOrEmpty(guestSession));
                return Unauthorized(new { success = false, message = "Guest session required" });
            }

            // Ensure email matches session
            if (email != guestEmail)
            {
                _logger.LogWarning("Email mismatch - URL: {EmailUrl}, Header: {EmailHeader}", email, guestEmail);
                return Unauthorized(new { success = false, message = "Email mismatch" });
            }

            // Validate guest session (basic validation for now)
            if (!ValidateGuestSession(guestSession, email))
            {
                _logger.LogWarning("Invalid guest session for email: {Email}", email);
                return Unauthorized(new { success = false, message = "Invalid guest session" });
            }

            // Find all applications for this guest user
            var applications = await _unitOfWork.Repository<ApplicationEntity>()
                .FindAsync(a => a.EmailAddress == email);

            var applicationsList = applications.ToList();

            if (!applicationsList.Any())
            {
                _logger.LogInformation("No applications found for guest user: {Email}", email);
                return Ok(new
                {
                    success = false,
                    message = "No applications found for this email",
                    data = new object[0]
                });
            }

            var result = applicationsList.Select(app => new
            {
                id = app.Id.ToString(),
                applicationNumber = app.ApplicationNumber,
                position = app.Position.ToString(),
                firstName = app.FirstName,
                lastName = app.LastName,
                status = MapWorkflowStateToStatus(app.CurrentState),
                submittedAt = app.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                updatedAt = app.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                estimatedCompletion = (string?)null
            }).OrderByDescending(x => x.submittedAt).ToList();

            _logger.LogInformation("Found {Count} applications for guest user: {Email}", result.Count, email);

            return Ok(new
            {
                success = true,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching applications for guest user: {Email}", email);
            return StatusCode(500, new
            {
                success = false,
                message = $"Error: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Get application details for guest access (requires OTP verification token)
    /// </summary>
    /// <param name="applicationNumber">Application number</param>
    /// <returns>Application details for guest view</returns>
    [HttpGet("{applicationNumber}")] // Will match non-email patterns due to route ordering
    [Authorize(Roles = "Guest")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    public async Task<IActionResult> GetGuestApplication(string applicationNumber)
    {
        try
        {
            // Get current user's email from JWT token
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            if (string.IsNullOrEmpty(userEmail))
            {
                return BadRequest(new { message = "User email not found in token" });
            }

            // Find application by application number and ensure it belongs to the current user
            var applications = await _unitOfWork.Repository<ApplicationEntity>()
                .FindAsync(a => a.ApplicationNumber == applicationNumber && a.EmailAddress == userEmail);
        
            var application = applications.FirstOrDefault();
            if (application == null)
            {
                return NotFound(new { message = "Application not found" });
            }

            // Get related data
            var addresses = await _unitOfWork.Repository<Address>()
                .FindAsync(addr => addr.ApplicationId == application.Id);
            var qualifications = await _unitOfWork.Repository<Qualification>()
                .FindAsync(qual => qual.ApplicationId == application.Id);
            var experiences = await _unitOfWork.Repository<Experience>()
                .FindAsync(exp => exp.ApplicationId == application.Id);
            var documents = await _unitOfWork.Repository<Document>()
                .FindAsync(doc => doc.ApplicationId == application.Id);

            var addressList = addresses.ToList();
            var localAddress = addressList.FirstOrDefault(a => a.IsLocal);
            var permanentAddress = addressList.FirstOrDefault(a => !a.IsLocal);
            var qualification = qualifications.FirstOrDefault();
            var experienceList = experiences.ToList();
            var documentList = documents.ToList();

            // Find PAN and Aadhar documents by type and extract numbers from descriptions
            var panDocument = documentList.FirstOrDefault(d => d.DocumentType == "PAN");
            var aadharDocument = documentList.FirstOrDefault(d => d.DocumentType == "AADHAR");
            
            // Extract PAN and Aadhar numbers from document descriptions
            var panNumber = ExtractNumberFromDescription(panDocument?.Description, "PAN Number:");
            var aadharNumber = ExtractNumberFromDescription(aadharDocument?.Description, "Aadhar Number:");

            // Build enhanced response
            var response = new
            {
                success = true,
                data = new
                {
                    // Basic Information
                    id = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    firstName = application.FirstName,
                    middleName = application.MiddleName,
                    lastName = application.LastName,
                    name = $"{application.FirstName} {application.MiddleName} {application.LastName}".Trim(),
                    position = application.Position.ToString(),
                    status = application.CurrentState.ToString(),
                    submittedAt = application.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    createdAt = application.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    updatedAt = application.SubmissionDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),

                    // Personal Details
                    motherName = application.MotherName,
                    mobileNumber = application.MobileNumber,
                    emailId = application.EmailAddress,
                    birthDate = application.SubmissionDate.ToString("yyyy-MM-dd"), // Using submission date as placeholder
                    bloodGroup = application.BloodGroup.ToString(),
                    height = application.Height.ToString(),
                    gender = application.Gender.ToString().ToLower(),

                    // Address Information
                    localAddress = localAddress != null ? new
                    {
                        flatHouseNumber = localAddress.FlatHouseNumber,
                        streetAddress = localAddress.StreetAddress,
                        addressLine = localAddress.AddressLine,
                        country = localAddress.Country,
                        state = localAddress.State,
                        city = localAddress.City,
                        postalCode = localAddress.PostalCode
                    } : null,

                    permanentAddress = permanentAddress != null ? new
                    {
                        flatHouseNumber = permanentAddress.FlatHouseNumber,
                        streetAddress = permanentAddress.StreetAddress,
                        address = permanentAddress.AddressLine,
                        country = permanentAddress.Country,
                        state = permanentAddress.State,
                        city = permanentAddress.City,
                        postalCode = permanentAddress.PostalCode
                    } : null,

                    permanentSameAsLocal = application.PermanentSameAsLocal,

                    // Identity Documents (looked up from documents table)
                    panNumber = panNumber ?? "", // Extracted from document description
                    aadharNumber = aadharNumber ?? "", // Extracted from document description
                    panDocumentUrl = panDocument?.FilePath,
                    aadharDocumentUrl = aadharDocument?.FilePath,

                    // Qualification Details
                    qualification = qualification != null ? new
                    {
                        instituteName = qualification.InstituteName,
                        universityName = qualification.UniversityName,
                        courseSpecialization = qualification.CourseSpecialization,
                        degreeProgram = qualification.DegreeProgram,
                        passingMonth = qualification.PassingMonth,
                        passingYear = int.TryParse(qualification.PassingYear, out var year) ? year : 0,
                        percentage = 0, // Not stored in current model
                        qualificationDocumentUrl = qualification.CertificateFilePath
                    } : null,

                    // Experience Details
                    experiences = experienceList.Select(exp => new
                    {
                        organizationName = exp.CompanyName,
                        position = exp.Position,
                        fromDate = exp.FromDate.ToString("yyyy-MM-dd"),
                        toDate = exp.ToDate.ToString("yyyy-MM-dd"),
                        experienceType = "Professional", // Default value
                        experienceDocumentUrl = exp.CertificateFilePath
                    }).ToList(),

                    totalExperienceYears = experienceList.Sum(e => e.YearsOfExperience),
                    totalExperienceMonths = 0, // Not calculated in current model

                    // Documents
                    documents = documentList.Select(doc => new
                    {
                        documentType = doc.DocumentType,
                        fileName = doc.Title,
                        fileUrl = doc.FilePath,
                        uploadedAt = doc.UploadDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }).ToList(),

                    // Additional Information
                    place = application.Place,
                    roomNumber = application.RoomNumber,
                    certificateNumber = application.CertificateNumber,
                    hasCertificate = !string.IsNullOrEmpty(application.CertificateFilePath),
                    canDownloadCertificate = application.CurrentState == WorkflowStateType.FINAL_APPROVE && 
                                            !string.IsNullOrEmpty(application.CertificateFilePath)
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching guest application details for application number: {ApplicationNumber}", applicationNumber);
            return BadRequest(new
            {
                success = false,
                message = "Failed to fetch application details",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Download certificate for guest access (requires OTP verification token)
    /// </summary>
    /// <param name="applicationNumber">Application number</param>
    /// <returns>Certificate PDF file</returns>
    [HttpGet("{applicationNumber}/certificate")]
    [Authorize(Roles = "Guest")]
    [ProducesResponseType(typeof(FileResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    public async Task<IActionResult> GetGuestCertificate(string applicationNumber)
    {
        var applications = await _unitOfWork.Repository<ApplicationEntity>()
            .FindAsync(a => a.ApplicationNumber == applicationNumber);
        
        var application = applications.FirstOrDefault();
        if (application == null)
        {
            return NotFound(new { message = "Application not found" });
        }

        // Check if certificate is available
        if (application.CurrentState != WorkflowStateType.FINAL_APPROVE)
        {
            return BadRequest(new { 
                message = "Certificate is not yet available. Please check application status.",
                currentStatus = application.CurrentState.ToString()
            });
        }

        if (string.IsNullOrEmpty(application.CertificateFilePath))
        {
            return BadRequest(new { message = "Certificate file not found" });
        }

        // Check if certificate file exists on disk
        if (!System.IO.File.Exists(application.CertificateFilePath))
        {
            return NotFound(new { message = "Certificate file not found on server" });
        }

        // Return certificate file
        var fileBytes = await System.IO.File.ReadAllBytesAsync(application.CertificateFilePath);
        var fileName = $"Certificate_{application.ApplicationNumber}_{application.CertificateNumber}.pdf";
        
        return File(fileBytes, "application/pdf", fileName);
    }

    // Helper methods
    private async Task<User> GetOrCreateGuestUser(string email)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == email);
        var existingUser = users.FirstOrDefault();
        
        if (existingUser != null)
            return existingUser;

        // Create new guest user
        var guestRole = await _unitOfWork.Repository<Role>().FindAsync(r => r.Name == "Guest");
        var role = guestRole.FirstOrDefault();
        
        if (role == null)
        {
            // Create Guest role if it doesn't exist
            role = new Role { Name = "Guest", Description = "Guest user for applications" };
            await _unitOfWork.Repository<Role>().AddAsync(role);
            await _unitOfWork.SaveChangesAsync();
        }

        var newUser = new User
        {
            Email = email,
            FirstName = "Guest",
            LastName = "User",
            RoleId = role.Id,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            Password = GenerateRandomPassword()
        };

        await _unitOfWork.Repository<User>().AddAsync(newUser);
        await _unitOfWork.SaveChangesAsync();
        return newUser;
    }

    private BloodGroup ParseBloodGroup(string bloodGroup)
    {
        return bloodGroup?.ToUpper() switch
        {
            "A+" => BloodGroup.APositive,
            "A-" => BloodGroup.ANegative,
            "B+" => BloodGroup.BPositive,
            "B-" => BloodGroup.BNegative,
            "AB+" => BloodGroup.ABPositive,
            "AB-" => BloodGroup.ABNegative,
            "O+" => BloodGroup.OPositive,
            "O-" => BloodGroup.ONegative,
            _ => BloodGroup.APositive
        };
    }

    private decimal ParseHeight(string height)
    {
        if (decimal.TryParse(height, out var result))
            return result;
        return 0;
    }

    private Gender ParseGender(string gender)
    {
        return gender?.ToLower() switch
        {
            "male" => Gender.Male,
            "female" => Gender.Female,
            "other" => Gender.Other,
            _ => Gender.Male
        };
    }

    private PositionType ParsePosition(string position)
    {
        return position?.Replace(" ", "") switch
        {
            "Architect" => PositionType.Architect,
            "LicenceEngineer" => PositionType.LicenseEngineer,
            "StructuralEngineer" => PositionType.StructuralEngineer,
            "Supervisor1" => PositionType.Supervisor1,
            "Supervisor2" => PositionType.Supervisor2,
            _ => PositionType.LicenseEngineer
        };
    }

    private DateTime ParseBirthDate(string birthDate)
    {
        if (DateTime.TryParse(birthDate, out var date))
        {
            return date;
        }
        return DateTime.MinValue; // Default value if parsing fails
    }

    private DateTime? ParseDate(string? dateString)
    {
        if (string.IsNullOrEmpty(dateString))
            return null;
            
        if (DateTime.TryParse(dateString, out var date))
        {
            return date;
        }
        return null;
    }

    private string GenerateRandomPassword()
    {
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 8)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private string? ExtractNumberFromDescription(string? description, string prefix)
    {
        if (string.IsNullOrEmpty(description) || string.IsNullOrEmpty(prefix))
            return null;

        var index = description.IndexOf(prefix, StringComparison.OrdinalIgnoreCase);
        if (index == -1)
            return null;

        var startIndex = index + prefix.Length;
        var endIndex = description.IndexOf(',', startIndex);
        if (endIndex == -1)
            endIndex = description.Length;

        return description.Substring(startIndex, endIndex - startIndex).Trim();
    }

    private async Task<string?> SaveFile(IFormFile? file, string subfolder)
    {
        if (file == null || file.Length == 0)
            return null;

        try
        {
            var uploadsFolder = Path.Combine("wwwroot", "uploads", subfolder);
            Directory.CreateDirectory(uploadsFolder);

            var fileName = $"{Guid.NewGuid()}_{file.FileName}";
            var filePath = Path.Combine(uploadsFolder, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            return Path.Combine("uploads", subfolder, fileName).Replace("\\", "/");
        }
        catch
        {
            return null;
        }
    }

    // Helper methods for guest authentication and status mapping
    private bool ValidateGuestAccess(string email)
    {
        try
        {
            // Check JWT token first
            if (User.Identity?.IsAuthenticated == true && User.IsInRole("Guest"))
            {
                var tokenEmail = User.FindFirst("email")?.Value;
                if (!string.IsNullOrEmpty(tokenEmail) && tokenEmail.Equals(email, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            // Check X-Guest-Session header
            var guestSessionHeader = Request.Headers["X-Guest-Session"].FirstOrDefault();
            var guestEmailHeader = Request.Headers["X-Guest-Email"].FirstOrDefault();
            
            if (!string.IsNullOrEmpty(guestEmailHeader) && guestEmailHeader.Equals(email, StringComparison.OrdinalIgnoreCase))
            {
                // Basic validation - in production, you'd want to validate the session token properly
                if (!string.IsNullOrEmpty(guestSessionHeader))
                {
                    return true;
                }
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private string MapWorkflowStateToStatus(WorkflowStateType workflowState)
    {
        return workflowState switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => "SUBMITTED",
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => "UNDER_REVIEW",
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING => "UNDER_REVIEW",
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => "UNDER_REVIEW",
            WorkflowStateType.CITY_ENGINEER_PENDING => "UNDER_REVIEW",
            WorkflowStateType.PAYMENT_PENDING => "PAYMENT_PENDING",
            WorkflowStateType.CLERK_PENDING => "UNDER_REVIEW",
            WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING => "UNDER_REVIEW",
            WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING => "UNDER_REVIEW",
            WorkflowStateType.FINAL_APPROVE => "APPROVED",
            WorkflowStateType.REJECTED => "REJECTED",
            _ => "SUBMITTED"
        };
    }

    private string? GetEstimatedCompletion(DateTime submissionDate)
    {
        // Add 21 business days (approximately 3 weeks) for processing
        var estimatedDate = submissionDate.AddDays(21);
        return estimatedDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
    }

    /// <summary>
    /// Validates guest session for accessing guest applications
    /// </summary>
    /// <param name="sessionToken">Session token from X-Guest-Session header</param>
    /// <param name="email">Email to validate against session</param>
    /// <returns>True if session is valid, false otherwise</returns>
    private bool ValidateGuestSession(string sessionToken, string email)
    {
        try
        {
            // For now, return true as this is a placeholder implementation
            // TODO: Implement proper session validation logic
            // This could involve checking against a cache, database, or validating a signed token
            
            _logger.LogInformation("Validating guest session for email: {Email}", email);
            
            // Placeholder validation - replace with actual implementation
            return !string.IsNullOrEmpty(sessionToken) && !string.IsNullOrEmpty(email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating guest session for email: {Email}", email);
            return false;
        }
    }
}
