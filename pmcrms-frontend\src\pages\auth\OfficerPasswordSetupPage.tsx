import React, { useState, useEffect } from 'react'
import { <PERSON>, useNavigate, useSearchParams } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Shield, ArrowLeft, Loader2, CheckCircle, XCircle } from 'lucide-react'
import { api } from '../../lib/api'

interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

interface TokenValidationResponse {
  isValid: boolean
  email: string
  firstName: string
  lastName: string
  role: string
}

const OfficerPasswordSetupPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const token = searchParams.get('token')
  const email = searchParams.get('email')
  
  const [loading, setLoading] = useState(false)
  const [validatingToken, setValidatingToken] = useState(true)
  const [tokenValid, setTokenValid] = useState(false)
  const [officerInfo, setOfficerInfo] = useState<TokenValidationResponse | null>(null)
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  // Validate token on component mount
  useEffect(() => {
    if (!token || !email) {
      setError('Invalid invitation link. Please contact your administrator.')
      setValidatingToken(false)
      return
    }

    validateToken()
  }, [token, email])

  const validateToken = async () => {
    try {
      const response = await api.post('/auth/officer/validate-setup-token', { 
        token, 
        email 
      }) as ApiResponse<TokenValidationResponse>
      
      if (response.success) {
        setTokenValid(true)
        setOfficerInfo(response.data)
      } else {
        setError('Invalid or expired invitation link.')
      }
    } catch (error: any) {
      setError('Invalid or expired invitation link.')
    } finally {
      setValidatingToken(false)
    }
  }

  const handlePasswordSetup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!password || !confirmPassword) return

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await api.post('/auth/officer/setup-password-with-token', {
        token,
        email,
        password,
        confirmPassword
      }) as ApiResponse<any>
      
      if (response.success) {
        setSuccess(true)
        setTimeout(() => {
          navigate('/officer/login?email=' + encodeURIComponent(email || ''))
        }, 2000)
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to set password. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (validatingToken) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-12 w-12 text-primary animate-spin" />
            </div>
            <CardTitle className="text-2xl">Validating Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation link...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (!tokenValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <XCircle className="h-12 w-12 text-red-500" />
            </div>
            <CardTitle className="text-2xl">Invalid Invitation</CardTitle>
            <CardDescription>
              {error || 'Your invitation link is invalid or has expired.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 text-center">
                Please contact your administrator for a new invitation.
              </p>
              <Link to="/officer/login">
                <Button className="w-full" variant="outline">
                  Go to Officer Login
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
            <CardTitle className="text-2xl">Password Set Successfully!</CardTitle>
            <CardDescription>
              Your password has been set. Redirecting to login page...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Shield className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">Set Up Your Password</CardTitle>
          <CardDescription>
            Welcome {officerInfo?.firstName} {officerInfo?.lastName}! Please create your password to complete your account setup.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {error && (
            <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handlePasswordSetup} className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={officerInfo?.email || ''}
                disabled
                className="bg-gray-50"
              />
            </div>
            
            <div>
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                type="text"
                value={officerInfo?.role || ''}
                disabled
                className="bg-gray-50"
              />
            </div>
            
            <div>
              <Label htmlFor="password">New Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your new password"
                required
                minLength={8}
              />
              <p className="text-xs text-gray-500 mt-1">Must be at least 8 characters long</p>
            </div>
            
            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
                required
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Setting Password...
                </>
              ) : (
                'Set Password & Complete Setup'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default OfficerPasswordSetupPage
