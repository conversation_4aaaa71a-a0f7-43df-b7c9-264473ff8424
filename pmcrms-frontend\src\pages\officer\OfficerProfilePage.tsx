import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { User, Edit, Save, X, Shield, ArrowLeft } from 'lucide-react'
import { api } from '@/lib/api'
import { Link } from 'react-router-dom'

interface OfficerProfile {
  id: number
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  role: string
  isActive: boolean
  createdAt: string
  lastLogin?: string
}

interface UpdateProfileRequest {
  firstName: string
  lastName: string
  phoneNumber?: string
}

const OfficerProfilePage: React.FC = () => {
  const [profile, setProfile] = useState<OfficerProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [editForm, setEditForm] = useState<UpdateProfileRequest>({
    firstName: '',
    lastName: '',
    phoneNumber: ''
  })
  const { toast } = useToast()

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setLoading(true)
      const response = await api.get('/auth/officer/profile')

      if (response.success && response.data) {
        const profileData = response.data as OfficerProfile
        setProfile(profileData)
        setEditForm({
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          phoneNumber: profileData.phoneNumber || ''
        })
      }
    } catch (error: any) {
      console.error('Failed to load profile:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load profile',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    setEditing(true)
  }

  const handleCancel = () => {
    if (profile) {
      setEditForm({
        firstName: profile.firstName,
        lastName: profile.lastName,
        phoneNumber: profile.phoneNumber || ''
      })
    }
    setEditing(false)
  }

  const handleSave = async () => {
    if (!editForm.firstName.trim() || !editForm.lastName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'First name and last name are required',
        variant: 'destructive'
      })
      return
    }

    try {
      setSaving(true)
      const response = await api.put('/auth/officer/profile', {
        firstName: editForm.firstName.trim(),
        lastName: editForm.lastName.trim(),
        phoneNumber: editForm.phoneNumber?.trim() || null
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Profile updated successfully'
        })
        setEditing(false)
        await loadProfile() // Reload to get updated data
      }
    } catch (error: any) {
      console.error('Failed to update profile:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update profile',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      'JRENGG_ARCH': 'Junior Engineer - Architect',
      'JRENGG_STRU': 'Junior Engineer - Structural',
      'JRENGG_LICE': 'Junior Engineer - License',
      'JRENGG_SUPER1': 'Junior Engineer - Supervisor 1',
      'JRENGG_SUPER2': 'Junior Engineer - Supervisor 2',
      'ASSIENGG_ARCH': 'Assistant Engineer - Architect',
      'ASSIENGG_STRU': 'Assistant Engineer - Structural',
      'ASSIENGG_LICE': 'Assistant Engineer - License',
      'ASSIENGG_SUPER1': 'Assistant Engineer - Supervisor 1',
      'ASSIENGG_SUPER2': 'Assistant Engineer - Supervisor 2',
      'ExecutiveEngineer': 'Executive Engineer',
      'CityEngineer': 'City Engineer',
      'Clerk': 'Clerk',
      'OfflinePaymentOfficer': 'Offline Payment Officer'
    }
    return roleMap[role] || role
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Profile Not Found</h3>
              <p className="text-gray-600 mb-4">Unable to load your profile information.</p>
              <Button onClick={loadProfile}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
                <p className="text-gray-600">Manage your account information</p>
              </div>
            </div>
            <Link to="/dashboard">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Information */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>
                      Update your personal information and contact details
                    </CardDescription>
                  </div>
                  {!editing && (
                    <Button variant="outline" onClick={handleEdit}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    {editing ? (
                      <Input
                        id="firstName"
                        value={editForm.firstName}
                        onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                        placeholder="Enter your first name"
                      />
                    ) : (
                      <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                        {profile.firstName}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    {editing ? (
                      <Input
                        id="lastName"
                        value={editForm.lastName}
                        onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                        placeholder="Enter your last name"
                      />
                    ) : (
                      <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                        {profile.lastName}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                    {profile.email}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                </div>

                <div>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  {editing ? (
                    <Input
                      id="phoneNumber"
                      value={editForm.phoneNumber}
                      onChange={(e) => setEditForm({ ...editForm, phoneNumber: e.target.value })}
                      placeholder="Enter your phone number"
                    />
                  ) : (
                    <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                      {profile.phoneNumber || 'Not provided'}
                    </p>
                  )}
                </div>

                {editing && (
                  <div className="flex space-x-3 pt-4">
                    <Button onClick={handleSave} disabled={saving}>
                      <Save className="h-4 w-4 mr-2" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button variant="outline" onClick={handleCancel} disabled={saving}>
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Account Details */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Account Details</CardTitle>
                <CardDescription>Your account information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Role</Label>
                  <div className="mt-1">
                    <Badge variant="secondary" className="text-sm">
                      {getRoleDisplayName(profile.role)}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700">Status</Label>
                  <div className="mt-1">
                    <Badge variant={profile.isActive ? 'default' : 'secondary'}>
                      {profile.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700">Member Since</Label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(profile.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700">Last Login</Label>
                  <p className="mt-1 text-sm text-gray-900">
                    {profile.lastLogin 
                      ? new Date(profile.lastLogin).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : 'Never'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OfficerProfilePage
