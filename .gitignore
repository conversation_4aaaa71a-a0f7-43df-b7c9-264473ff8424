# Build folders
bin/
obj/

# VS Code settings folder
.vscode/

# User-specific files
*.user
*.suo
*.userosscache
*.sln.docstates

# Auto generated files by Visual Studio
*.pidb
*.svclog
*.scc

# Build results
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
build/
bld/
[Bb]in/
[Oo]bj/

# Rider IDE
.idea/

# ASP.NET Temporary files
.app/

# Rider & Resharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# NuGet packages
*.nupkg
*.snupkg
.nuget/
packages/
# The packages folder can be ignored because they are restored automatically during build.

# EF Core Migrations metadata file
Migrations/

# Entity Framework Core snapshot files
*/Migrations/*.cs

# Visual Studio Code workspace file
*.code-workspace

# User secrets
secrets.json

# DotCover
*.dotCover

# Publish artifacts
publish/

# Log files
*.log

# JetBrains Rider
.idea/

# SQL Server files
*.mdf
*.ldf
*.ndf

# IIS Express
/.vs/
iisExpress/

# Others
*.vspscc
*.vsscc

# dotCover
*.dotCover

# Coverage Results
coverage/
*.coverage
*.coveragexml

# StyleCop
StyleCopReport.xml

# OS generated files
.DS_Store
Thumbs.db
