import React, { useState } from 'react'
import { api } from '../lib/api'

const ApiTest: React.FC = () => {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAdminLogin = async () => {
    setLoading(true)
    try {
      const response = await api.post('/auth/admin/login', {
        email: '<EMAIL>',
        password: 'Admin@123!'
      })
      
      console.log('Raw API Response:', response)
      setResult({
        success: true,
        data: response,
        message: 'API call successful'
      })
    } catch (error: any) {
      console.error('API Error:', error)
      setResult({
        success: false,
        error: error.message,
        response: error.response?.data
      })
    } finally {
      setLoading(false)
    }
  }

  const testAdminSetup = async () => {
    setLoading(true)
    try {
      const response = await api.get('/AdminSetup/status')
      console.log('Admin Setup Status:', response)
      setResult({
        success: true,
        data: response,
        message: 'Admin setup status retrieved'
      })
    } catch (error: any) {
      console.error('Admin Setup Error:', error)
      setResult({
        success: false,
        error: error.message,
        response: error.response?.data
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">API Test Page</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testAdminSetup}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Admin Setup Status'}
        </button>
        
        <button
          onClick={testAdminLogin}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50 ml-4"
        >
          {loading ? 'Testing...' : 'Test Admin Login'}
        </button>
      </div>

      {result && (
        <div className="mt-6">
          <h2 className="text-lg font-semibold mb-2">API Response:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

export default ApiTest
