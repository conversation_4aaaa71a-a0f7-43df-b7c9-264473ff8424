import { api } from './api'

export interface AdminCredentials {
  email: string
  password: string
}

export interface AdminUser {
  id: number
  email: string
  role: string
  firstName?: string
  lastName?: string
}

export interface AdminLoginResponse {
  success: boolean
  token: string
  user: AdminUser
  message?: string
}

export interface AdminSetupStatus {
  isSetupComplete: boolean
  hasDefaultPassword: boolean
  requiresPasswordChange: boolean
}

export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface DashboardStats {
  totalApplications: number
  pending: number
  approved: number
  rejected: number
  inProgress: number
  monthlyTrend: number[]
  positionBreakdown: Record<string, number>
  recentApplications: any[]
  systemHealth: {
    status: 'Good' | 'Warning' | 'Critical'
    uptime: string
    lastBackup: string
  }
}

// Admin Authentication Service
export const AdminAuthService = {
  // Check if admin is authenticated
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('adminToken')
    const user = localStorage.getItem('adminUser')
    return !!(token && user)
  },

  // Get current admin user
  getCurrentUser: (): AdminUser | null => {
    const userStr = localStorage.getItem('adminUser')
    return userStr ? JSON.parse(userStr) : null
  },

  // Get auth token
  getToken: (): string | null => {
    return localStorage.getItem('adminToken')
  },

  // Login admin
  login: async (credentials: AdminCredentials): Promise<AdminLoginResponse> => {
    console.log('AdminAuthService.login called with:', credentials)
    
    try {
      const response = await api.post<AdminLoginResponse>('/auth/admin/login', credentials)
      
      console.log('Admin Login API Response:', response)
      
      // Handle response data structure
      const responseData = response.data || response
      
      // Check if response has success field (ApiResponse structure)
      if ('success' in responseData && responseData.success && 'data' in responseData) {
        console.log('Using ApiResponse structure with data field')
        const data = (responseData as any).data
        const { token, user } = data
        
        if (!token || !user) {
          throw new Error('Invalid response: missing token or user data')
        }
        
        // Store authentication data
        localStorage.setItem('adminToken', token)
        localStorage.setItem('adminUser', JSON.stringify(user))
        
        return {
          success: true,
          token: token,
          user: user,
          message: (responseData as any).message || 'Login successful'
        }
      }
      // Check if response directly contains token field
      else if ('token' in responseData && 'user' in responseData) {
        console.log('Using direct response structure')
        const { token, user } = responseData as AdminLoginResponse
        
        if (!token || !user) {
          throw new Error('Invalid response: missing token or user data')
        }
        
        // Store authentication data
        localStorage.setItem('adminToken', token)
        localStorage.setItem('adminUser', JSON.stringify(user))
        
        return {
          success: true,
          token: token,
          user: user,
          message: responseData.message || 'Login successful'
        }
      } else {
        throw new Error('Invalid response format: missing required fields')
      }
      
    } catch (error: any) {
      console.error('Admin login error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Login failed')
    }
  },

  // Logout admin
  logout: (): void => {
    localStorage.removeItem('adminToken')
    localStorage.removeItem('adminUser')
    window.location.href = '/admin/login'
  },

  // Verify token
  verifyToken: async (): Promise<boolean> => {
    try {
      const response = await api.get('/auth/admin/verify')
      return response.success || false
    } catch (error) {
      AdminAuthService.logout()
      return false
    }
  },

  // Check admin setup status
  checkSetupStatus: async (): Promise<AdminSetupStatus> => {
    try {
      const response = await api.get<AdminSetupStatus>('/auth/admin/setup/status')
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to check setup status')
    }
  },

  // Initialize admin (first-time setup)
  initializeAdmin: async (): Promise<AdminLoginResponse> => {
    try {
      const response = await api.post<AdminLoginResponse>('/auth/admin/setup/initialize')
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to initialize admin')
    }
  },

  // Change password
  changePassword: async (passwordData: PasswordChangeRequest): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await api.post<{ success: boolean; message: string }>('/auth/admin/setup/change-password', passwordData)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to change password')
    }
  }
}

// Admin API Service
export const AdminAPIService = {
  // Dashboard APIs
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      const response = await api.get<DashboardStats>('/Reports/dashboard')
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard stats')
    }
  },

  // Applications APIs
  getApplications: async (params?: {
    page?: number
    limit?: number
    status?: string
    position?: string
    dateFrom?: string
    dateTo?: string
    search?: string
  }) => {
    try {
      const response = await api.get('/Applications', params)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch applications')
    }
  },

  getApplicationById: async (id: string) => {
    try {
      const response = await api.get(`/Applications/${id}`)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch application')
    }
  },

  updateApplicationStatus: async (id: string, statusData: {
    status: string
    comments?: string
    assignedTo?: string
  }) => {
    try {
      const response = await api.put(`/Applications/${id}/status`, statusData)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update application status')
    }
  },

  // User Management APIs
  getUsers: async (params?: {
    page?: number
    limit?: number
    role?: string
    search?: string
  }) => {
    try {
      const response = await api.get('/Users', params)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users')
    }
  },

  createUser: async (userData: {
    email: string
    firstName: string
    lastName: string
    role: string
    password: string
  }) => {
    try {
      const response = await api.post('/Users', userData)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create user')
    }
  },

  updateUser: async (id: string, userData: Partial<{
    firstName: string
    lastName: string
    email: string
    role: string
    isActive: boolean
  }>) => {
    try {
      const response = await api.put(`/Users/<USER>
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update user')
    }
  },

  deleteUser: async (id: string) => {
    try {
      const response = await api.delete(`/Users/<USER>
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete user')
    }
  },

  // Reports APIs
  getApplicationReports: async (params?: {
    dateFrom?: string
    dateTo?: string
    position?: string
    status?: string
    groupBy?: 'position' | 'status' | 'date'
  }) => {
    try {
      const response = await api.get('/Reports/applications', params)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch application reports')
    }
  },

  exportReport: async (type: 'csv' | 'excel', params?: {
    dateFrom?: string
    dateTo?: string
    position?: string
    status?: string
  }) => {
    try {
      const response = await api.get(`/Reports/export/${type}`, {
        ...params,
        responseType: 'blob'
      })
      return response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to export report')
    }
  },

  // System Health APIs
  getSystemHealth: async () => {
    try {
      const response = await api.get('/System/health')
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch system health')
    }
  },

  // Audit Log APIs
  getAuditLogs: async (params?: {
    page?: number
    limit?: number
    userId?: string
    action?: string
    dateFrom?: string
    dateTo?: string
  }) => {
    try {
      const response = await api.get('/Audit/logs', params)
      return response.data || response
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch audit logs')
    }
  }
}
