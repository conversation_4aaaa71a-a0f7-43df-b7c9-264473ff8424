using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using pmcrms_api.Application.DTOs.Auth;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using pmcrms_api.Infrastructure.Data;
using pmcrms_api.Infrastructure.Services;
using System.Net;
using System.Security.Claims;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Administrative operations and user management
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
[Tags("Administration")]
[Produces("application/json")]
public class AdminController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEmailService _emailService;
    private readonly ApplicationDbContext _context;

    public AdminController(IAuthService authService, IUnitOfWork unitOfWork, IEmailService emailService, ApplicationDbContext context)
    {
        _authService = authService;
        _unitOfWork = unitOfWork;
        _emailService = emailService;
        _context = context;
    }

    /// <summary>
    /// Invite a new officer to the system
    /// </summary>
    /// <param name="request">Officer invitation details</param>
    /// <returns>Success message when officer is invited</returns>
    /// <response code="200">Officer invited successfully</response>
    /// <response code="400">Invalid request data or user already exists</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officers/invite")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> InviteOfficer([FromBody] InviteOfficerRequest request)
    {
        var userRepository = _unitOfWork.Repository<User>();
        var roleRepository = _unitOfWork.Repository<Role>();

        var existingUsers = await userRepository.FindAsync(u => u.Email == request.Email);
        if (existingUsers.Any())
        {
            return BadRequest(new { message = "User with this email already exists" });
        }

        var roles = await roleRepository.FindAsync(r => r.Name == request.Role);
        var role = roles.FirstOrDefault();
        if (role == null)
        {
            return BadRequest(new { message = "Invalid role" });
        }

        var user = new User
        {
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PhoneNumber = request.PhoneNumber,
            RoleId = role.Id,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            // Set a temporary password or leave it empty until the user sets it
            Password = _authService.HashPassword(Guid.NewGuid().ToString())
        };

        await userRepository.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();

        // Generate a password reset token (for security)
        var resetToken = Guid.NewGuid().ToString("N");
        
        // Store the reset token in the database with expiry (24 hours)
        user.ResetToken = resetToken;
        user.ResetTokenExpiry = DateTime.UtcNow.AddHours(24);
        await _unitOfWork.SaveChangesAsync();
        
        try
        {
            await SendInvitationEmailAsync(user.Email, user.FirstName, user.LastName, role.Name, resetToken);
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the invitation
            // The user is created even if email fails
            Console.WriteLine($"Failed to send invitation email: {ex.Message}");
        }

        return Ok(new { message = "Officer invited successfully" });
    }

    /// <summary>
    /// Get all officers in the system
    /// </summary>
    /// <returns>List of all officers with their details</returns>
    /// <response code="200">Officers retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("officers")]
    [ProducesResponseType(typeof(IEnumerable<object>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetOfficers()
    {
        try
        {
            // Officers who have set a valid password (not empty or placeholder)
            var officers = await _context.Users
                .Include(u => u.Role)
                .Where(u => u.Role != null &&
                           u.Role.Name != "User" &&
                           u.Role.Name != "Admin" &&
                           u.Role.Name != "Guest" &&
                           !string.IsNullOrEmpty(u.Password) &&
                           u.Password != "" &&
                           u.Password != null &&
                           u.ResetToken == null) // Officers who have completed password setup
                .ToListAsync();

            var officerData = officers.Select(o => new
            {
                id = o.Id,
                email = o.Email,
                firstName = o.FirstName,
                lastName = o.LastName,
                phoneNumber = o.PhoneNumber,
                isActive = o.IsActive,
                role = o.Role.Name,
                createdAt = o.CreatedAt,
                lastLogin = o.LastLogin,
                department = "N/A",
                assignedApplicationsCount = 0
            }).ToList();

            return Ok(new { success = true, data = officerData, message = "Officers retrieved successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to retrieve officers", error = ex.Message });
        }
    }

    /// <summary>
    /// Get pending officer invitations (officers who haven't completed setup)
    /// </summary>
    /// <returns>List of pending officer invitations</returns>
    /// <response code="200">Pending invitations retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("officers/pending")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetPendingInvitations()
    {
        try
        {
            // Officers who have not set a valid password (still pending setup)
            var pendingOfficers = await _context.Users
                .Include(u => u.Role)
                .Where(u => u.Role != null &&
                           u.Role.Name != "User" &&
                           u.Role.Name != "Admin" &&
                           u.Role.Name != "Guest" &&
                           (string.IsNullOrEmpty(u.Password) ||
                            u.Password == "" ||
                            u.Password == null ||
                            u.FirstName == "Officer" ||
                            u.LastName == "User"))
                .ToListAsync();

            var pendingData = pendingOfficers.Select(o => new
            {
                id = o.Id,
                email = o.Email,
                role = o.Role.Name,
                createdAt = o.CreatedAt,
                status = "Pending Setup"
            }).ToList();

            return Ok(new { success = true, data = pendingData, message = "Pending invitations retrieved successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to retrieve pending invitations", error = ex.Message });
        }
    }

    /// <summary>
    /// Reinvite an officer who hasn't completed setup
    /// </summary>
    /// <param name="id">Officer ID to reinvite</param>
    /// <returns>Success message when reinvitation is sent</returns>
    /// <response code="200">Reinvitation sent successfully</response>
    /// <response code="404">Officer not found</response>
    /// <response code="400">Officer has already completed setup</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officers/{id}/reinvite")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ReinviteOfficer(int id)
    {
        try
        {
            var officer = await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == id);

            if (officer == null)
            {
                return NotFound(new { message = "Officer not found" });
            }

            // Check if officer has already completed setup
            if (officer.FirstName != "Officer" && officer.LastName != "User" && officer.LastLogin != null)
            {
                return BadRequest(new { message = "Officer has already completed setup" });
            }

            // Generate new reset token
            var resetToken = Guid.NewGuid().ToString("N");
            
            // Store the reset token in the database with expiry (24 hours)
            officer.ResetToken = resetToken;
            officer.ResetTokenExpiry = DateTime.UtcNow.AddHours(24);
            await _unitOfWork.SaveChangesAsync();
            
            // Send reinvitation email
            await SendInvitationEmailAsync(officer.Email, officer.FirstName, officer.LastName, officer.Role.Name, resetToken);

            return Ok(new { message = "Reinvitation sent successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to send reinvitation", error = ex.Message });
        }
    }

    /// <summary>
    /// Deactivate an officer account
    /// </summary>
    /// <param name="id">Officer ID to deactivate</param>
    /// <returns>Success message when officer is deactivated</returns>
    /// <response code="200">Officer deactivated successfully</response>
    /// <response code="400">Cannot deactivate admin user</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("officers/{id}/deactivate")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> DeactivateOfficer(int id)
    {
        var userRepository = _unitOfWork.Repository<User>();
        var user = await userRepository.GetByIdAsync(id);

        if (user == null)
        {
            return NotFound(new { message = "Officer not found" });
        }

        if (user.Role.Name == "Admin")
        {
            return BadRequest(new { message = "Cannot deactivate admin user" });
        }

        user.IsActive = false;
        userRepository.Update(user);
        await _unitOfWork.SaveChangesAsync();

        return Ok(new { message = "Officer deactivated successfully" });
    }

    /// <summary>
    /// Send invitation email to newly created officer
    /// </summary>
    private async Task SendInvitationEmailAsync(string email, string firstName, string lastName, string role, string resetToken)
    {
        var subject = "Welcome to PMCRMS - Officer Invitation";
        
        var setupLink = $"http://localhost:3000/officer/setup-password?token={resetToken}&email={Uri.EscapeDataString(email)}";
        
        var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif; color: #333;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #2563eb;'>Welcome to PMCRMS Portal</h2>
                    
                    <p>Dear {firstName} {lastName},</p>
                    
                    <p>You have been invited to join the PMCRMS (Project Management Construction Resource Management System) as a <strong>{role}</strong>.</p>
                    
                    <div style='background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                        <h3 style='margin-top: 0; color: #1e40af;'>Your Account Details:</h3>
                        <p><strong>Email:</strong> {email}</p>
                        <p><strong>Role:</strong> {role}</p>
                    </div>
                    
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{setupLink}' 
                           style='display: inline-block; padding: 15px 30px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;'>
                            Set Up Your Password
                        </a>
                    </div>
                    
                    <h3>Setup Instructions:</h3>
                    <ol>
                        <li>Click the ""Set Up Your Password"" button above</li>
                        <li>Create a secure password for your account</li>
                        <li>Complete your account setup</li>
                        <li>Start using the PMCRMS portal with your new credentials</li>
                    </ol>
                    
                    <p><strong>Note:</strong> This setup link will expire in 24 hours for security reasons. If you don't complete the setup within this time, please contact your administrator for a new invitation.</p>
                    
                    <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                    <p style='background-color: #f3f4f6; padding: 10px; word-break: break-all; font-family: monospace;'>{setupLink}</p>
                    
                    <p style='margin-top: 30px;'>If you have any questions or need assistance, please contact the system administrator.</p>
                    
                    <hr style='border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;'>
                    <p style='font-size: 12px; color: #6b7280;'>
                        This is an automated message from PMCRMS Portal. Please do not reply to this email.
                        <br>For security reasons, do not share this setup link with anyone.
                    </p>
                </div>
            </body>
            </html>";

        await _emailService.SendEmailAsync(email, subject, body, true);
    }

    /// <summary>
    /// Manually advance applications to next workflow state (for testing/admin purposes)
    /// </summary>
    /// <param name="applicationId">Application ID to advance</param>
    /// <param name="request">Request containing target state and comments</param>
    /// <returns>Result of the state transition</returns>
    /// <response code="200">Application state advanced successfully</response>
    /// <response code="404">Application not found</response>
    /// <response code="400">Invalid state transition</response>
    [HttpPost("applications/{applicationId:int}/advance-state")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> AdvanceApplicationState(int applicationId, [FromBody] AdvanceStateRequest? request = null)
    {
        try
        {
            var application = await _context.Applications
                .Include(a => a.WorkflowState)
                .Include(a => a.AssignedTo)
                .FirstOrDefaultAsync(a => a.Id == applicationId);

            if (application == null)
            {
                return NotFound(new { message = "Application not found" });
            }

            // Determine target state
            WorkflowStateType targetState;
            if (request?.ToState != null)
            {
                targetState = request.ToState.Value;
            }
            else
            {
                // Auto-advance to next logical state
                targetState = application.CurrentState switch
                {
                    WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => WorkflowStateType.JUNIOR_ENGINEER_PENDING,
                    WorkflowStateType.JUNIOR_ENGINEER_PENDING => WorkflowStateType.ASSISTANT_ENGINEER_PENDING,
                    WorkflowStateType.ASSISTANT_ENGINEER_PENDING => WorkflowStateType.EXECUTIVE_ENGINEER_PENDING,
                    WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => WorkflowStateType.CITY_ENGINEER_PENDING,
                    WorkflowStateType.CITY_ENGINEER_PENDING => WorkflowStateType.PAYMENT_PENDING,
                    WorkflowStateType.PAYMENT_PENDING => WorkflowStateType.CLERK_PENDING,
                    WorkflowStateType.CLERK_PENDING => WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING,
                    WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING => WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING,
                    WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING => WorkflowStateType.FINAL_APPROVE,
                    _ => application.CurrentState // No change if already at final state
                };
            }

            if (targetState == application.CurrentState)
            {
                return Ok(new 
                { 
                    success = true, 
                    message = "Application is already in the target state",
                    currentState = application.CurrentState,
                    stateName = application.CurrentState.ToString()
                });
            }

            // Find the target workflow state by mapping the enum to the correct state
            var targetWorkflowState = await _context.WorkflowStates
                .FirstOrDefaultAsync(ws => ws.Name == MapWorkflowStateTypeToName(targetState));

            if (targetWorkflowState == null)
            {
                return BadRequest(new { message = $"Target workflow state {targetState} not found in database" });
            }

            var previousState = application.CurrentState;

            // Update application state
            application.CurrentState = targetState;
            application.WorkflowStateId = targetWorkflowState.Id;

            // Add to workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                ApplicationId = application.Id,
                FromState = previousState,
                ToState = targetState,
                ActionByUserId = GetCurrentUserId(),
                Remarks = request?.Comments ?? $"Admin advanced application from {previousState} to {targetState}",
                TransitionDate = DateTime.UtcNow
            });

            await _context.SaveChangesAsync();

            return Ok(new 
            { 
                success = true, 
                message = $"Application advanced from {previousState} to {targetState}",
                data = new
                {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    previousState = previousState,
                    newState = targetState,
                    newStateName = targetState.ToString(),
                    advancedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to advance application state", error = ex.Message });
        }
    }

    private string MapWorkflowStateTypeToName(WorkflowStateType stateType)
    {
        return stateType switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => "DOCUMENT_VERIFICATION_PENDING",
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => "JUNIOR_ENGINEER_PENDING",
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING => "ASSISTANT_ENGINEER_PENDING",
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => "EXECUTIVE_ENGINEER_PENDING",
            WorkflowStateType.CITY_ENGINEER_PENDING => "CITY_ENGINEER_PENDING",
            WorkflowStateType.PAYMENT_PENDING => "PAYMENT_PENDING",
            WorkflowStateType.CLERK_PENDING => "CLERK_PENDING",
            WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING => "EXECUTIVE_DIGITAL_SIGNATURE_PENDING",
            WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING => "CITY_DIGITAL_SIGNATURE_PENDING",
            WorkflowStateType.FINAL_APPROVE => "FINAL_APPROVE",
            WorkflowStateType.REJECTED => "REJECTED",
            _ => stateType.ToString()
        };
    }

    /// <summary>
    /// Get all applications with their current states (for admin overview)
    /// </summary>
    /// <returns>List of all applications with their states</returns>
    /// <response code="200">Applications retrieved successfully</response>
    [HttpGet("applications")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetAllApplications()
    {
        try
        {
            var applications = await _context.Applications
                .Include(a => a.WorkflowState)
                .Include(a => a.AssignedTo)
                .Include(a => a.User)
                .Select(a => new
                {
                    a.Id,
                    a.ApplicationNumber,
                    ApplicantName = $"{a.FirstName} {a.MiddleName} {a.LastName}".Trim(),
                    a.EmailAddress,
                    a.Position,
                    CurrentState = a.CurrentState,
                    CurrentStateName = a.CurrentState.ToString(),
                    WorkflowState = a.WorkflowState != null ? new { a.WorkflowState.Name, a.WorkflowState.Description } : null,
                    AssignedTo = a.AssignedTo != null ? $"{a.AssignedTo.FirstName} {a.AssignedTo.LastName}" : null,
                    a.SubmissionDate,
                    a.AssignedAt
                })
                .OrderByDescending(a => a.SubmissionDate)
                .ToListAsync();

            return Ok(new 
            { 
                success = true, 
                data = applications,
                count = applications.Count,
                message = "Applications retrieved successfully" 
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to retrieve applications", error = ex.Message });
        }
    }

    /// <summary>
    /// Assign unassigned applications to appropriate engineers
    /// </summary>
    /// <returns>Result of the assignment operation</returns>
    /// <response code="200">Applications assigned successfully</response>
    [HttpPost("applications/auto-assign")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AutoAssignApplications()
    {
        try
        {
            // Get all unassigned applications in DOCUMENT_VERIFICATION_PENDING state
            var unassignedApplications = await _context.Applications
                .Where(a => a.AssignedToUserId == null && 
                           a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING)
                .ToListAsync();

            var assignments = new List<object>();

            foreach (var application in unassignedApplications)
            {
                // Get the appropriate role for this position
                var targetRole = GetJuniorEngineerRoleForPosition(application.Position);
                
                // Find available engineers with this role
                var availableEngineer = await _context.Users
                    .Include(u => u.Role)
                    .Where(u => u.Role.Name == targetRole && u.IsActive)
                    .FirstOrDefaultAsync();

                if (availableEngineer != null)
                {
                    application.AssignedToUserId = availableEngineer.Id;
                    application.AssignedAt = DateTime.UtcNow;

                    assignments.Add(new
                    {
                        applicationId = application.Id,
                        applicationNumber = application.ApplicationNumber,
                        position = application.Position.ToString(),
                        assignedTo = $"{availableEngineer.FirstName} {availableEngineer.LastName}",
                        assignedRole = availableEngineer.Role.Name
                    });
                }
            }

            if (assignments.Any())
            {
                await _context.SaveChangesAsync();
            }

            return Ok(new 
            { 
                success = true, 
                message = $"Assigned {assignments.Count} applications to appropriate engineers",
                assignments = assignments,
                unassignedCount = unassignedApplications.Count - assignments.Count
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to auto-assign applications", error = ex.Message });
        }
    }

    private string GetJuniorEngineerRoleForPosition(PositionType position)
    {
        return position switch
        {
            PositionType.Architect => "JRENGG_ARCH",
            PositionType.StructuralEngineer => "JRENGG_STRU", 
            PositionType.LicenseEngineer => "JRENGG_LICE",
            PositionType.Supervisor1 => "JRENGG_SUPER1",
            PositionType.Supervisor2 => "JRENGG_SUPER2",
            _ => "JRENGG_ARCH" // Default fallback
        };
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 1; // Default to admin user if not found
    }
}

public class AdvanceStateRequest
{
    public WorkflowStateType? ToState { get; set; }
    public string? Comments { get; set; }
}
