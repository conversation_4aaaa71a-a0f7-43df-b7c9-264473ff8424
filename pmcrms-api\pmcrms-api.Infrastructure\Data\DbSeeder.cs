using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using BCrypt.Net;

namespace pmcrms_api.Infrastructure.Data;

public static class DbSeeder
{
    public static async Task InitializeDatabaseAsync(IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;

        try
        {
            var context = services.GetRequiredService<ApplicationDbContext>();
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
            
            // Ensure database is created and migrations are applied
            logger.LogInformation("Ensuring database exists and applying migrations...");
            await context.Database.MigrateAsync();
            
            // Only seed if database connection is working
            if (await context.Database.CanConnectAsync())
            {
                logger.LogInformation("Database connection successful. Seeding data...");
                await SeedRolesAsync(context, logger);
                await SeedWorkflowStatesAsync(context, logger);
                await SeedAdminUserAsync(context, logger);
            }
            else
            {
                logger.LogWarning("Cannot connect to database. Skipping seeding.");
            }
        }
        catch (Exception ex)
        {
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
            logger.LogError(ex, "An error occurred while initializing the database.");
            // Don't throw - let the app start even if database is not available
        }
    }

    private static async Task SeedRolesAsync(ApplicationDbContext context, ILogger logger)
    {
        try
        {
            // Check if Roles table exists and has data
            if (!await context.Roles.AnyAsync())
            {
                logger.LogInformation("Seeding roles...");
                var roles = Enum.GetNames(typeof(RoleType))
                    .Select(role => new Role
                    {
                        Name = role,
                        Description = $"{role} role"
                    });

                await context.Roles.AddRangeAsync(roles);
                await context.SaveChangesAsync();
                logger.LogInformation("Roles seeded successfully.");
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't throw - the app can still start without seeded data
            logger.LogError(ex, "Error seeding roles: {Message}", ex.Message);
        }
    }

    private static async Task SeedWorkflowStatesAsync(ApplicationDbContext context, ILogger logger)
    {
        try
        {
            // Check if WorkflowStates table exists and has data
            if (!await context.WorkflowStates.AnyAsync())
            {
                logger.LogInformation("Seeding workflow states...");
                
                var workflowStates = new[]
                {
                    new WorkflowState { Name = "DOCUMENT_VERIFICATION_PENDING", Description = "Document verification pending", Order = 1, IsFinal = false },
                    new WorkflowState { Name = "JUNIOR_ENGINEER_PENDING", Description = "Junior Engineer review pending", Order = 2, IsFinal = false },
                    new WorkflowState { Name = "ASSISTANT_ENGINEER_PENDING", Description = "Assistant Engineer review pending", Order = 3, IsFinal = false },
                    new WorkflowState { Name = "EXECUTIVE_ENGINEER_PENDING", Description = "Executive Engineer review pending", Order = 4, IsFinal = false },
                    new WorkflowState { Name = "CITY_ENGINEER_PENDING", Description = "City Engineer review pending", Order = 5, IsFinal = false },
                    new WorkflowState { Name = "PAYMENT_PENDING", Description = "Payment pending", Order = 6, IsFinal = false },
                    new WorkflowState { Name = "CLERK_PENDING", Description = "Clerk approval pending", Order = 7, IsFinal = false },
                    new WorkflowState { Name = "EXECUTIVE_DIGITAL_SIGNATURE_PENDING", Description = "Executive Engineer digital signature pending", Order = 8, IsFinal = false },
                    new WorkflowState { Name = "CITY_DIGITAL_SIGNATURE_PENDING", Description = "City Engineer digital signature pending", Order = 9, IsFinal = false },
                    new WorkflowState { Name = "FINAL_APPROVE", Description = "Final approval - Certificate issued", Order = 10, IsFinal = true },
                    new WorkflowState { Name = "REJECTED", Description = "Application rejected", Order = 11, IsFinal = true }
                };

                await context.WorkflowStates.AddRangeAsync(workflowStates);
                await context.SaveChangesAsync();
                logger.LogInformation("Workflow states seeded successfully.");
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't throw - the app can still start without seeded data
            logger.LogError(ex, "Error seeding workflow states: {Message}", ex.Message);
        }
    }

    private static async Task SeedAdminUserAsync(ApplicationDbContext context, ILogger logger)
    {
        try
        {
            // Check if admin user exists
            var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "Admin");
            if (adminRole == null)
            {
                logger.LogWarning("Admin role not found. Cannot create admin user.");
                return;
            }

            var adminExists = await context.Users.AnyAsync(u => u.Email == "<EMAIL>");
            if (!adminExists)
            {
                logger.LogInformation("Creating default admin user...");
                
                var adminUser = new User
                {
                    FirstName = "System",
                    LastName = "Administrator",
                    Email = "<EMAIL>",
                    Password = BCrypt.Net.BCrypt.HashPassword("Admin@123!"), // Default admin password
                    PhoneNumber = "+91-9999999999",
                    RoleId = adminRole.Id,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                await context.Users.AddAsync(adminUser);
                await context.SaveChangesAsync();
                
                logger.LogInformation("Admin user created successfully:");
                logger.LogInformation("Email: <EMAIL>");
                logger.LogInformation("Default Password: Admin@123!");
                logger.LogWarning("IMPORTANT: Please change the default admin password after first login!");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error seeding admin user: {Message}", ex.Message);
        }
    }
}
