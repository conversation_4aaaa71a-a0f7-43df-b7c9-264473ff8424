using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

public class Appointment
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Title { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    public DateTime AppointmentDate { get; set; }

    public DateTime CreatedAt { get; set; }

    [Required]
    [StringLength(50)]
    public string Status { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Location { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;

    public int UserId { get; set; }
    public User User { get; set; } = null!;
}
