import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Search, ArrowLeft, FileText, Download, Loader2, CheckCircle, Clock, XCircle, AlertCircle } from 'lucide-react'
import { useGuestAuth } from '../../hooks/useGuestAuth'
import { guestAuth } from '../../lib/api'
import Logo from '../../components/Logo'

// Validation schema
const applicationSearchSchema = z.object({
  applicationNumber: z.string().min(1, 'Application number is required'),
})

type ApplicationSearchForm = z.infer<typeof applicationSearchSchema>

// Status badge component
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'FINAL_APPROVE':
        return { icon: CheckCircle, color: 'text-green-600 bg-green-100', label: 'Approved' }
      case 'REJECTED':
        return { icon: XCircle, color: 'text-red-600 bg-red-100', label: 'Rejected' }
      case 'PAYMENT_PENDING':
        return { icon: AlertCircle, color: 'text-amber-600 bg-amber-100', label: 'Payment Pending' }
      default:
        return { icon: Clock, color: 'text-blue-600 bg-blue-100', label: 'In Progress' }
    }
  }

  const config = getStatusConfig(status)
  const IconComponent = config.icon

  return (
    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
      <IconComponent className="h-4 w-4" />
      <span>{config.label}</span>
    </div>
  )
}

const ApplicationStatus: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [applicationData, setApplicationData] = useState<any>(null)
  const { guestSession, isGuestAuthenticated } = useGuestAuth()

  const form = useForm<ApplicationSearchForm>({
    resolver: zodResolver(applicationSearchSchema),
    defaultValues: {
      applicationNumber: '',
    },
  })

  const handleSearch = async (data: ApplicationSearchForm) => {
    if (!isGuestAuthenticated || !guestSession) {
      // Redirect to guest login
      window.location.href = '/guest'
      return
    }

    try {
      setLoading(true)
      const response = await guestAuth.getApplicationStatus(data.applicationNumber, guestSession.accessToken)
      setApplicationData(response.data)
    } catch (error: any) {
      console.error('Error fetching application status:', error)
      form.setError('applicationNumber', {
        type: 'manual',
        message: error.response?.data?.message || 'Application not found or access denied',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadCertificate = async () => {
    if (!isGuestAuthenticated || !guestSession || !applicationData) return

    try {
      await guestAuth.downloadCertificate(applicationData.applicationNumber, guestSession.accessToken)
    } catch (error: any) {
      console.error('Error downloading certificate:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl space-y-6">
        {/* Search Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Logo size="lg" showText={false} />
            </div>
            <CardTitle className="text-2xl">Application Status Tracking</CardTitle>
            <CardDescription>
              {isGuestAuthenticated 
                ? `Welcome back, ${guestSession?.email}. Enter your application number to check status.`
                : 'Check your application progress and workflow status'
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {!isGuestAuthenticated ? (
              <div className="text-center space-y-4">
                <p className="text-gray-600">
                  Please login as a guest to check your application status
                </p>
                <Link to="/guest">
                  <Button className="w-full">
                    Guest Login
                  </Button>
                </Link>
              </div>
            ) : (
              <form onSubmit={form.handleSubmit(handleSearch)} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="applicationNumber" className="text-sm font-medium text-gray-700">
                    Application Number
                  </label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="applicationNumber"
                      type="text"
                      placeholder="Enter your application number"
                      className="pl-10"
                      {...form.register('applicationNumber')}
                    />
                  </div>
                  {form.formState.errors.applicationNumber && (
                    <p className="text-sm text-red-600">
                      {form.formState.errors.applicationNumber.message}
                    </p>
                  )}
                </div>
                
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Check Status
                    </>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Application Details Card */}
        {applicationData && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Application Details</CardTitle>
                  <CardDescription>
                    Application #{applicationData.applicationNumber}
                  </CardDescription>
                </div>
                <StatusBadge status={applicationData.status} />
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900">Position Type</h4>
                  <p className="text-gray-600">{applicationData.positionType}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Submission Date</h4>
                  <p className="text-gray-600">{new Date(applicationData.createdAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Current Stage</h4>
                  <p className="text-gray-600">{applicationData.status.replace(/_/g, ' ')}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Last Updated</h4>
                  <p className="text-gray-600">{new Date(applicationData.updatedAt).toLocaleDateString()}</p>
                </div>
              </div>

              {/* Download Certificate */}
              {applicationData.status === 'FINAL_APPROVE' && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">Certificate Ready</h4>
                  <Button onClick={handleDownloadCertificate} className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    Download Certificate
                  </Button>
                </div>
              )}

              {/* Comments/Messages */}
              {applicationData.comments && applicationData.comments.length > 0 && (
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">Officer Comments</h4>
                  <div className="space-y-2">
                    {applicationData.comments.map((comment: any, index: number) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm text-gray-600">{comment.message}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {comment.officerRole} • {new Date(comment.createdAt).toLocaleString()}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-3">
              <h3 className="font-medium text-gray-900">Need Help?</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Link to="/apply">
                  <Button variant="outline" className="w-full">
                    Submit New Application
                  </Button>
                </Link>
                <Link to="/">
                  <Button variant="outline" className="w-full">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default ApplicationStatus
