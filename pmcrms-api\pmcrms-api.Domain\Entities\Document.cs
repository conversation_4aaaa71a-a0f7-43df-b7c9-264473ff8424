using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

public class Document
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string DocumentType { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string FilePath { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string FileType { get; set; } = string.Empty;

    [Required]
    public long FileSize { get; set; }

    public DateTime UploadDate { get; set; }
    
    [StringLength(200)]
    public string? Description { get; set; }

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;

    public ICollection<DigitalSignature> Signatures { get; set; } = new List<DigitalSignature>();
}
