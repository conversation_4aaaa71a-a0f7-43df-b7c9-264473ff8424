import React, { useState, useCallback } from 'react';
import { CubeProvider } from '@cubejs-client/react';
import cubeApi, { CUBE_QUERIES } from '../../lib/cubeConfig';
import AdminLayout from '../../components/AdminLayout';
import PositionPieChart from '../../components/charts/PositionPieChart';
import WorkflowStageChart from '../../components/charts/WorkflowStageChart';
import { ChevronLeftIcon, FilterIcon, DownloadIcon, RefreshCcwIcon } from 'lucide-react';

interface DrillDownFilters {
  position?: string;
  status?: string;
  dateRange?: string;
  assignedTo?: string;
}

interface ApplicationDetail {
  id: string;
  applicationNumber: string;
  applicantName: string;
  position: string;
  status: string;
  submittedDate: string;
  assignedTo: string;
  phoneNumber: string;
  email: string;
}

const AdminReportsPage: React.FC = () => {
  const [drillDownFilters, setDrillDownFilters] = useState<DrillDownFilters>({});
  const [selectedApplications, setSelectedApplications] = useState<ApplicationDetail[]>([]);
  const [showApplicationDetails, setShowApplicationDetails] = useState(false);
  const [currentView, setCurrentView] = useState<'overview' | 'position-detail' | 'stage-detail'>('overview');
  const [isLoading, setIsLoading] = useState(false);

  // Mock data for demo purposes (replace with real Cube.js queries)
  const mockApplications: ApplicationDetail[] = [
    {
      id: '1',
      applicationNumber: 'PMC-2025-0150',
      applicantName: 'John Doe',
      position: 'Architect',
      status: 'DOCUMENT_VERIFICATION_PENDING',
      submittedDate: '2025-08-20',
      assignedTo: 'Jane Smith',
      phoneNumber: '9876543210',
      email: '<EMAIL>'
    },
    {
      id: '2',
      applicationNumber: 'PMC-2025-0151',
      applicantName: 'Sarah Wilson',
      position: 'Architect',
      status: 'JUNIOR_ENGINEER_PENDING',
      submittedDate: '2025-08-19',
      assignedTo: 'Mike Johnson',
      phoneNumber: '9876543211',
      email: '<EMAIL>'
    }
  ];

  const handlePositionDrillDown = useCallback(async (position: string) => {
    setIsLoading(true);
    setDrillDownFilters({ position });
    setCurrentView('position-detail');
    
    // In real implementation, this would filter applications by position
    const filteredApps = mockApplications.filter(app => app.position === position);
    setSelectedApplications(filteredApps);
    setIsLoading(false);
  }, []);

  const handleStageDrillDown = useCallback(async (status: string, position?: string) => {
    setIsLoading(true);
    const filters: DrillDownFilters = { status };
    if (position) filters.position = position;
    
    setDrillDownFilters(filters);
    setCurrentView('stage-detail');
    setShowApplicationDetails(true);
    
    // In real implementation, this would filter applications by status and position
    let filteredApps = mockApplications.filter(app => app.status === status);
    if (position) {
      filteredApps = filteredApps.filter(app => app.position === position);
    }
    
    setSelectedApplications(filteredApps);
    setIsLoading(false);
  }, []);

  const handleBackToOverview = () => {
    setCurrentView('overview');
    setDrillDownFilters({});
    setSelectedApplications([]);
    setShowApplicationDetails(false);
  };

  const handleRefresh = () => {
    // Refresh current view data
    window.location.reload();
  };

  const handleExport = () => {
    // Export current view data
    const csvContent = selectedApplications.map(app => 
      `${app.applicationNumber},${app.applicantName},${app.position},${app.status},${app.submittedDate},${app.assignedTo}`
    ).join('\n');
    
    const blob = new Blob([`Application Number,Applicant Name,Position,Status,Submitted Date,Assigned To\n${csvContent}`], 
      { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `applications_report_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  // Build dynamic queries based on current filters
  const getPositionQuery = () => {
    return {
      ...CUBE_QUERIES.applicationsByPosition,
      ...(drillDownFilters.dateRange && {
        timeDimensions: [{
          dimension: 'Applications.submittedDate',
          dateRange: drillDownFilters.dateRange
        }]
      })
    };
  };

  const getStageQuery = () => {
    const query = drillDownFilters.position 
      ? CUBE_QUERIES.applicationsByStatusAndPosition
      : CUBE_QUERIES.applicationsByStatus;

    return {
      ...query,
      ...(drillDownFilters.position && {
        filters: [{
          member: 'Applications.position',
          operator: 'equals',
          values: [drillDownFilters.position]
        }]
      })
    };
  };

  const getStatusDisplayName = (status: string) => {
    const displayNames: Record<string, string> = {
      'DOCUMENT_VERIFICATION_PENDING': 'Document Verification',
      'JUNIOR_ENGINEER_PENDING': 'Junior Engineer Review',
      'ASSISTANT_ENGINEER_PENDING': 'Assistant Engineer Review',
      'EXECUTIVE_ENGINEER_PENDING': 'Executive Engineer Review',
      'CITY_ENGINEER_PENDING': 'City Engineer Review',
      'PAYMENT_PENDING': 'Payment Pending',
      'FINAL_APPROVE': 'Approved',
      'REJECTED': 'Rejected'
    };
    return displayNames[status] || status;
  };

  const getStatusBadgeColor = (status: string) => {
    const colors: Record<string, string> = {
      'DOCUMENT_VERIFICATION_PENDING': 'bg-yellow-100 text-yellow-800',
      'JUNIOR_ENGINEER_PENDING': 'bg-blue-100 text-blue-800',
      'ASSISTANT_ENGINEER_PENDING': 'bg-indigo-100 text-indigo-800',
      'EXECUTIVE_ENGINEER_PENDING': 'bg-purple-100 text-purple-800',
      'CITY_ENGINEER_PENDING': 'bg-pink-100 text-pink-800',
      'PAYMENT_PENDING': 'bg-orange-100 text-orange-800',
      'FINAL_APPROVE': 'bg-green-100 text-green-800',
      'REJECTED': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <CubeProvider cubeApi={cubeApi}>
      <AdminLayout>
        <div className="p-6 max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-4">
              {currentView !== 'overview' && (
                <button
                  onClick={handleBackToOverview}
                  className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <ChevronLeftIcon className="h-5 w-5 mr-1" />
                  Back to Overview
                </button>
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Applications Analytics & Reports
                </h1>
                {currentView !== 'overview' && (
                  <p className="text-sm text-gray-600 mt-1">
                    {drillDownFilters.position && `Position: ${drillDownFilters.position}`}
                    {drillDownFilters.status && ` | Status: ${getStatusDisplayName(drillDownFilters.status)}`}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={handleRefresh}
                className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCcwIcon className="h-4 w-4 mr-2" />
                Refresh
              </button>
              
              {selectedApplications.length > 0 && (
                <button
                  onClick={handleExport}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Export ({selectedApplications.length})
                </button>
              )}
            </div>
          </div>

          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          )}

          {/* Overview Charts */}
          {currentView === 'overview' && !isLoading && (
            <div className="space-y-8">
              {/* Summary Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-sm font-medium text-gray-500">Total Applications</h3>
                  <p className="text-3xl font-bold text-gray-900 mt-2">1,250</p>
                  <p className="text-sm text-green-600 mt-1">↗ +12% from last month</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-sm font-medium text-gray-500">Pending Review</h3>
                  <p className="text-3xl font-bold text-yellow-600 mt-2">145</p>
                  <p className="text-sm text-red-600 mt-1">↗ +5% from last month</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-sm font-medium text-gray-500">Approved</h3>
                  <p className="text-3xl font-bold text-green-600 mt-2">980</p>
                  <p className="text-sm text-green-600 mt-1">↗ +8% from last month</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow">
                  <h3 className="text-sm font-medium text-gray-500">Avg. Processing Time</h3>
                  <p className="text-3xl font-bold text-blue-600 mt-2">15.5</p>
                  <p className="text-sm text-green-600 mt-1">↘ -2 days from last month</p>
                </div>
              </div>

              {/* Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <PositionPieChart
                  query={getPositionQuery()}
                  title="Applications by Position"
                  height={400}
                  onDrillDown={handlePositionDrillDown}
                />
                
                <WorkflowStageChart
                  query={getStageQuery()}
                  title="Applications by Workflow Stage"
                  height={400}
                  onDrillDown={handleStageDrillDown}
                />
              </div>
            </div>
          )}

          {/* Position Detail View */}
          {currentView === 'position-detail' && !isLoading && (
            <div className="space-y-8">
              <WorkflowStageChart
                query={getStageQuery()}
                title={`Workflow Stages for ${drillDownFilters.position}`}
                height={400}
                onDrillDown={handleStageDrillDown}
                selectedPosition={drillDownFilters.position}
              />
            </div>
          )}

          {/* Application Details Table */}
          {showApplicationDetails && selectedApplications.length > 0 && !isLoading && (
            <div className="mt-8">
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Application Details ({selectedApplications.length} applications)
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {drillDownFilters.position && `Position: ${drillDownFilters.position}`}
                    {drillDownFilters.status && ` | Status: ${getStatusDisplayName(drillDownFilters.status)}`}
                  </p>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Application
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Applicant
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Submitted
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Assigned To
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Contact
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {selectedApplications.map((app) => (
                        <tr key={app.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-blue-600">
                              {app.applicationNumber}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {app.applicantName}
                            </div>
                            <div className="text-sm text-gray-500">{app.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-800 rounded-full">
                              {app.position}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(app.status)}`}>
                              {getStatusDisplayName(app.status)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(app.submittedDate).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {app.assignedTo}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {app.phoneNumber}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && selectedApplications.length === 0 && showApplicationDetails && (
            <div className="text-center py-12">
              <FilterIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900">No applications found</p>
              <p className="text-gray-600">Try adjusting your filters or date range.</p>
            </div>
          )}
        </div>
      </AdminLayout>
    </CubeProvider>
  );
};

export default AdminReportsPage;
