# Cube.js Environment Configuration
# Copy this file to .env and update with your actual values

# ===== DATABASE CONFIGURATION =====
CUBEJS_DB_TYPE=mssql
CUBEJS_DB_HOST=localhost
CUBEJS_DB_PORT=1433
CUBEJS_DB_NAME=PMCRMSDatabase
CUBEJS_DB_USER=sa
CUBEJS_DB_PASS=your_password_here

# For PostgreSQL (alternative)
# CUBEJS_DB_TYPE=postgres
# CUBEJS_DB_HOST=localhost
# CUBEJS_DB_PORT=5432
# CUBEJS_DB_NAME=pmcrms_db
# CUBEJS_DB_USER=postgres
# CUBEJS_DB_PASS=your_password_here

# ===== CUBE.JS API CONFIGURATION =====
CUBEJS_API_SECRET=your-super-secret-api-key-here-change-in-production
CUBEJS_API_PORT=4000
CUBEJS_API_HOST=0.0.0.0

# ===== CACHE CONFIGURATION =====
# Redis configuration (recommended for production)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ===== PRE-AGGREGATION STORAGE =====
# External database for pre-aggregations (recommended for production)
PREAGG_DB_TYPE=postgres
PREAGG_DB_HOST=localhost
PREAGG_DB_PORT=5432
PREAGG_DB_NAME=pmcrms_preagg
PREAGG_DB_USER=postgres
PREAGG_DB_PASS=your_preagg_password

# ===== ENVIRONMENT SETTINGS =====
NODE_ENV=development
LOG_LEVEL=info
LOG_QUERIES=false

# ===== SECURITY SETTINGS =====
# JWT validation endpoint for your ASP.NET Core API
ASP_NET_API_BASE_URL=https://localhost:7001
JWT_VALIDATION_ENDPOINT=/api/auth/validate-token

# ===== FRONTEND CORS ORIGINS =====
FRONTEND_URL=http://localhost:3000
ADDITIONAL_CORS_ORIGINS=http://localhost:8080,https://your-domain.com

# ===== PERFORMANCE SETTINGS =====
CUBEJS_CONCURRENCY=2
CUBEJS_CACHE_AND_QUEUE_DRIVER=redis
CUBEJS_REFRESH_WORKER_MODE=true
CUBEJS_ROLLUP_ONLY=false

# ===== MONITORING & TELEMETRY =====
CUBEJS_TELEMETRY=false
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_PORT=4001

# ===== DEVELOPMENT SETTINGS =====
CUBEJS_DEV_MODE=true
CUBEJS_WEB_SOCKETS=true
CUBEJS_PLAYGROUND_ENABLED=true
CUBEJS_PLAYGROUND_AUTH_SECRET=playground-auth-secret

# ===== PRODUCTION OVERRIDES =====
# Uncomment these for production deployment
# NODE_ENV=production
# CUBEJS_DEV_MODE=false
# CUBEJS_PLAYGROUND_ENABLED=false
# LOG_LEVEL=warn
# CUBEJS_TELEMETRY=true

# ===== SSL CONFIGURATION (if needed) =====
# CUBEJS_SSL_CERT=./ssl/server.crt
# CUBEJS_SSL_KEY=./ssl/server.key

# ===== RATE LIMITING =====
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_MESSAGE="Too many requests, please try again later"

# ===== CUSTOM HEADERS =====
CUSTOM_HEADER_NAME=X-PMCRMS-Analytics
CUSTOM_HEADER_VALUE=v1.0.0
