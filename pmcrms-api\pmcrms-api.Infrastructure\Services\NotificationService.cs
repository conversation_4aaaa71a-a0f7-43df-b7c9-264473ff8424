using Microsoft.Extensions.Configuration;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Models;
using DomainEntities = pmcrms_api.Domain.Entities;

namespace pmcrms_api.Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IEmailService _emailService;
        private readonly IPDFService _pdfService;
        private readonly IConfiguration _configuration;

        public NotificationService(
            IEmailService emailService,
            IPDFService pdfService,
            IConfiguration configuration)
        {
            _emailService = emailService;
            _pdfService = pdfService;
            _configuration = configuration;
        }

        public async Task NotifyApplicationSubmissionAsync(DomainEntities.Application application)
        {
            var subject = $"Application Submitted Successfully - {application.ApplicationNumber}";
            var body = $@"
                <h2>Application Submission Confirmation</h2>
                <p>Dear {application.ApplicantName},</p>
                <p>Your application has been successfully submitted with the following details:</p>
                <ul>
                    <li>Application Number: {application.ApplicationNumber}</li>
                    <li>Submission Date: {DateTime.Now:dd/MM/yyyy}</li>
                </ul>
                <p>You will be notified about the next steps via email.</p>
                <p>Best Regards,<br/>PMCRMS Team</p>";

            await _emailService.SendEmailAsync(application.EmailAddress, subject, body);
        }

        public async Task NotifyAppointmentScheduledAsync(DomainEntities.Application application, DateTime appointmentDateTime)
        {
            var subject = $"Document Verification Appointment Scheduled - {application.ApplicationNumber}";
            var body = $@"
                <h2>Document Verification Appointment</h2>
                <p>Dear {application.ApplicantName},</p>
                <p>Your document verification appointment has been scheduled:</p>
                <ul>
                    <li>Application Number: {application.ApplicationNumber}</li>
                    <li>Date: {appointmentDateTime:dd/MM/yyyy}</li>
                    <li>Time: {appointmentDateTime:hh:mm tt}</li>
                </ul>
                <p>Please bring all original documents for verification.</p>
                <p>Best Regards,<br/>PMCRMS Team</p>";

            await _emailService.SendEmailAsync(application.EmailAddress, subject, body);
        }

        public async Task NotifyDocumentVerificationAsync(DomainEntities.Application application, bool isVerified, string? remarks = null)
        {
            var status = isVerified ? "Successful" : "Requires Attention";
            var subject = $"Document Verification {status} - {application.ApplicationNumber}";
            
            var body = $@"
                <h2>Document Verification Update</h2>
                <p>Dear {application.ApplicantName},</p>
                <p>Your document verification status has been updated:</p>
                <ul>
                    <li>Application Number: {application.ApplicationNumber}</li>
                    <li>Status: {status}</li>";

            if (!string.IsNullOrEmpty(remarks))
            {
                body += $@"<li>Remarks: {remarks}</li>";
            }

            body += $@"</ul>
                {(isVerified ? "<p>You can now proceed with the payment.</p>" : "<p>Please address the issues mentioned in the remarks and resubmit the documents.</p>")}
                <p>Best Regards,<br/>PMCRMS Team</p>";

            await _emailService.SendEmailAsync(application.EmailAddress, subject, body);
        }

        public async Task NotifyPaymentCompletionAsync(DomainEntities.Application application, Transaction transaction)
        {
            var subject = $"Payment Confirmation - {application.ApplicationNumber}";
            var body = $@"
                <h2>Payment Confirmation</h2>
                <p>Dear {application.ApplicantName},</p>
                <p>Your payment has been successfully processed:</p>
                <ul>
                    <li>Application Number: {application.ApplicationNumber}</li>
                    <li>Transaction ID: {transaction.TransactionId}</li>
                    <li>Amount: ₹{decimal.Parse(transaction.Amount):N2}</li>
                    <li>Date: {transaction.TransactionDate:dd/MM/yyyy}</li>
                </ul>
                <p>Your certificate will be issued shortly.</p>
                <p>Best Regards,<br/>PMCRMS Team</p>";

            // Create Payment object for challan generation
            var payment = new Payment
            {
                PaymentNumber = transaction.TransactionId,
                Amount = decimal.Parse(transaction.Amount),
                PaymentMethod = transaction.PaymentMode ?? "Online",
                PaymentDate = transaction.TransactionDate,
                Status = transaction.Status
            };

            // Generate challan PDF
            var challanPdf = await _pdfService.GenerateChallanAsync(application, payment);
            
            var message = new EmailMessage
            {
                To = application.EmailAddress,
                Subject = subject,
                Body = body,
                IsHtml = true
            };

            await _emailService.SendEmailWithAttachmentAsync(
                message,
                challanPdf,
                $"challan_{application.ApplicationNumber}.pdf");
        }

        public async Task NotifyCertificateIssuanceAsync(DomainEntities.Application application, byte[] certificatePdf)
        {
            var subject = $"Certificate Issued - {application.ApplicationNumber}";
            var body = $@"
                <h2>Certificate Issuance</h2>
                <p>Dear {application.ApplicantName},</p>
                <p>Your certificate has been successfully issued. Please find it attached with this email.</p>
                <ul>
                    <li>Application Number: {application.ApplicationNumber}</li>
                    <li>Issue Date: {DateTime.Now:dd/MM/yyyy}</li>
                </ul>
                <p>Best Regards,<br/>PMCRMS Team</p>";

            var message = new EmailMessage
            {
                To = application.EmailAddress,
                Subject = subject,
                Body = body,
                IsHtml = true
            };

            await _emailService.SendEmailWithAttachmentAsync(
                message,
                certificatePdf,
                $"certificate_{application.ApplicationNumber}.pdf");
        }
    }
}
