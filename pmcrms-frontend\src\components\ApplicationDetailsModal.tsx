import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from './ui/dialog'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card'
import { Separator } from './ui/separator'
import { Calendar, MapPin, Phone, Mail, User, FileText, Building, Briefcase, Loader2, Eye, Users, GraduationCap, CheckCircle, Shield } from 'lucide-react'
import { useToast } from '../hooks/use-toast'
import { useAuthStore } from '../store'
import api from '../lib/api'
import ScheduleAppointmentModal from './ScheduleAppointmentModal'

interface ApplicationDetailsModalProps {
  applicationId: number | null
  isOpen: boolean
  onClose: () => void
}

interface ApplicationDetails {
  id: number
  applicationNumber: string
  firstName: string
  middleName?: string
  lastName: string
  motherName: string
  emailAddress: string
  mobileNumber: string
  position: string
  gender: string
  bloodGroup: string
  height: number
  birthDate?: string
  panNumber?: string
  aadharNumber?: string
  coaCertificationNumber?: string
  coaValidity?: string
  amount: number
  status: string
  submissionDate: string
  approvalDate?: string
  place: string
  roomNumber: string
  contactPerson?: string
  reviewDate?: string
  transactionMode?: boolean
  transactionDate?: string
  challanNumber?: string
  certificateNumber?: string
  fromDate?: string
  toYear?: number
  isPayment?: boolean
  certificateFilePath?: string
  selfDeclarationFormFilePath?: string
  challanFilePath?: string
  panAttachmentFilePath?: string
  aadharAttachmentFilePath?: string
  coaCertificateFilePath?: string
  electricityBillFilePath?: string
  structuralEngineerDocFilePath?: string
  profilePictureFilePath?: string
  photoFilePath?: string
  signatureFilePath?: string
  identityCardFilePath?: string
  aadharCardFilePath?: string
  panCardFilePath?: string
  permanentSameAsLocal: boolean
  assignedToUserId?: number
  assignedTo?: {
    id: number
    firstName: string
    lastName: string
    email: string
  }
  assigneeId?: number
  assigneeName?: string
  assigneeRole?: string
  assignedBy?: string
  assignedAt?: string
  updatedAt?: string
  addresses: {
    permanent?: {
      flatHouseNumber: string
      streetAddress: string
      addressLine: string
      city: string
      state: string
      country: string
      postalCode: string
    }
    local?: {
      flatHouseNumber: string
      streetAddress: string
      addressLine: string
      city: string
      state: string
      country: string
      postalCode: string
    }
  }
  experiences: Array<{
    id: number
    companyName: string
    position: string
    yearsOfExperience: number
    certificateFilePath?: string
    fromDate: string
    toDate: string
    totalExperience: string
    monthDifference: number
    yearDifference: number
    experienceRequired: boolean
  }>
  qualifications: Array<{
    id: number
    instituteName: string
    universityName: string
    courseSpecialization: string
    degreeProgram: string
    passingMonth: string
    passingYear: string
    certificateFilePath?: string
    lastYearMarksheetFilePath?: string
  }>
}

const ApplicationDetailsModal: React.FC<ApplicationDetailsModalProps> = ({
  applicationId,
  isOpen,
  onClose
}) => {
  const [applicationDetails, setApplicationDetails] = useState<ApplicationDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [showScheduleModal, setShowScheduleModal] = useState(false)
  const [hasReviewedDetails, setHasReviewedDetails] = useState(false)
  const { toast } = useToast()
  const { token } = useAuthStore()

  console.log('ApplicationDetailsModal rendered with:', { applicationId, isOpen })

  useEffect(() => {
    if (isOpen && applicationId) {
      console.log('Fetching application details for ID:', applicationId)
      fetchApplicationDetails()
    }
  }, [isOpen, applicationId])

  useEffect(() => {
    if (isOpen) {
      setHasReviewedDetails(false)
    }
  }, [isOpen])

  const fetchApplicationDetails = async () => {
    if (!applicationId) return

    setLoading(true)
    try {
      console.log('Fetching application details for ID:', applicationId)
      const response = await api.get(`/applications/${applicationId}`)
      console.log('Full API Response:', response)
      console.log('Response type:', typeof response)
      
      // Handle both possible response formats
      let apiData;
      if (response.data && response.status) {
        // This is the full axios response, extract the actual data
        apiData = response.data;
        console.log('Extracted data from axios response:', apiData)
      } else {
        // This is already the extracted data
        apiData = response;
        console.log('Using response directly:', apiData)
      }
      
      if (apiData && apiData.success && apiData.data) {
        console.log('Setting application details:', apiData.data)
        // Add safety checks for arrays to prevent undefined errors
        const safeApplicationDetails = {
          ...apiData.data,
          experiences: apiData.data.experiences || [],
          qualifications: apiData.data.qualifications || []
        }
        setApplicationDetails(safeApplicationDetails)
      } else {
        console.error('API response does not have expected format:', apiData)
        toast({
          title: 'Error',
          description: 'Invalid response format from server',
          variant: 'destructive'
        })
      }
    } catch (error: any) {
      console.error('Failed to load application details:', error)
      console.error('Error details:', error.response?.data)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load application details',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800'
      case 'under_review':
      case 'document_verification_pending':
      case 'junior_engineer_pending':
      case 'assistant_engineer_pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
      case 'license_issued':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatAddress = (address: any) => {
    if (!address) return 'N/A'
    
    const parts = [
      address.flatHouseNumber,
      address.streetAddress,
      address.addressLine,
      address.city,
      address.state,
      address.country,
      address.postalCode
    ].filter(Boolean)
    
    return parts.join(', ') || 'N/A'
  }

  const handleScheduleSuccess = () => {
    toast({
      title: 'Appointment Scheduled',
      description: 'The appointment has been scheduled successfully.',
    })
    setShowScheduleModal(false)
    onClose()
  }

  const markAsReviewed = () => {
    setHasReviewedDetails(true)
  }

  const canScheduleAppointment = () => {
    const status = applicationDetails?.status?.toLowerCase()
    return (status === 'document_verification_pending' || status === 'document verification pending') && hasReviewedDetails
  }

  const canCompleteVerification = () => {
    const status = applicationDetails?.status?.toLowerCase()
    return (status === 'junior_engineer_pending' || status === 'junior engineer pending') && hasReviewedDetails
  }

  const canApproveAsAssistant = () => {
    const status = applicationDetails?.status?.toLowerCase()
    return (status === 'assistant_engineer_pending' || status === 'assistant engineer pending') && hasReviewedDetails
  }

  const canAuthorizeAsExecutive = () => {
    const status = applicationDetails?.status?.toLowerCase()
    return (status === 'executive_engineer_pending' || status === 'executive engineer pending') && hasReviewedDetails
  }

  const canIssueCertificate = () => {
    const status = applicationDetails?.status?.toLowerCase()
    return (status === 'city_engineer_pending' || status === 'city engineer pending') && hasReviewedDetails
  }

  const handleCompleteVerification = async () => {
    if (!applicationDetails) return
    
    try {
      const response = await api.post(`/applications/${applicationDetails.id}/complete-verification`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        toast({
          title: "Success",
          description: "Document verification completed successfully",
        })
        onClose() // Close the modal
        // Optionally refresh the applications list
        window.location.reload()
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to complete verification",
        variant: "destructive"
      })
    }
  }

  const handleApproveAsAssistant = async () => {
    if (!applicationDetails) return
    
    try {
      const response = await api.post(`/applications/${applicationDetails.id}/complete-assistant-review`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        toast({
          title: "Success",
          description: "Assistant Engineer review completed successfully",
        })
        onClose()
        window.location.reload()
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to complete review",
        variant: "destructive"
      })
    }
  }

  const handleAuthorizeAsExecutive = async () => {
    if (!applicationDetails) return
    
    try {
      const response = await api.post(`/applications/${applicationDetails.id}/complete-executive-review`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        toast({
          title: "Success",
          description: "Executive Engineer authorization completed successfully",
        })
        onClose()
        window.location.reload()
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to complete authorization",
        variant: "destructive"
      })
    }
  }

  const handleIssueCertificate = async () => {
    if (!applicationDetails) return
    
    try {
      const response = await api.post(`/applications/${applicationDetails.id}/complete-city-engineer-review`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        toast({
          title: "Success",
          description: "Certificate issued successfully - Application moved to Payment stage",
        })
        onClose()
        window.location.reload()
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to issue certificate",
        variant: "destructive"
      })
    }
  }

  // Always render the modal when isOpen is true
  if (!isOpen) {
    return null
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          {loading ? (
            <>
              <DialogHeader>
                <DialogTitle>Loading Application Details</DialogTitle>
              </DialogHeader>
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading application details...</span>
              </div>
            </>
          ) : !applicationDetails ? (
            <>
              <DialogHeader>
                <DialogTitle>Application Details</DialogTitle>
              </DialogHeader>
              <div className="flex items-center justify-center py-8">
                <span>Failed to load application details</span>
              </div>
            </>
          ) : (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Application Details - #{applicationDetails.applicationNumber}
                </DialogTitle>
                <div className="flex items-center gap-4 mt-2">
                  <Badge className={getStatusBadgeColor(applicationDetails.status)}>
                    {formatStatus(applicationDetails.status)}
                  </Badge>
              <span className="text-sm text-gray-600">
                Submitted: {new Date(applicationDetails.submissionDate).toLocaleDateString()}
              </span>
            </div>
          </DialogHeader>

          {/* Debug Information - Remove after testing */}
          <div className="bg-yellow-50 border border-yellow-200 rounded p-3 text-sm">
            <strong>Debug Info:</strong><br/>
            Status: "{applicationDetails.status}"<br/>
            Status (lowercase): "{applicationDetails.status?.toLowerCase()}"<br/>
            Can Schedule: {canScheduleAppointment().toString()}<br/>
            Can Complete: {canCompleteVerification().toString()}<br/>
            Has Reviewed: {hasReviewedDetails.toString()}
          </div>

          {/* Instructions for user */}
          {!hasReviewedDetails && (
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <div className="flex items-center gap-2 text-blue-800 font-medium mb-2">
                <Eye className="h-5 w-5" />
                Review Required
              </div>
              <p className="text-blue-700 text-sm">
                Please review all application details below, then click "Mark as Reviewed" at the bottom to enable action buttons.
              </p>
            </div>
          )}

          <div className="space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">First Name</label>
                    <p className="text-sm mt-1">{applicationDetails.firstName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Name</label>
                    <p className="text-sm mt-1">{applicationDetails.lastName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Mother's Name</label>
                    <p className="text-sm mt-1">{applicationDetails.motherName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="text-sm mt-1 flex items-center gap-1">
                      <Mail className="h-4 w-4" />
                      {applicationDetails.emailAddress}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Mobile Number</label>
                    <p className="text-sm mt-1 flex items-center gap-1">
                      <Phone className="h-4 w-4" />
                      {applicationDetails.mobileNumber}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Position</label>
                    <p className="text-sm mt-1">{applicationDetails.position}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Gender</label>
                    <p className="text-sm mt-1">{applicationDetails.gender}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Blood Group</label>
                    <p className="text-sm mt-1">{applicationDetails.bloodGroup}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Middle Name</label>
                    <p className="text-sm mt-1">{applicationDetails.middleName || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Birth Date</label>
                    <p className="text-sm mt-1">
                      {applicationDetails.birthDate 
                        ? new Date(applicationDetails.birthDate).toLocaleDateString() 
                        : 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Identification Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Identification Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">PAN Number</label>
                    <p className="text-sm mt-1">{applicationDetails.panNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Aadhar Number</label>
                    <p className="text-sm mt-1">{applicationDetails.aadharNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Height</label>
                    <p className="text-sm mt-1">{applicationDetails.height} cm</p>
                  </div>
                  {applicationDetails.coaCertificationNumber && (
                    <>
                      <div>
                        <label className="text-sm font-medium text-gray-600">COA Certification Number</label>
                        <p className="text-sm mt-1">{applicationDetails.coaCertificationNumber}</p>
                      </div>
                      {applicationDetails.coaValidity && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">COA Validity</label>
                          <p className="text-sm mt-1">
                            {new Date(applicationDetails.coaValidity).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Address Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Permanent Address</label>
                    <p className="text-sm mt-1">{formatAddress(applicationDetails.addresses.permanent)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Local Address</label>
                    <p className="text-sm mt-1">
                      {applicationDetails.permanentSameAsLocal 
                        ? 'Same as permanent address' 
                        : formatAddress(applicationDetails.addresses.local)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Professional Experience */}
            {applicationDetails.experiences && applicationDetails.experiences.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="h-5 w-5" />
                    Professional Experience
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {applicationDetails.experiences.map((exp) => (
                      <div key={exp.id} className="border rounded-lg p-4">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-600">Company</label>
                            <p className="text-sm mt-1">{exp.companyName}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Position</label>
                            <p className="text-sm mt-1">{exp.position}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Experience</label>
                            <p className="text-sm mt-1">{exp.totalExperience}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">From Date</label>
                            <p className="text-sm mt-1">{new Date(exp.fromDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">To Date</label>
                            <p className="text-sm mt-1">{new Date(exp.toDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Years</label>
                            <p className="text-sm mt-1">{exp.yearsOfExperience} years</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Educational Qualifications */}
            {applicationDetails.qualifications && applicationDetails.qualifications.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GraduationCap className="h-5 w-5" />
                    Educational Qualifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {applicationDetails.qualifications.map((qual, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-600">Institute Name</label>
                            <p className="text-sm mt-1">{qual.instituteName}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">University Name</label>
                            <p className="text-sm mt-1">{qual.universityName}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Course Specialization</label>
                            <p className="text-sm mt-1">{qual.courseSpecialization}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Degree Program</label>
                            <p className="text-sm mt-1">{qual.degreeProgram}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Passing Date</label>
                            <p className="text-sm mt-1">{qual.passingMonth}/{qual.passingYear}</p>
                          </div>
                          <div className="col-span-2 md:col-span-3">
                            <label className="text-sm font-medium text-gray-600">Documents</label>
                            <div className="flex gap-2 mt-2">
                              {qual.certificateFilePath && (
                                <button
                                  onClick={() => window.open(`/uploads/documents/${qual.certificateFilePath}`, '_blank')}
                                  className="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 flex items-center gap-1"
                                >
                                  <FileText className="h-4 w-4" />
                                  Certificate
                                </button>
                              )}
                              {qual.lastYearMarksheetFilePath && (
                                <button
                                  onClick={() => window.open(`/uploads/documents/${qual.lastYearMarksheetFilePath}`, '_blank')}
                                  className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 flex items-center gap-1"
                                >
                                  <FileText className="h-4 w-4" />
                                  Marksheet
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Transaction & Payment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Transaction & Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Transaction Mode</label>
                    <p className="text-sm mt-1">{applicationDetails.transactionMode ? 'Online' : 'Offline'}</p>
                  </div>
                  {applicationDetails.transactionDate && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Transaction Date</label>
                      <p className="text-sm mt-1">
                        {new Date(applicationDetails.transactionDate).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                  {applicationDetails.challanNumber && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Challan Number</label>
                      <p className="text-sm mt-1">{applicationDetails.challanNumber}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-600">Payment Status</label>
                    <p className="text-sm mt-1">{applicationDetails.isPayment ? 'Paid' : 'Pending'}</p>
                  </div>
                  {applicationDetails.certificateNumber && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Certificate Number</label>
                      <p className="text-sm mt-1">{applicationDetails.certificateNumber}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Document Attachments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Attachments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {applicationDetails.certificateFilePath && (
                    <div className="p-3 border rounded-md">
                      <label className="text-sm font-medium text-gray-600">Certificate File</label>
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-sm text-blue-600">Certificate Document</p>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(applicationDetails.certificateFilePath, '_blank')}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.panAttachmentFilePath && (
                    <div className="p-3 border rounded-md">
                      <label className="text-sm font-medium text-gray-600">PAN Attachment</label>
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-sm text-blue-600">PAN Document</p>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(applicationDetails.panAttachmentFilePath, '_blank')}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.aadharAttachmentFilePath && (
                    <div className="p-3 border rounded-md">
                      <label className="text-sm font-medium text-gray-600">Aadhar Attachment</label>
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-sm text-blue-600">Aadhar Document</p>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(applicationDetails.aadharAttachmentFilePath, '_blank')}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.coaCertificateFilePath && (
                    <div className="p-3 border rounded-md">
                      <label className="text-sm font-medium text-gray-600">COA Certificate</label>
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-sm text-blue-600">COA Certificate Document</p>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(applicationDetails.coaCertificateFilePath, '_blank')}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  )}
                  {!applicationDetails.certificateFilePath && 
                   !applicationDetails.panAttachmentFilePath && 
                   !applicationDetails.aadharAttachmentFilePath && 
                   !applicationDetails.coaCertificateFilePath && (
                    <div className="col-span-2 text-center text-gray-500 py-4">
                      No documents attached
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Application Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Application Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Application Number</label>
                    <p className="text-sm mt-1">{applicationDetails.applicationNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Amount</label>
                    <p className="text-sm mt-1">₹{applicationDetails.amount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Place</label>
                    <p className="text-sm mt-1">{applicationDetails.place}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Room Number</label>
                    <p className="text-sm mt-1">{applicationDetails.roomNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Submission Date</label>
                    <p className="text-sm mt-1 flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(applicationDetails.submissionDate).toLocaleDateString()}
                    </p>
                  </div>
                  {applicationDetails.approvalDate && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Approval Date</label>
                      <p className="text-sm mt-1 flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(applicationDetails.approvalDate).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Documents */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Attached Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {applicationDetails.photoFilePath && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Photo</label>
                      <div className="mt-2">
                        <button
                          onClick={() => window.open(`/uploads/documents/${applicationDetails.photoFilePath}`, '_blank')}
                          className="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 flex items-center gap-1"
                        >
                          <Eye className="h-4 w-4" />
                          View Photo
                        </button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.signatureFilePath && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Signature</label>
                      <div className="mt-2">
                        <button
                          onClick={() => window.open(`/uploads/documents/${applicationDetails.signatureFilePath}`, '_blank')}
                          className="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 flex items-center gap-1"
                        >
                          <Eye className="h-4 w-4" />
                          View Signature
                        </button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.identityCardFilePath && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Identity Card</label>
                      <div className="mt-2">
                        <button
                          onClick={() => window.open(`/uploads/documents/${applicationDetails.identityCardFilePath}`, '_blank')}
                          className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 flex items-center gap-1"
                        >
                          <FileText className="h-4 w-4" />
                          View ID Card
                        </button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.aadharCardFilePath && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Aadhar Card</label>
                      <div className="mt-2">
                        <button
                          onClick={() => window.open(`/uploads/documents/${applicationDetails.aadharCardFilePath}`, '_blank')}
                          className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 flex items-center gap-1"
                        >
                          <FileText className="h-4 w-4" />
                          View Aadhar
                        </button>
                      </div>
                    </div>
                  )}
                  {applicationDetails.panCardFilePath && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">PAN Card</label>
                      <div className="mt-2">
                        <button
                          onClick={() => window.open(`/uploads/documents/${applicationDetails.panCardFilePath}`, '_blank')}
                          className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 flex items-center gap-1"
                        >
                          <FileText className="h-4 w-4" />
                          View PAN
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Assignment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Assignment Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {applicationDetails.assigneeId && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Assigned To</label>
                      <p className="text-sm mt-1">{applicationDetails.assigneeName || `User ID: ${applicationDetails.assigneeId}`}</p>
                    </div>
                  )}
                  {applicationDetails.assigneeRole && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Assignee Role</label>
                      <p className="text-sm mt-1">{applicationDetails.assigneeRole}</p>
                    </div>
                  )}
                  {applicationDetails.assignedBy && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Assigned By</label>
                      <p className="text-sm mt-1">{applicationDetails.assignedBy}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-600">Current Status</label>
                    <p className="text-sm mt-1 capitalize">{applicationDetails.status.replace(/_/g, ' ')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Submitted At</label>
                    <p className="text-sm mt-1">
                      {new Date(applicationDetails.submissionDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Updated</label>
                    <p className="text-sm mt-1">
                      {new Date(applicationDetails.updatedAt || applicationDetails.submissionDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator className="my-6" />

          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <div className="flex gap-3">
              {!hasReviewedDetails && (
                <Button 
                  onClick={markAsReviewed}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" />
                  Mark as Reviewed
                </Button>
              )}
              {hasReviewedDetails && (
                <span className="text-sm text-green-600 flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Application reviewed
                </span>
              )}
            </div>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              
              {/* Show Schedule Appointment button for DOCUMENT_VERIFICATION_PENDING */}
              {canScheduleAppointment() && (
                <Button 
                  onClick={() => setShowScheduleModal(true)}
                  className="flex items-center gap-2"
                >
                  <Calendar className="h-4 w-4" />
                  Schedule Appointment
                </Button>
              )}
              
              {/* Show Document Verified button for JUNIOR_ENGINEER_PENDING */}
              {canCompleteVerification() && (
                <Button 
                  onClick={handleCompleteVerification}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
                >
                  <FileText className="h-4 w-4" />
                  Document Verified
                </Button>
              )}

              {/* Show Approve Application button for ASSISTANT_ENGINEER_PENDING */}
              {canApproveAsAssistant() && (
                <Button 
                  onClick={handleApproveAsAssistant}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="h-4 w-4" />
                  Approve Application
                </Button>
              )}

              {/* Show Authorize Application button for EXECUTIVE_ENGINEER_PENDING */}
              {canAuthorizeAsExecutive() && (
                <Button 
                  onClick={handleAuthorizeAsExecutive}
                  className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Shield className="h-4 w-4" />
                  Authorize Application
                </Button>
              )}

              {/* Show City Engineer Approve button for CITY_ENGINEER_PENDING */}
              {canIssueCertificate() && (
                <Button 
                  onClick={handleIssueCertificate}
                  className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700 text-white"
                >
                  City Engineer Approve
                </Button>
              )}
            </div>
          </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Schedule Appointment Modal */}
      {showScheduleModal && applicationDetails && (
        <ScheduleAppointmentModal
          application={{
            id: applicationDetails.id,
            applicationNumber: applicationDetails.applicationNumber,
            firstName: applicationDetails.firstName,
            lastName: applicationDetails.lastName,
            position: applicationDetails.position
          }}
          isOpen={showScheduleModal}
          onClose={() => setShowScheduleModal(false)}
          onScheduled={handleScheduleSuccess}
        />
      )}
    </>
  )
}

export default ApplicationDetailsModal
