{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=PmcrmsDb;Username=postgres;Password=root"}, "JwtSettings": {"Secret": "your-very-long-secret-key-here-at-least-32-characters", "Issuer": "pmcrms-api", "Audience": "pmcrms-client", "ExpiryMinutes": 60}, "Easebuzz": {"Key": "your-easebuzz-key", "Salt": "your-easebuzz-salt", "Environment": "test"}, "SmtpClient": {"FromName": "ezBricks Admin", "Server": "smtp-relay.sendinblue.com", "Port": 587, "User": "<EMAIL>", "Password": "nJfbRhsHWFUxrgVL", "UseSsl": true, "RequiresAuthentication": true, "SocketOptions": 3, "Body": "Welcome to ezbricks. To ensure a secure login, we've sent you a one-time password (OTP). Please use it to log in to your account <b>@Model.otp</b>", "Subject": "Your Secure Login OTP for ezbricks"}, "SmtpSettings": {"Server": "smtp-relay.sendinblue.com", "Port": 587, "Username": "<EMAIL>", "Password": "nJfbRhsHWFUxrgVL", "SenderEmail": "<EMAIL>", "SenderName": "ezBricks Admin"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}