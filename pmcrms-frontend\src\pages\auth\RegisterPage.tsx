import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { ArrowLeft, Mail, Download, FileText } from 'lucide-react'
import Logo from '../../components/Logo'

const RegisterPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Main Registration Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Logo size="lg" showText={false} />
            </div>
            <CardTitle className="text-2xl">Create Account</CardTitle>
            <CardDescription>
              Register to start your professional registration
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-gray-600 mb-4">
                Registration will be implemented in the next phase
              </p>
              <Link to="/login">
                <Button className="w-full">
                  Back to Login
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Guest Access Card */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <Mail className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-900">Already Have a Registration?</h3>
              <p className="text-sm text-gray-600">
                Access your certificate or check application status without creating an account
              </p>
              <Link to="/guest">
                <Button variant="outline" className="w-full border-blue-300 text-blue-700 hover:bg-blue-100">
                  <Mail className="mr-2 h-4 w-4" />
                  Guest Login with OTP
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="text-center space-y-3">
          <p className="text-sm text-gray-600">Need help?</p>
          <div className="grid grid-cols-2 gap-3">
            <Link to="/status">
              <Button variant="ghost" size="sm" className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Check Status
              </Button>
            </Link>
            <Link to="/">
              <Button variant="ghost" size="sm" className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download Forms
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default RegisterPage
