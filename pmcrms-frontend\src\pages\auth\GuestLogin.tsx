import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { ArrowLeft, Mail, Loader2 } from 'lucide-react'
import { guestAuth } from '../../lib/api'
import Logo from '../../components/Logo'

// Validation schemas
const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

const otpSchema = z.object({
  otp: z.string().min(6, 'OTP must be 6 digits').max(6, 'OTP must be 6 digits'),
})

type EmailForm = z.infer<typeof emailSchema>
type OtpForm = z.infer<typeof otpSchema>

const GuestLogin: React.FC = () => {
  const [step, setStep] = useState<'email' | 'otp'>('email')
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [resendCooldown, setResendCooldown] = useState(0)
  const navigate = useNavigate()

  // Email form
  const emailForm = useForm<EmailForm>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: '',
    },
  })

  // OTP form
  const otpForm = useForm<OtpForm>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
    },
  })

  // Send OTP to email
  const handleSendOtp = async (data: EmailForm) => {
    try {
      setLoading(true)
      await guestAuth.sendOtp(data.email)
      setEmail(data.email)
      setStep('otp')
      setResendCooldown(60) // 60 seconds cooldown
      
      // Start countdown
      const timer = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error: any) {
      console.error('Error sending OTP:', error)
      // Handle error - show toast notification
    } finally {
      setLoading(false)
    }
  }

  // Verify OTP and login
  const handleVerifyOtp = async (data: OtpForm) => {
    try {
      setLoading(true)
      console.log('Verifying OTP for:', email, 'with OTP:', data.otp)
      
      const response = await guestAuth.verifyOtp(email, data.otp)
      
      console.log('Full OTP Verify Response:', response) // Debug log
      console.log('Response data:', response.data) // Debug log
      
      // Store guest session data - handle different response structures
      const responseData = response.data || response
      console.log('Response data extracted:', responseData) // Debug log
      
      const accessToken = responseData.accessToken
      const expiresIn = responseData.expiresIn || 3600 // Default to 1 hour if not provided
      
      console.log('Access Token:', accessToken) // Debug log
      console.log('Expires In:', expiresIn) // Debug log
      
      if (!accessToken) {
        throw new Error('No access token received from server')
      }
      
      const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString()
      
      const sessionData = {
        email,
        accessToken: accessToken,
        expiresAt: expiresAt,
      }
      
      console.log('Storing session data:', sessionData) // Debug log
      localStorage.setItem('guest_session', JSON.stringify(sessionData))
      localStorage.setItem('guest_email', email) // Store email separately for compatibility
      localStorage.setItem('guest_session_created_at', new Date().toISOString()) // Store creation time
      
      console.log('Navigating to /dashboard/guest') // Debug log
      // Redirect to guest dashboard or status page
      navigate('/dashboard/guest', { replace: true })
      
    } catch (error: any) {
      console.error('Error verifying OTP:', error)
      console.error('Error response:', error.response) // Debug log
      otpForm.setError('otp', {
        type: 'manual',
        message: error.response?.data?.message || 'Invalid OTP. Please try again.',
      })
    } finally {
      setLoading(false)
    }
  }

  // Resend OTP
  const handleResendOtp = async () => {
    if (resendCooldown > 0) return
    
    try {
      setLoading(true)
      await guestAuth.sendOtp(email)
      setResendCooldown(60)
      
      const timer = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error: any) {
      console.error('Error resending OTP:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Logo size="lg" showText={false} />
          </div>
          <CardTitle className="text-2xl">
            {step === 'email' ? 'Guest Access' : 'Enter OTP'}
          </CardTitle>
          <CardDescription>
            {step === 'email' 
              ? 'Enter your email to receive OTP for certificate download' 
              : `We've sent a 6-digit code to ${email}`
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {step === 'email' ? (
            <form onSubmit={emailForm.handleSubmit(handleSendOtp)} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    className="pl-10"
                    {...emailForm.register('email')}
                  />
                </div>
                {emailForm.formState.errors.email && (
                  <p className="text-sm text-red-600">
                    {emailForm.formState.errors.email.message}
                  </p>
                )}
              </div>
              
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending OTP...
                  </>
                ) : (
                  'Send OTP'
                )}
              </Button>
            </form>
          ) : (
            <form onSubmit={otpForm.handleSubmit(handleVerifyOtp)} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="otp" className="text-sm font-medium text-gray-700">
                  6-Digit OTP
                </label>
                <Input
                  id="otp"
                  type="text"
                  placeholder="Enter 6-digit OTP"
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                  {...otpForm.register('otp')}
                />
                {otpForm.formState.errors.otp && (
                  <p className="text-sm text-red-600">
                    {otpForm.formState.errors.otp.message}
                  </p>
                )}
              </div>
              
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  'Verify & Continue'
                )}
              </Button>
              
              <div className="text-center space-y-2">
                <p className="text-sm text-gray-600">
                  Didn't receive the code?
                </p>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleResendOtp}
                  disabled={resendCooldown > 0 || loading}
                >
                  {resendCooldown > 0 
                    ? `Resend in ${resendCooldown}s` 
                    : 'Resend OTP'
                  }
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setStep('email')
                    otpForm.reset()
                  }}
                >
                  Change Email
                </Button>
              </div>
            </form>
          )}
          
          <div className="text-center">
            <Link to="/login">
              <Button variant="outline" className="w-full">
                Regular Login Instead
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default GuestLogin
