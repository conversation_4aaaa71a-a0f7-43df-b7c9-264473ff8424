import { useState, useEffect } from 'react'
import { GuestSession } from '../types/types'

export const useGuestAuth = () => {
  const [guestSession, setGuestSession] = useState<GuestSession | null>(null)
  const [isGuestAuthenticated, setIsGuestAuthenticated] = useState(false)

  useEffect(() => {
    // Check for existing guest session on mount
    const sessionData = localStorage.getItem('guest_session')
    if (sessionData) {
      try {
        const session: GuestSession = JSON.parse(sessionData)
        const now = new Date().getTime()
        const expiresAt = new Date(session.expiresAt).getTime()

        if (now < expiresAt) {
          setGuestSession(session)
          setIsGuestAuthenticated(true)
        } else {
          // Session expired, clear it
          localStorage.removeItem('guest_session')
        }
      } catch (error) {
        // Invalid session data, clear it
        localStorage.removeItem('guest_session')
      }
    }
  }, [])

  const setGuestSessionData = (session: GuestSession) => {
    setGuestSession(session)
    setIsGuestAuthenticated(true)
    localStorage.setItem('guest_session', JSON.stringify(session))
  }

  const clearGuestSession = () => {
    setGuestSession(null)
    setIsGuestAuthenticated(false)
    localStorage.removeItem('guest_session')
  }

  const isSessionValid = (): boolean => {
    if (!guestSession) return false
    
    const now = new Date().getTime()
    const expiresAt = new Date(guestSession.expiresAt).getTime()
    
    if (now >= expiresAt) {
      clearGuestSession()
      return false
    }
    
    return true
  }

  return {
    guestSession,
    isGuestAuthenticated,
    setGuestSessionData,
    clearGuestSession,
    isSessionValid,
  }
}
