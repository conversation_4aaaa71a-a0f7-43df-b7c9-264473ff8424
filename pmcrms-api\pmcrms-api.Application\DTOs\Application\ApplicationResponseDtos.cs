namespace pmcrms_api.Application.DTOs.Application;

/// <summary>
/// Response model for application details
/// </summary>
public class ApplicationResponse
{
    public int Id { get; set; }
    public string ApplicationNumber { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string EmailAddress { get; set; } = string.Empty;
    public string MobileNumber { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime SubmissionDate { get; set; }
    public DateTime? ApprovalDate { get; set; }
    public string MotherName { get; set; } = string.Empty;
    public string Place { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string BloodGroup { get; set; } = string.Empty;
    public decimal Height { get; set; }
    public string Gender { get; set; } = string.Empty;
    public bool PermanentSameAsLocal { get; set; }
    public AddressResponse Addresses { get; set; } = new();
    public List<ExperienceResponse> Experiences { get; set; } = new();
}

/// <summary>
/// Response model for addresses
/// </summary>
public class AddressResponse
{
    public AddressDetailsResponse? Permanent { get; set; }
    public AddressDetailsResponse? Local { get; set; }
}

/// <summary>
/// Response model for address details
/// </summary>
public class AddressDetailsResponse
{
    public string FlatHouseNumber { get; set; } = string.Empty;
    public string Street { get; set; } = string.Empty;
    public string AddressLine { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
}

/// <summary>
/// Response model for experience details
/// </summary>
public class ExperienceResponse
{
    public int Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public int YearsOfExperience { get; set; }
    public string? CertificateFilePath { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string TotalExperience { get; set; } = string.Empty;
    public int MonthDifference { get; set; }
    public int YearDifference { get; set; }
    public bool ExperienceRequired { get; set; }
}
