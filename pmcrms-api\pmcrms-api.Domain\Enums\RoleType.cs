namespace pmcrms_api.Domain.Enums;

/// <summary>
/// Complete role hierarchy for PMCRMS system
/// Includes generic roles and specific sub-roles for position-based routing
/// </summary>
public enum RoleType
{
    // ===========================================
    // END USER ROLES
    // ===========================================
    User,        // End User (applicants)
    Guest,       // Guest login for certificate download

    // ===========================================
    // JUNIOR ENGINEER ROLES
    // ===========================================
    JrEngineer,              // Generic Junior Engineer role
    JRENGG_ARCH,            // Jr. Engineer Architect
    JRENGG_STRU,            // Jr. Engineer Structural
    JRENGG_LICE,            // Jr. Engineer License
    JRENGG_SUPER1,          // Jr. Engineer Supervisor-1
    JRENGG_SUPER2,          // Jr. Engineer Supervisor-2

    // ===========================================
    // ASSISTANT ENGINEER ROLES
    // ===========================================
    AssistantEngineer,       // Generic Assistant Engineer role
    ASSIENGG_ARCH,          // Assistant Engineer Architect
    ASSIENGG_STRU,          // Assistant Engineer Structural
    ASSIENGG_LICE,          // Assistant Engineer License
    ASSIENGG_SUPER1,        // Assistant Engineer Supervisor-1
    ASSIENGG_SUPER2,        // Assistant Engineer Supervisor-2

    // ===========================================
    // SENIOR ENGINEER ROLES
    // ===========================================
    ExecutiveEngineer,       // Executive Engineer
    CityEngineer,           // City Engineer (also CITYENGG)

    // ===========================================
    // SUPPORT ROLES
    // ===========================================
    Clerk,                  // Clerk for post-payment processing
    OfflinePaymentOfficer,  // Offline Payment Officer
    Admin                   // System Administrator
}
