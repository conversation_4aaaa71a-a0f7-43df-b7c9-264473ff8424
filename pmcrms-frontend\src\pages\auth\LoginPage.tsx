import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { useAuthStore } from '../../store'
import { Mail, ArrowLeft, KeyRound, Loader2 } from 'lucide-react'
import Logo from '../../components/Logo'
import { guestAuth } from '../../lib/api'

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

const otpEmailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

const otpVerifySchema = z.object({
  otp: z.string().length(6, 'OTP must be 6 digits'),
})

type LoginFormData = z.infer<typeof loginSchema>
type OTPEmailFormData = z.infer<typeof otpEmailSchema>
type OTPVerifyFormData = z.infer<typeof otpVerifySchema>

const LoginPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'regular' | 'guest'>('regular')
  const [guestStep, setGuestStep] = useState<'email' | 'otp'>('email')
  const [guestEmail, setGuestEmail] = useState('')
  const [resendCooldown, setResendCooldown] = useState(0)
  const [guestLoading, setGuestLoading] = useState(false)
  const { login, isLoading, error } = useAuthStore()
  const navigate = useNavigate()

  const emailForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const guestEmailForm = useForm<OTPEmailFormData>({
    resolver: zodResolver(otpEmailSchema),
  })

  const guestOtpForm = useForm<OTPVerifyFormData>({
    resolver: zodResolver(otpVerifySchema),
  })

  const onRegularLogin = async (data: LoginFormData) => {
    try {
      await login(data)
      
      // Get the user from the store to check their role
      const { user } = useAuthStore.getState()
      
      // Redirect based on user role
      if (user?.role === 'Admin') {
        navigate('/admin/dashboard')
      } else {
        navigate('/dashboard')
      }
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  const onSendGuestOTP = async (data: OTPEmailFormData) => {
    try {
      setGuestLoading(true)
      await guestAuth.sendOtp(data.email)
      setGuestEmail(data.email)
      setGuestStep('otp')
      setResendCooldown(60)
      
      // Start countdown
      const timer = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error: any) {
      console.error('Error sending OTP:', error)
      guestEmailForm.setError('email', {
        type: 'manual',
        message: error.response?.data?.message || 'Failed to send OTP'
      })
    } finally {
      setGuestLoading(false)
    }
  }

  const onVerifyGuestOTP = async (data: OTPVerifyFormData) => {
    try {
      setGuestLoading(true)
      console.log('Verifying OTP for:', guestEmail, 'with OTP:', data.otp)
      
      const response = await guestAuth.verifyOtp(guestEmail, data.otp)
      
      console.log('Full OTP Verify Response:', response) // Debug log
      console.log('Response data:', response.data) // Debug log
      
      // Store guest session - handle different response structures
      const responseData = response.data || response
      console.log('Response data extracted:', responseData) // Debug log
      
      const accessToken = responseData.accessToken
      const expiresIn = responseData.expiresIn || 3600 // Default to 1 hour if not provided
      
      console.log('Access Token:', accessToken) // Debug log
      console.log('Expires In:', expiresIn) // Debug log
      
      if (!accessToken) {
        throw new Error('No access token received from server')
      }
      
      const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString()
      
      const sessionData = {
        email: guestEmail,
        accessToken: accessToken,
        expiresAt: expiresAt,
      }
      
      console.log('Storing session data:', sessionData) // Debug log
      localStorage.setItem('guest_session', JSON.stringify(sessionData))
      
      console.log('Navigating to /dashboard/guest') // Debug log
      // Redirect to application status page
      navigate('/dashboard/guest', { replace: true })
      
    } catch (error: any) {
      console.error('Error verifying OTP:', error)
      console.error('Error response:', error.response) // Debug log
      guestOtpForm.setError('otp', {
        type: 'manual',
        message: error.response?.data?.message || error.message || 'Invalid OTP'
      })
    } finally {
      setGuestLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return
    
    try {
      setGuestLoading(true)
      await guestAuth.sendOtp(guestEmail)
      setResendCooldown(60)
      
      const timer = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error: any) {
      console.error('Error resending OTP:', error)
    } finally {
      setGuestLoading(false)
    }
  }

  const resetGuestForm = () => {
    setGuestStep('email')
    setGuestEmail('')
    setResendCooldown(0)
    guestEmailForm.reset()
    guestOtpForm.reset()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Logo size="lg" showText={false} />
          </div>
          <CardTitle className="text-2xl">Welcome to PMCRMS</CardTitle>
          <CardDescription>
            Sign in to access your dashboard or check application status
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Tab Navigation */}
          <div className="flex rounded-lg bg-gray-100 p-1">
            <button
              type="button"
              onClick={() => {
                setActiveTab('regular')
                resetGuestForm()
              }}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'regular'
                  ? 'bg-white text-primary shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Mail className="inline-block w-4 h-4 mr-2" />
              Regular Login
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('guest')}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'guest'
                  ? 'bg-white text-primary shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <KeyRound className="inline-block w-4 h-4 mr-2" />
              Guest Access
            </button>
          </div>

          {error && activeTab === 'regular' && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Regular Login Tab */}
          {activeTab === 'regular' && (
            <form onSubmit={emailForm.handleSubmit(onRegularLogin)} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <Input
                  id="email"
                  type="email"
                  autoComplete="email"
                  {...emailForm.register('email')}
                  placeholder="Enter your email"
                />
                {emailForm.formState.errors.email && (
                  <p className="text-sm text-red-600 mt-1">
                    {emailForm.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <Input
                  id="password"
                  type="password"
                  autoComplete="current-password"
                  {...emailForm.register('password')}
                  placeholder="Enter your password"
                />
                {emailForm.formState.errors.password && (
                  <p className="text-sm text-red-600 mt-1">
                    {emailForm.formState.errors.password.message}
                  </p>
                )}
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          )}

          {/* Guest Login Tab */}
          {activeTab === 'guest' && (
            <div className="space-y-4">
              {guestStep === 'email' ? (
                <form onSubmit={guestEmailForm.handleSubmit(onSendGuestOTP)} className="space-y-4">
                  <div>
                    <label htmlFor="guestEmail" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="guestEmail"
                        type="email"
                        autoComplete="email"
                        {...guestEmailForm.register('email')}
                        placeholder="Enter your email for OTP"
                        className="pl-10"
                      />
                    </div>
                    {guestEmailForm.formState.errors.email && (
                      <p className="text-sm text-red-600 mt-1">
                        {guestEmailForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={guestLoading}>
                    {guestLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending OTP...
                      </>
                    ) : (
                      'Send OTP'
                    )}
                  </Button>

                  <p className="text-xs text-gray-600 text-center">
                    Enter your email to receive a 6-digit OTP for guest access
                  </p>
                </form>
              ) : (
                <form onSubmit={guestOtpForm.handleSubmit(onVerifyGuestOTP)} className="space-y-4">
                  <div>
                    <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-1">
                      6-Digit OTP
                    </label>
                    <Input
                      id="otp"
                      type="text"
                      {...guestOtpForm.register('otp')}
                      placeholder="Enter 6-digit OTP"
                      maxLength={6}
                      className="text-center text-lg tracking-widest"
                    />
                    {guestOtpForm.formState.errors.otp && (
                      <p className="text-sm text-red-600 mt-1">
                        {guestOtpForm.formState.errors.otp.message}
                      </p>
                    )}
                    <p className="text-xs text-gray-600 mt-1">
                      OTP sent to {guestEmail}
                    </p>
                  </div>

                  <Button type="submit" className="w-full" disabled={guestLoading}>
                    {guestLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify & Continue'
                    )}
                  </Button>

                  <div className="text-center space-y-2">
                    <p className="text-sm text-gray-600">
                      Didn't receive the code?
                    </p>
                    <div className="flex justify-center space-x-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleResendOTP}
                        disabled={resendCooldown > 0 || guestLoading}
                      >
                        {resendCooldown > 0 
                          ? `Resend in ${resendCooldown}s` 
                          : 'Resend OTP'
                        }
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={resetGuestForm}
                      >
                        Change Email
                      </Button>
                    </div>
                  </div>
                </form>
              )}
            </div>
          )}

          {/* Links Section */}
          <div className="text-center space-y-3 pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/register" className="text-primary hover:underline">
                Register here
              </Link>
            </div>
            
            <div className="text-xs text-gray-500 space-y-1">
              <p>Officers/Admins:</p>
              <div className="flex justify-center space-x-3">
                <Link to="/officer/login" className="text-primary hover:underline">
                  Officer Login
                </Link>
                <Link to="/admin/login" className="text-primary hover:underline">
                  Admin Login
                </Link>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default LoginPage
