using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

public class Payment
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string PaymentNumber { get; set; } = string.Empty;

    [Required]
    public decimal Amount { get; set; }

    [Required]
    [StringLength(50)]
    public string PaymentMethod { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Status { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Notes { get; set; }

    public DateTime PaymentDate { get; set; }
    public DateTime CreatedAt { get; set; }

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;

    public ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
}
