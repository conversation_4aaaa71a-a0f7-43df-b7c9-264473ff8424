using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Application.Services;
using pmcrms_api.Infrastructure.Data;
using pmcrms_api.Infrastructure.Repositories;
using pmcrms_api.Infrastructure.Services;

namespace pmcrms_api.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)));

        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IWorkflowService, WorkflowService>();
        services.AddScoped<IApplicationRoutingService, ApplicationRoutingService>();
        services.AddScoped<IPDFService, SECertificatePlugin>();
        services.AddScoped<IPaymentService, EasebuzzPaymentService>();
        services.AddScoped<IEmailService, SmtpEmailService>();
        services.AddScoped<INotificationService, NotificationService>();
        services.AddScoped<ICertificateService, CertificateService>();
        services.AddScoped<IRecommendationFormService, RecommendationFormService>();

        return services;
    }
}
