using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using pmcrms_api.Application.DTOs.Auth;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using pmcrms_api.Infrastructure.Data;
using System.ComponentModel.DataAnnotations;
using System.Net;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Admin setup and initial configuration endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Tags("Admin Setup")]
[Produces("application/json")]
public class AdminSetupController : ControllerBase
{
    private readonly ApplicationDbContext _context;

    public AdminSetupController(ApplicationDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Initialize the system with default admin credentials
    /// </summary>
    /// <returns>Admin setup result with credentials</returns>
    /// <response code="200">Admin user created successfully</response>
    /// <response code="400">Admin user already exists or setup failed</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("initialize")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AdminSetupResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> InitializeAdmin()
    {
        try
        {
            // Check if any admin user already exists
            var existingAdmin = await _context.Users
                .Include(u => u.Role)
                .AnyAsync(u => u.Role.Name == "Admin");

            if (existingAdmin)
            {
                return BadRequest(new { message = "Admin user already exists. Use the change password endpoint instead." });
            }

            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Create roles if they don't exist
            if (!await _context.Roles.AnyAsync())
            {
                var roles = Enum.GetNames(typeof(RoleType))
                    .Select(role => new Role
                    {
                        Name = role,
                        Description = $"{role} role"
                    });

                await _context.Roles.AddRangeAsync(roles);
                await _context.SaveChangesAsync();
            }

            // Get admin role
            var adminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Admin");
            if (adminRole == null)
            {
                return BadRequest(new { message = "Could not create admin role." });
            }

            // Create default admin user
            var defaultPassword = "Admin@123!";
            var adminUser = new User
            {
                FirstName = "System",
                LastName = "Administrator", 
                Email = "<EMAIL>",
                Password = BCrypt.Net.BCrypt.HashPassword(defaultPassword),
                PhoneNumber = "+91-9999999999",
                RoleId = adminRole.Id,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            await _context.Users.AddAsync(adminUser);
            await _context.SaveChangesAsync();

            return Ok(new AdminSetupResponse
            {
                Success = true,
                Message = "Admin user created successfully",
                AdminEmail = adminUser.Email,
                DefaultPassword = defaultPassword,
                Warning = "IMPORTANT: Please change the default password immediately after first login!"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to initialize admin user", error = ex.Message });
        }
    }

    /// <summary>
    /// Change admin password (requires current admin authentication)
    /// </summary>
    /// <param name="request">Password change request</param>
    /// <returns>Password change result</returns>
    /// <response code="200">Password changed successfully</response>
    /// <response code="400">Invalid request or current password incorrect</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Admin user not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("change-password")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ChangeAdminPassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            var userEmail = User.Identity?.Name;
            if (string.IsNullOrEmpty(userEmail))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var adminUser = await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Email == userEmail && u.Role.Name == "Admin");

            if (adminUser == null)
            {
                return NotFound(new { message = "Admin user not found" });
            }

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, adminUser.Password))
            {
                return BadRequest(new { message = "Current password is incorrect" });
            }

            // Update password
            adminUser.Password = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Password changed successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to change password", error = ex.Message });
        }
    }

    /// <summary>
    /// Check if admin setup is required
    /// </summary>
    /// <returns>Setup status</returns>
    /// <response code="200">Setup status retrieved</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("status")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AdminSetupStatusResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetSetupStatus()
    {
        try
        {
            var hasAdminUser = await _context.Users
                .Include(u => u.Role)
                .AnyAsync(u => u.Role.Name == "Admin");

            var hasRoles = await _context.Roles.AnyAsync();
            var canConnect = await _context.Database.CanConnectAsync();

            return Ok(new AdminSetupStatusResponse
            {
                DatabaseConnected = canConnect,
                RolesSeeded = hasRoles,
                AdminUserExists = hasAdminUser,
                SetupRequired = !hasAdminUser || !hasRoles,
                Message = !canConnect ? "Database connection failed" :
                         !hasRoles ? "Roles not seeded" :
                         !hasAdminUser ? "Admin user not created" :
                         "System is properly configured"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Failed to get setup status", error = ex.Message });
        }
    }
}

public class AdminSetupResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string AdminEmail { get; set; } = string.Empty;
    public string DefaultPassword { get; set; } = string.Empty;
    public string Warning { get; set; } = string.Empty;
}

public class AdminSetupStatusResponse
{
    public bool DatabaseConnected { get; set; }
    public bool RolesSeeded { get; set; }
    public bool AdminUserExists { get; set; }
    public bool SetupRequired { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class ChangePasswordRequest
{
    [Required]
    [MinLength(8)]
    public string CurrentPassword { get; set; } = string.Empty;

    [Required]
    [MinLength(8)]
    [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$",
        ErrorMessage = "Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, one number and one special character")]
    public string NewPassword { get; set; } = string.Empty;

    [Required]
    [Compare("NewPassword", ErrorMessage = "Password confirmation does not match")]
    public string ConfirmPassword { get; set; } = string.Empty;
}
