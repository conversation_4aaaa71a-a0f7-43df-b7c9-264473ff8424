import React from 'react';
import { useCubeQuery } from '@cubejs-client/react';
import { BarChart, Bar, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface WorkflowStageChartProps {
  query: any;
  title?: string;
  height?: number;
  onDrillDown?: (status: string, position?: string) => void;
  selectedPosition?: string;
}

// Color mapping for workflow stages
const STAGE_COLORS: Record<string, string> = {
  'DOCUMENT_VERIFICATION_PENDING': '#fbbf24',
  'JUNIOR_ENGINEER_PENDING': '#60a5fa',
  'ASSISTANT_ENGINEER_PENDING': '#34d399',
  'EXECUTIVE_ENGINEER_PENDING': '#a78bfa',
  'CITY_ENGINEER_PENDING': '#f87171',
  'PAYMENT_PENDING': '#fb923c',
  'FINAL_APPROVE': '#10b981',
  'REJECTED': '#ef4444'
};

// Display names for workflow stages
const STAGE_DISPLAY_NAMES: Record<string, string> = {
  'DOCUMENT_VERIFICATION_PENDING': 'Doc Verification',
  'JUNIOR_ENGINEER_PENDING': 'Junior Engineer',
  'ASSISTANT_ENGINEER_PENDING': 'Assistant Engineer',
  'EXECUTIVE_ENGINEER_PENDING': 'Executive Engineer',
  'CITY_ENGINEER_PENDING': 'City Engineer',
  'PAYMENT_PENDING': 'Payment Pending',
  'FINAL_APPROVE': 'Approved',
  'REJECTED': 'Rejected'
};

const WorkflowStageChart: React.FC<WorkflowStageChartProps> = ({
  query,
  title = 'Applications by Workflow Stage',
  height = 400,
  onDrillDown,
  selectedPosition
}) => {
  const { resultSet, isLoading, error } = useCubeQuery(query);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-red-600">
        <div className="text-center">
          <p className="text-lg font-semibold">Error loading chart data</p>
          <p className="text-sm">{error.toString()}</p>
        </div>
      </div>
    );
  }

  if (!resultSet) {
    return (
      <div className="flex items-center justify-center h-96 text-gray-500">
        <p>No data available</p>
      </div>
    );
  }

  const chartData = resultSet.tablePivot().map((row: any) => {
    const status = row['Applications.status'] || 'Unknown';
    const position = row['Applications.position'];
    const count = parseInt(row['Applications.count']) || 0;
    
    return {
      stage: STAGE_DISPLAY_NAMES[status] || status,
      fullStage: status,
      position: position,
      applications: count,
      color: STAGE_COLORS[status] || '#6b7280'
    };
  });

  // Group data by stage if we have position data
  const groupedData = chartData.reduce((acc: any[], item) => {
    const existingStage = acc.find(stage => stage.stage === item.stage);
    if (existingStage) {
      existingStage.applications += item.applications;
      if (item.position) {
        existingStage[item.position] = (existingStage[item.position] || 0) + item.applications;
      }
    } else {
      const newStage: any = {
        stage: item.stage,
        fullStage: item.fullStage,
        applications: item.applications,
        color: item.color
      };
      if (item.position) {
        newStage[item.position] = item.applications;
      }
      acc.push(newStage);
    }
    return acc;
  }, []);

  // Get unique positions for the legend
  const positions = [...new Set(chartData.map(item => item.position).filter(Boolean))];

  const handleBarClick = (data: any) => {
    if (onDrillDown) {
      onDrillDown(data.fullStage, selectedPosition);
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const total = payload.reduce((sum: number, item: any) => sum + (item.value || 0), 0);
      
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg min-w-48">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          {payload.map((item: any, index: number) => (
            <div key={index} className="flex justify-between items-center mb-1">
              <span className="text-sm" style={{ color: item.color }}>
                {item.dataKey === 'applications' ? 'Total' : item.dataKey}:
              </span>
              <span className="font-semibold ml-2">{item.value}</span>
            </div>
          ))}
          {payload.length > 1 && (
            <div className="border-t pt-1 mt-1">
              <div className="flex justify-between items-center font-semibold">
                <span>Total:</span>
                <span>{total}</span>
              </div>
            </div>
          )}
          {onDrillDown && (
            <p className="text-xs text-gray-500 mt-2">Click to view applications</p>
          )}
        </div>
      );
    }
    return null;
  };

  const displayTitle = selectedPosition 
    ? `${title} - ${selectedPosition}` 
    : title;

  const totalApplications = groupedData.reduce((sum, item) => sum + item.applications, 0);

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{displayTitle}</h3>
        <div className="text-sm text-gray-600">
          Total: {totalApplications.toLocaleString()} applications
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={groupedData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
          <XAxis 
            dataKey="stage" 
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            stroke="#6b7280"
          />
          <YAxis stroke="#6b7280" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          
          {positions.length > 0 ? (
            // Show breakdown by position
            <>
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="rect"
              />
              {positions.map((position, index) => (
                <Bar
                  key={position}
                  dataKey={position}
                  stackId="a"
                  fill={STAGE_COLORS[Object.keys(STAGE_COLORS)[index % Object.keys(STAGE_COLORS).length]]}
                  onClick={handleBarClick}
                  style={{ cursor: onDrillDown ? 'pointer' : 'default' }}
                />
              ))}
            </>
          ) : (
            // Show total applications per stage
            <Bar
              dataKey="applications"
              fill="#3b82f6"
              onClick={handleBarClick}
              style={{ cursor: onDrillDown ? 'pointer' : 'default' }}
            />
          )}
        </BarChart>
      </ResponsiveContainer>

      {/* Stage Summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {groupedData.slice(0, 4).map((item, index) => (
            <div 
              key={index} 
              className="text-center p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
              onClick={() => onDrillDown && onDrillDown(item.fullStage)}
            >
              <div 
                className="w-4 h-4 rounded mx-auto mb-1" 
                style={{ backgroundColor: item.color }}
              ></div>
              <p className="text-xs text-gray-600 mb-1">{item.stage}</p>
              <p className="text-sm font-semibold text-gray-900">{item.applications}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WorkflowStageChart;
