using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.DTOs.Reports;
using pmcrms_api.Application.DTOs.Dashboard;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using System.Net;
using ApplicationEntity = pmcrms_api.Domain.Entities.Application;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Comprehensive reporting and analytics endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,Officer")]
[Tags("Reports")]
[Produces("application/json")]
public class ReportsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public ReportsController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    /// <summary>
    /// Get dashboard statistics and overview data
    /// </summary>
    /// <returns>Dashboard statistics including application counts, trends, and health data</returns>
    /// <response code="200">Dashboard data retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("dashboard")]
    [ProducesResponseType(typeof(DashboardResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<DashboardResponse>> GetDashboard()
    {
        try
        {
            var applications = await _unitOfWork.Repository<ApplicationEntity>().GetAllAsync();
            var users = await _unitOfWork.Repository<User>().GetAllAsync();

            // Calculate statistics
            var totalApplications = applications.Count();
            var pending = applications.Count(a => a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING);
            var approved = applications.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE);
            var rejected = applications.Count(a => a.CurrentState == WorkflowStateType.REJECTED);
            var inProgress = applications.Count(a => 
                a.CurrentState != WorkflowStateType.FINAL_APPROVE && 
                a.CurrentState != WorkflowStateType.REJECTED &&
                a.CurrentState != WorkflowStateType.DOCUMENT_VERIFICATION_PENDING);

            // Monthly trend (last 7 months)
            var monthlyTrend = new List<int>();
            for (int i = 6; i >= 0; i--)
            {
                var monthStart = DateTime.Now.AddMonths(-i).Date.AddDays(1 - DateTime.Now.AddMonths(-i).Day);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                var monthCount = applications.Count(a => a.SubmissionDate >= monthStart && a.SubmissionDate <= monthEnd);
                monthlyTrend.Add(monthCount);
            }

            // Position breakdown
            var positionBreakdown = applications
                .GroupBy(a => a.Position.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            // Recent applications
            var recentApplications = applications
                .OrderByDescending(a => a.SubmissionDate)
                .Take(10)
                .Select(a => new RecentApplicationDto
                {
                    Id = a.Id.ToString(),
                    ApplicationNumber = a.ApplicationNumber ?? "N/A",
                    ApplicantName = $"{a.FirstName} {a.LastName}",
                    Position = a.Position.ToString(),
                    Status = a.CurrentState.ToString(),
                    SubmittedDate = a.SubmissionDate,
                    AssignedTo = a.AssignedTo?.FirstName + " " + a.AssignedTo?.LastName ?? "Unassigned"
                })
                .ToList();

            var response = new DashboardResponse
            {
                Success = true,
                Data = new DashboardData
                {
                    TotalApplications = totalApplications,
                    Pending = pending,
                    Approved = approved,
                    Rejected = rejected,
                    InProgress = inProgress,
                    MonthlyTrend = monthlyTrend,
                    PositionBreakdown = positionBreakdown,
                    RecentApplications = recentApplications,
                    SystemHealth = new SystemHealthDto
                    {
                        Status = "Good",
                        Uptime = "99.9%",
                        LastBackup = DateTime.UtcNow.AddHours(-22) // Mock backup time
                    }
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve dashboard data", error = ex.Message });
        }
    }

    /// <summary>
    /// Get comprehensive applications report with tables, charts, and analytics
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>Applications report with summary, table data, and chart data</returns>
    /// <response code="200">Report generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("applications")]
    [ProducesResponseType(typeof(ApplicationsReportResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ApplicationsReportResponse>> GetApplicationsReport(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-1);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(
            a => a.SubmissionDate >= startDate && a.SubmissionDate <= endDate);

        var applicationsWithUsers = applications.ToList(); // Materialize query

        // Summary statistics
        var summary = new ApplicationSummary
        {
            TotalApplications = applicationsWithUsers.Count,
            SubmittedApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING),
            UnderReviewApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING),
            ApprovedApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE),
            RejectedApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.REJECTED),
            PendingPaymentApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.PAYMENT_PENDING),
            CompletedApplications = applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE),
            TotalAmount = applicationsWithUsers.Sum(a => a.Amount),
            ApprovedAmount = applicationsWithUsers.Where(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE).Sum(a => a.Amount),
            ApprovalRate = applicationsWithUsers.Count > 0 ? (double)applicationsWithUsers.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE) / applicationsWithUsers.Count * 100 : 0
        };

        // Table data
        var applicationItems = applicationsWithUsers.Select(a => new ApplicationReportItem
        {
            Id = a.Id,
            ApplicationNumber = a.ApplicationNumber,
            ApplicantName = a.ApplicantName,
            ApplicantEmail = a.EmailAddress, // Updated property name
            Purpose = a.Position.ToString(), // Use Position instead of Purpose
            Amount = a.Amount,
            Status = a.CurrentState.ToString(),
            SubmissionDate = a.SubmissionDate,
            ApprovalDate = a.ApprovalDate,
            ProcessingDays = a.ApprovalDate.HasValue 
                ? (int)(a.ApprovalDate.Value - a.SubmissionDate).TotalDays 
                : (int)(DateTime.UtcNow - a.SubmissionDate).TotalDays
        }).ToList();

        // Status distribution chart
        var statusChart = Enum.GetValues<WorkflowStateType>()
            .Select(status => new ChartDataPoint
            {
                Label = status.ToString(),
                Count = applicationsWithUsers.Count(a => a.CurrentState == status),
                Value = applicationsWithUsers.Count(a => a.CurrentState == status),
                Color = GetStatusColor(status)
            })
            .Where(c => c.Count > 0)
            .ToList();

        // Submission trend
        var submissionTrend = applicationsWithUsers
            .GroupBy(a => a.SubmissionDate.Date)
            .Select(g => new TrendDataPoint
            {
                Date = g.Key,
                Count = g.Count(),
                Value = g.Count(),
                Label = g.Key.ToString("MMM dd")
            })
            .OrderBy(t => t.Date)
            .ToList();

        var response = new ApplicationsReportResponse
        {
            Summary = summary,
            Applications = applicationItems,
            StatusChart = statusChart,
            SubmissionTrend = submissionTrend,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"Applications Report from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Get approved applications report with detailed analysis
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>Approved applications report with analytics</returns>
    /// <response code="200">Report generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("approved-applications")]
    [ProducesResponseType(typeof(ApprovedApplicationsReportResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ApprovedApplicationsReportResponse>> GetApprovedApplicationsReport(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-3);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var approvedApplications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(
            a => a.CurrentState == WorkflowStateType.FINAL_APPROVE
                 && a.ApprovalDate >= startDate && a.ApprovalDate <= endDate);

        var approvedList = approvedApplications.ToList();
        var processingTimes = approvedList
            .Where(a => a.ApprovalDate.HasValue)
            .Select(a => (int)(a.ApprovalDate!.Value - a.SubmissionDate).TotalDays)
            .ToList();

        // Summary
        var summary = new ApprovedApplicationsSummary
        {
            TotalApproved = approvedList.Count,
            TotalApprovedAmount = approvedList.Sum(a => a.Amount),
            AverageApprovalTime = processingTimes.Count > 0 ? (decimal)processingTimes.Average() : 0,
            AverageAmount = approvedList.Count > 0 ? approvedList.Average(a => a.Amount) : 0,
            FastestApprovalDays = processingTimes.Count > 0 ? processingTimes.Min() : 0,
            SlowestApprovalDays = processingTimes.Count > 0 ? processingTimes.Max() : 0
        };

        // Approved applications table
        var approvedItems = approvedList
            .Where(a => a.ApprovalDate.HasValue)
            .Select(a => new ApprovedApplicationItem
            {
                Id = a.Id,
                ApplicationNumber = a.ApplicationNumber,
                ApplicantName = a.ApplicantName,
                Amount = a.Amount,
                SubmissionDate = a.SubmissionDate,
                ApprovalDate = a.ApprovalDate!.Value,
                ApprovalDays = (int)(a.ApprovalDate!.Value - a.SubmissionDate).TotalDays,
                ApprovedBy = "Officer" // TODO: Get from workflow history
            }).ToList();

        // Monthly approvals chart
        var monthlyApprovals = approvedList
            .Where(a => a.ApprovalDate.HasValue)
            .GroupBy(a => new { a.ApprovalDate!.Value.Year, a.ApprovalDate!.Value.Month })
            .Select(g => new ChartDataPoint
            {
                Label = $"{g.Key.Year}-{g.Key.Month:D2}",
                Count = g.Count(),
                Value = g.Sum(a => a.Amount),
                Color = "#4CAF50"
            })
            .OrderBy(c => c.Label)
            .ToList();

        // Amount distribution
        var amountRanges = new[]
        {
            new { Label = "Under 10K", Min = 0m, Max = 10000m, Color = "#FF9800" },
            new { Label = "10K - 50K", Min = 10000m, Max = 50000m, Color = "#2196F3" },
            new { Label = "50K - 100K", Min = 50000m, Max = 100000m, Color = "#4CAF50" },
            new { Label = "Over 100K", Min = 100000m, Max = decimal.MaxValue, Color = "#9C27B0" }
        };

        var amountDistribution = amountRanges.Select(range => new ChartDataPoint
        {
            Label = range.Label,
            Count = approvedList.Count(a => a.Amount >= range.Min && a.Amount < range.Max),
            Value = approvedList.Where(a => a.Amount >= range.Min && a.Amount < range.Max).Sum(a => a.Amount),
            Color = range.Color
        }).Where(c => c.Count > 0).ToList();

        var response = new ApprovedApplicationsReportResponse
        {
            Summary = summary,
            ApprovedApplications = approvedItems,
            MonthlyApprovals = monthlyApprovals,
            AmountDistribution = amountDistribution,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"Approved Applications from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Get application analytics with advanced metrics and visualizations
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>Application analytics with charts and metrics</returns>
    /// <response code="200">Analytics generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("application-analytics")]
    [ProducesResponseType(typeof(ApplicationAnalyticsResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ApplicationAnalyticsResponse>> GetApplicationAnalytics(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-6);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(
            a => a.SubmissionDate >= startDate && a.SubmissionDate <= endDate);

        var appList = applications.ToList();
        var approvedCount = appList.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE);
        var rejectedCount = appList.Count(a => a.CurrentState == WorkflowStateType.REJECTED);
        
        var processingTimes = appList
            .Where(a => a.ApprovalDate.HasValue || a.CurrentState == WorkflowStateType.REJECTED)
            .Select(a => a.ApprovalDate.HasValue 
                ? (int)(a.ApprovalDate.Value - a.SubmissionDate).TotalDays
                : (int)(DateTime.UtcNow - a.SubmissionDate).TotalDays)
            .ToList();

        // Analytics summary
        var summary = new AnalyticsSummary
        {
            TotalApplications = appList.Count,
            ApprovalRate = appList.Count > 0 ? (double)approvedCount / appList.Count * 100 : 0,
            RejectionRate = appList.Count > 0 ? (double)rejectedCount / appList.Count * 100 : 0,
            AverageProcessingDays = processingTimes.Count > 0 ? processingTimes.Average() : 0,
            TotalRequestedAmount = appList.Sum(a => a.Amount),
            TotalApprovedAmount = appList.Where(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE).Sum(a => a.Amount),
            AverageRequestAmount = appList.Count > 0 ? appList.Average(a => a.Amount) : 0
        };

        // Status distribution
        var statusDistribution = Enum.GetValues<WorkflowStateType>()
            .Select(status => new ChartDataPoint
            {
                Label = status.ToString(),
                Count = appList.Count(a => a.CurrentState == status),
                Value = appList.Count(a => a.CurrentState == status),
                Color = GetStatusColor(status)
            })
            .Where(c => c.Count > 0)
            .ToList();

        // Submission trend
        var submissionTrend = appList
            .GroupBy(a => a.SubmissionDate.Date)
            .Select(g => new TrendDataPoint
            {
                Date = g.Key,
                Count = g.Count(),
                Value = g.Count(),
                Label = g.Key.ToString("MMM dd")
            })
            .OrderBy(t => t.Date)
            .ToList();

        // Approval trend
        var approvalTrend = appList
            .Where(a => a.ApprovalDate.HasValue)
            .GroupBy(a => a.ApprovalDate!.Value.Date)
            .Select(g => new TrendDataPoint
            {
                Date = g.Key,
                Count = g.Count(),
                Value = g.Count(),
                Label = g.Key.ToString("MMM dd")
            })
            .OrderBy(t => t.Date)
            .ToList();

        // Amount range distribution
        var amountRanges = new[]
        {
            new { Label = "Under 10K", Min = 0m, Max = 10000m, Color = "#FF9800" },
            new { Label = "10K - 50K", Min = 10000m, Max = 50000m, Color = "#2196F3" },
            new { Label = "50K - 100K", Min = 50000m, Max = 100000m, Color = "#4CAF50" },
            new { Label = "Over 100K", Min = 100000m, Max = decimal.MaxValue, Color = "#9C27B0" }
        };

        var amountRangeDistribution = amountRanges.Select(range => new ChartDataPoint
        {
            Label = range.Label,
            Count = appList.Count(a => a.Amount >= range.Min && a.Amount < range.Max),
            Value = appList.Where(a => a.Amount >= range.Min && a.Amount < range.Max).Sum(a => a.Amount),
            Color = range.Color
        }).Where(c => c.Count > 0).ToList();

        // Processing time distribution
        var timeRanges = new[]
        {
            new { Label = "0-7 days", Min = 0, Max = 7, Color = "#4CAF50" },
            new { Label = "8-14 days", Min = 8, Max = 14, Color = "#FF9800" },
            new { Label = "15-30 days", Min = 15, Max = 30, Color = "#FF5722" },
            new { Label = "Over 30 days", Min = 31, Max = int.MaxValue, Color = "#F44336" }
        };

        var processingTimeDistribution = timeRanges.Select(range => new ChartDataPoint
        {
            Label = range.Label,
            Count = processingTimes.Count(t => t >= range.Min && t <= range.Max),
            Value = processingTimes.Count(t => t >= range.Min && t <= range.Max),
            Color = range.Color
        }).Where(c => c.Count > 0).ToList();

        // Top applicants
        var topApplicants = appList
            .GroupBy(a => new { a.ApplicantName, a.EmailAddress }) // Updated property name
            .Select(g => new TopApplicant
            {
                ApplicantName = g.Key.ApplicantName,
                ApplicantEmail = g.Key.EmailAddress, // Updated property name
                ApplicationCount = g.Count(),
                TotalAmount = g.Sum(a => a.Amount),
                ApprovalRate = g.Count() > 0 ? (double)g.Count(a => a.CurrentState == WorkflowStateType.FINAL_APPROVE) / g.Count() * 100 : 0
            })
            .OrderByDescending(t => t.ApplicationCount)
            .Take(10)
            .ToList();

        var response = new ApplicationAnalyticsResponse
        {
            Summary = summary,
            StatusDistribution = statusDistribution,
            SubmissionTrend = submissionTrend,
            ApprovalTrend = approvalTrend,
            AmountRangeDistribution = amountRangeDistribution,
            ProcessingTimeDistribution = processingTimeDistribution,
            TopApplicants = topApplicants,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"Application Analytics from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Get successful transactions report with revenue analysis
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>Successful transactions report with analytics</returns>
    /// <response code="200">Report generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("transactions/success")]
    [ProducesResponseType(typeof(SuccessfulTransactionsReportResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<SuccessfulTransactionsReportResponse>> GetSuccessfulTransactionsReport(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-1);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var successfulPayments = await _unitOfWork.Repository<Payment>().FindAsync(
            p => p.Status == "Completed" && p.PaymentDate >= startDate && p.PaymentDate <= endDate);

        var paymentList = successfulPayments.ToList();
        var allPayments = await _unitOfWork.Repository<Payment>().FindAsync(
            p => p.PaymentDate >= startDate && p.PaymentDate <= endDate);
        var allPaymentsList = allPayments.ToList();

        // Summary
        var summary = new TransactionSummary
        {
            TotalTransactions = paymentList.Count,
            TotalAmount = paymentList.Sum(p => p.Amount),
            AverageAmount = paymentList.Count > 0 ? paymentList.Average(p => p.Amount) : 0,
            UniqueUsers = paymentList.Select(p => p.ApplicationId).Distinct().Count(),
            SuccessRate = allPaymentsList.Count > 0 ? (double)paymentList.Count / allPaymentsList.Count * 100 : 0
        };

        // Transaction items
        var transactionItems = paymentList.Select(p => new TransactionReportItem
        {
            Id = p.Id,
            PaymentNumber = p.PaymentNumber,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            PaymentDate = p.PaymentDate,
            ApplicationNumber = p.Application?.ApplicationNumber ?? "N/A",
            ApplicantName = p.Application?.ApplicantName ?? "N/A"
        }).ToList();

        // Daily revenue trend
        var dailyRevenue = paymentList
            .GroupBy(p => p.PaymentDate.Date)
            .Select(g => new TrendDataPoint
            {
                Date = g.Key,
                Value = g.Sum(p => p.Amount),
                Count = g.Count(),
                Label = g.Key.ToString("MMM dd")
            })
            .OrderBy(t => t.Date)
            .ToList();

        // Payment method distribution
        var paymentMethodDistribution = paymentList
            .GroupBy(p => p.PaymentMethod)
            .Select(g => new ChartDataPoint
            {
                Label = g.Key,
                Count = g.Count(),
                Value = g.Sum(p => p.Amount),
                Color = GetPaymentMethodColor(g.Key)
            })
            .ToList();

        var response = new SuccessfulTransactionsReportResponse
        {
            Summary = summary,
            Transactions = transactionItems,
            DailyRevenue = dailyRevenue,
            PaymentMethodDistribution = paymentMethodDistribution,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"Successful Transactions from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Get failed transactions report with failure analysis
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>Failed transactions report with analytics</returns>
    /// <response code="200">Report generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("transactions/failed")]
    [ProducesResponseType(typeof(FailedTransactionsReportResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<FailedTransactionsReportResponse>> GetFailedTransactionsReport(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-1);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var failedPayments = await _unitOfWork.Repository<Payment>().FindAsync(
            p => p.Status == "Failed" && p.PaymentDate >= startDate && p.PaymentDate <= endDate);

        var failedList = failedPayments.ToList();
        var allPayments = await _unitOfWork.Repository<Payment>().FindAsync(
            p => p.PaymentDate >= startDate && p.PaymentDate <= endDate);
        var allPaymentsList = allPayments.ToList();

        // Mock failure reasons (in real implementation, this would come from payment gateway logs)
        var failureReasons = new[] { "Insufficient Funds", "Card Expired", "Network Error", "Bank Declined", "Invalid Card" };
        var random = new Random();

        // Summary
        var summary = new FailedTransactionSummary
        {
            TotalFailedTransactions = failedList.Count,
            TotalFailedAmount = failedList.Sum(p => p.Amount),
            FailureRate = allPaymentsList.Count > 0 ? (double)failedList.Count / allPaymentsList.Count * 100 : 0,
            MostCommonFailureReason = failureReasons[random.Next(failureReasons.Length)]
        };

        // Failed transaction items
        var failedItems = failedList.Select(p => new FailedTransactionItem
        {
            Id = p.Id,
            PaymentNumber = p.PaymentNumber,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            AttemptDate = p.PaymentDate,
            FailureReason = failureReasons[random.Next(failureReasons.Length)],
            ApplicationNumber = p.Application?.ApplicationNumber ?? "N/A",
            ApplicantName = p.Application?.ApplicantName ?? "N/A"
        }).ToList();

        // Failure reasons distribution
        var failureReasonsChart = failureReasons.Select(reason => new ChartDataPoint
        {
            Label = reason,
            Count = random.Next(1, failedList.Count / 2 + 1),
            Value = random.Next(1, failedList.Count / 2 + 1),
            Color = GetFailureReasonColor(reason)
        }).ToList();

        // Failure trend
        var failureTrend = failedList
            .GroupBy(p => p.PaymentDate.Date)
            .Select(g => new TrendDataPoint
            {
                Date = g.Key,
                Count = g.Count(),
                Value = g.Sum(p => p.Amount),
                Label = g.Key.ToString("MMM dd")
            })
            .OrderBy(t => t.Date)
            .ToList();

        var response = new FailedTransactionsReportResponse
        {
            Summary = summary,
            FailedTransactions = failedItems,
            FailureReasons = failureReasonsChart,
            FailureTrend = failureTrend,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"Failed Transactions from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Get user activity report with engagement metrics
    /// </summary>
    /// <param name="startDate">Start date for the report (optional)</param>
    /// <param name="endDate">End date for the report (optional)</param>
    /// <returns>User activity report with analytics</returns>
    /// <response code="200">Report generated successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("user-activity")]
    [ProducesResponseType(typeof(UserActivityReportResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<UserActivityReportResponse>> GetUserActivityReport(
        [FromQuery] DateTime? startDate, 
        [FromQuery] DateTime? endDate)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-1);
        endDate ??= DateTime.UtcNow;

        if (startDate > endDate)
        {
            return BadRequest(new { message = "Start date must be before end date" });
        }

        var users = await _unitOfWork.Repository<User>().GetAllAsync();
        var userList = users.ToList();
        var applications = await _unitOfWork.Repository<ApplicationEntity>().GetAllAsync();
        var applicationsList = applications.ToList();

        var newUsersInPeriod = userList.Count(u => u.CreatedAt >= startDate && u.CreatedAt <= endDate);
        var random = new Random(); // Mock data for login activity

        // Summary
        var summary = new UserActivitySummary
        {
            TotalUsers = userList.Count,
            ActiveUsers = userList.Count(u => u.IsActive),
            InactiveUsers = userList.Count(u => !u.IsActive),
            NewUsersInPeriod = newUsersInPeriod,
            ActivityRate = userList.Count > 0 ? (double)userList.Count(u => u.IsActive) / userList.Count * 100 : 0,
            TotalLogins = random.Next(userList.Count * 5, userList.Count * 20), // Mock data
            AverageLoginsPerUser = userList.Count > 0 ? random.NextDouble() * 10 + 5 : 0 // Mock data
        };

        // User activity items
        var userActivities = userList.Select(u => new UserActivityItem
        {
            UserId = u.Id,
            UserName = $"{u.FirstName} {u.LastName}",
            Email = u.Email,
            Role = u.Role?.Name ?? "User",
            LastLoginDate = DateTime.UtcNow.AddDays(-random.Next(0, 30)), // Mock data
            LoginCount = random.Next(1, 50), // Mock data
            ApplicationsSubmitted = applicationsList.Count(a => a.UserId == u.Id),
            IsActive = u.IsActive,
            JoinDate = u.CreatedAt
        }).ToList();

        // User type distribution
        var userTypeDistribution = userList
            .GroupBy(u => u.Role?.Name ?? "User")
            .Select(g => new ChartDataPoint
            {
                Label = g.Key,
                Count = g.Count(),
                Value = g.Count(),
                Color = GetRoleColor(g.Key)
            })
            .ToList();

        // Login trend (mock data)
        var loginTrend = Enumerable.Range(0, (int)(endDate.Value - startDate.Value).TotalDays + 1)
            .Select(i => startDate.Value.AddDays(i))
            .Select(date => new TrendDataPoint
            {
                Date = date,
                Count = random.Next(userList.Count / 4, userList.Count / 2),
                Value = random.Next(userList.Count / 4, userList.Count / 2),
                Label = date.ToString("MMM dd")
            })
            .ToList();

        // Top active users
        var topActiveUsers = userActivities
            .Select(u => new TopActiveUser
            {
                UserName = u.UserName,
                Email = u.Email,
                Role = u.Role,
                ActivityScore = u.LoginCount + (u.ApplicationsSubmitted * 5), // Custom scoring
                LoginCount = u.LoginCount,
                ApplicationCount = u.ApplicationsSubmitted
            })
            .OrderByDescending(u => u.ActivityScore)
            .Take(10)
            .ToList();

        var response = new UserActivityReportResponse
        {
            Summary = summary,
            UserActivities = userActivities,
            UserTypeDistribution = userTypeDistribution,
            LoginTrend = loginTrend,
            TopActiveUsers = topActiveUsers,
            Period = new ReportPeriod
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Description = $"User Activity from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
            },
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = User.Identity?.Name ?? "System"
        };

        return Ok(response);
    }

    /// <summary>
    /// Export any report as Excel file
    /// </summary>
    /// <param name="reportType">Type of report to export (applications, approved-applications, etc.)</param>
    /// <param name="request">Export parameters</param>
    /// <returns>Excel file download</returns>
    /// <response code="200">Excel file generated successfully</response>
    /// <response code="400">Invalid report type or parameters</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("export/{reportType}/excel")]
    [ProducesResponseType(typeof(FileResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ExportReportExcel(string reportType, [FromBody] ExportRequest request)
    {
        // TODO: Implement actual Excel generation using a library like EPPlus or ClosedXML
        // This is a placeholder implementation
        
        var validReportTypes = new[] { "applications", "approved-applications", "application-analytics", "transactions-success", "transactions-failed", "user-activity" };
        if (!validReportTypes.Contains(reportType.ToLower()))
        {
            return BadRequest(new { message = "Invalid report type" });
        }

        var content = System.Text.Encoding.UTF8.GetBytes($"{reportType} Report Export - {DateTime.UtcNow:yyyy-MM-dd}");
        var fileName = $"{reportType}_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.xlsx";
        
        return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
    }

    /// <summary>
    /// Export any report as PDF file
    /// </summary>
    /// <param name="reportType">Type of report to export</param>
    /// <param name="request">Export parameters</param>
    /// <returns>PDF file download</returns>
    /// <response code="200">PDF file generated successfully</response>
    /// <response code="400">Invalid report type or parameters</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("export/{reportType}/pdf")]
    [ProducesResponseType(typeof(FileResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ExportReportPdf(string reportType, [FromBody] ExportRequest request)
    {
        // TODO: Implement actual PDF generation using QuestPDF
        // This is a placeholder implementation
        
        var validReportTypes = new[] { "applications", "approved-applications", "application-analytics", "transactions-success", "transactions-failed", "user-activity" };
        if (!validReportTypes.Contains(reportType.ToLower()))
        {
            return BadRequest(new { message = "Invalid report type" });
        }

        var content = System.Text.Encoding.UTF8.GetBytes($"{reportType} Report Export - {DateTime.UtcNow:yyyy-MM-dd}");
        var fileName = $"{reportType}_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.pdf";
        
        return File(content, "application/pdf", fileName);
    }

    #region Helper Methods

    private static string GetStatusColor(WorkflowStateType status)
    {
        return status switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => "#2196F3",
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => "#FF9800",
            WorkflowStateType.FINAL_APPROVE => "#4CAF50",
            WorkflowStateType.REJECTED => "#F44336",
            WorkflowStateType.PAYMENT_PENDING => "#9C27B0",
            WorkflowStateType.CLERK_PENDING => "#00BCD4",
            _ => "#9E9E9E"
        };
    }

    private static string GetPaymentMethodColor(string paymentMethod)
    {
        return paymentMethod.ToLower() switch
        {
            "credit card" => "#FF5722",
            "debit card" => "#3F51B5",
            "upi" => "#4CAF50",
            "net banking" => "#FF9800",
            "wallet" => "#9C27B0",
            _ => "#9E9E9E"
        };
    }

    private static string GetFailureReasonColor(string reason)
    {
        return reason switch
        {
            "Insufficient Funds" => "#F44336",
            "Card Expired" => "#FF5722",
            "Network Error" => "#FF9800",
            "Bank Declined" => "#E91E63",
            "Invalid Card" => "#9C27B0",
            _ => "#9E9E9E"
        };
    }

    private static string GetRoleColor(string role)
    {
        return role.ToLower() switch
        {
            "admin" => "#F44336",
            "officer" => "#2196F3",
            "user" => "#4CAF50",
            _ => "#9E9E9E"
        };
    }

    #endregion
}
