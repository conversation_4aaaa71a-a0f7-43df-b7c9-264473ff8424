cube(`Applications`, {
  sql: `
    SELECT 
      Id,
      ApplicationNumber,
      ApplicantName,
      Position,
      Status,
      SubmittedDate,
      LastUpdated,
      AssignedTo,
      AssignedToUserId,
      FirstName,
      LastName,
      Email,
      PhoneNumber,
      Experience,
      Qualification,
      CreatedDate,
      DATEDIFF(day, SubmittedDate, COALESCE(LastUpdated, GETUTCDATE())) as ProcessingDays,
      CASE 
        WHEN Status IN ('DOCUMENT_VERIFICATION_PENDING', 'JUNIOR_ENGINEER_PENDING', 'ASSISTANT_ENGINEER_PENDING', 'EXECUTIVE_ENGINEER_PENDING', 'CITY_ENGINEER_PENDING', 'PAYMENT_PENDING') 
        THEN 1 
        ELSE 0 
      END as IsPending,
      CASE 
        WHEN Status = 'FINAL_APPROVE' 
        THEN 1 
        ELSE 0 
      END as IsApproved,
      CASE 
        WHEN Status = 'REJECTED' 
        THEN 1 
        ELSE 0 
      END as IsRejected
    FROM Applications 
    WHERE IsDeleted = 0
  `,

  measures: {
    count: {
      type: `count`,
      title: `Total Applications`,
      description: `Total number of applications`
    },

    pendingCount: {
      type: `sum`,
      sql: `IsPending`,
      title: `Pending Applications`,
      description: `Applications awaiting review`
    },

    approvedCount: {
      type: `sum`,
      sql: `IsApproved`,
      title: `Approved Applications`,
      description: `Applications that have been approved`
    },

    rejectedCount: {
      type: `sum`,
      sql: `IsRejected`,
      title: `Rejected Applications`,
      description: `Applications that have been rejected`
    },

    averageProcessingTime: {
      type: `avg`,
      sql: `ProcessingDays`,
      title: `Average Processing Time`,
      description: `Average time to process applications (in days)`
    },

    maxProcessingTime: {
      type: `max`,
      sql: `ProcessingDays`,
      title: `Maximum Processing Time`,
      description: `Maximum time taken to process an application`
    },

    minProcessingTime: {
      type: `min`,
      sql: `ProcessingDays`,
      title: `Minimum Processing Time`,
      description: `Minimum time taken to process an application`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `string`,
      primaryKey: true
    },

    applicationNumber: {
      sql: `ApplicationNumber`,
      type: `string`,
      title: `Application Number`,
      description: `Unique application identifier`
    },

    applicantName: {
      sql: `ApplicantName`,
      type: `string`,
      title: `Applicant Name`,
      description: `Name of the applicant`
    },

    firstName: {
      sql: `FirstName`,
      type: `string`,
      title: `First Name`
    },

    lastName: {
      sql: `LastName`,
      type: `string`,
      title: `Last Name`
    },

    email: {
      sql: `Email`,
      type: `string`,
      title: `Email Address`
    },

    phoneNumber: {
      sql: `PhoneNumber`,
      type: `string`,
      title: `Phone Number`
    },

    position: {
      sql: `Position`,
      type: `string`,
      title: `Position`,
      description: `Professional position (Architect, License Engineer, etc.)`
    },

    status: {
      sql: `Status`,
      type: `string`,
      title: `Application Status`,
      description: `Current workflow status of the application`
    },

    assignedTo: {
      sql: `AssignedTo`,
      type: `string`,
      title: `Assigned Officer`,
      description: `Officer assigned to review the application`
    },

    assignedToUserId: {
      sql: `AssignedToUserId`,
      type: `number`,
      title: `Assigned Officer ID`
    },

    experience: {
      sql: `Experience`,
      type: `string`,
      title: `Work Experience`
    },

    qualification: {
      sql: `Qualification`,
      type: `string`,
      title: `Educational Qualification`
    },

    submittedDate: {
      sql: `SubmittedDate`,
      type: `time`,
      title: `Submitted Date`,
      description: `Date when application was submitted`
    },

    lastUpdated: {
      sql: `LastUpdated`,
      type: `time`,
      title: `Last Updated`,
      description: `Date when application was last updated`
    },

    createdDate: {
      sql: `CreatedDate`,
      type: `time`,
      title: `Created Date`
    },

    processingDays: {
      sql: `ProcessingDays`,
      type: `number`,
      title: `Processing Days`,
      description: `Number of days in processing`
    },

    // Derived dimensions for grouping
    submittedMonth: {
      sql: `FORMAT(SubmittedDate, 'yyyy-MM')`,
      type: `string`,
      title: `Submitted Month`
    },

    submittedYear: {
      sql: `YEAR(SubmittedDate)`,
      type: `number`,
      title: `Submitted Year`
    },

    statusCategory: {
      sql: `
        CASE 
          WHEN Status IN ('DOCUMENT_VERIFICATION_PENDING', 'JUNIOR_ENGINEER_PENDING', 'ASSISTANT_ENGINEER_PENDING') 
          THEN 'In Review'
          WHEN Status IN ('EXECUTIVE_ENGINEER_PENDING', 'CITY_ENGINEER_PENDING') 
          THEN 'Final Review'
          WHEN Status = 'PAYMENT_PENDING' 
          THEN 'Payment'
          WHEN Status = 'FINAL_APPROVE' 
          THEN 'Completed'
          WHEN Status = 'REJECTED' 
          THEN 'Rejected'
          ELSE 'Other'
        END
      `,
      type: `string`,
      title: `Status Category`,
      description: `Grouped status categories`
    }
  },

  segments: {
    pending: {
      sql: `${CUBE}.Status IN ('DOCUMENT_VERIFICATION_PENDING', 'JUNIOR_ENGINEER_PENDING', 'ASSISTANT_ENGINEER_PENDING', 'EXECUTIVE_ENGINEER_PENDING', 'CITY_ENGINEER_PENDING', 'PAYMENT_PENDING')`
    },

    approved: {
      sql: `${CUBE}.Status = 'FINAL_APPROVE'`
    },

    rejected: {
      sql: `${CUBE}.Status = 'REJECTED'`
    },

    thisMonth: {
      sql: `${CUBE}.SubmittedDate >= DATEADD(month, DATEDIFF(month, 0, GETUTCDATE()), 0)`
    },

    lastMonth: {
      sql: `${CUBE}.SubmittedDate >= DATEADD(month, DATEDIFF(month, 0, GETUTCDATE()) - 1, 0) 
            AND ${CUBE}.SubmittedDate < DATEADD(month, DATEDIFF(month, 0, GETUTCDATE()), 0)`
    },

    thisYear: {
      sql: `YEAR(${CUBE}.SubmittedDate) = YEAR(GETUTCDATE())`
    }
  },

  preAggregations: {
    // Daily aggregation for performance
    dailyStats: {
      measures: [
        CUBE.count,
        CUBE.pendingCount,
        CUBE.approvedCount,
        CUBE.rejectedCount,
        CUBE.averageProcessingTime
      ],
      dimensions: [
        CUBE.position,
        CUBE.status
      ],
      timeDimension: CUBE.submittedDate,
      granularity: `day`,
      refreshKey: {
        every: `1 hour`,
      },
    },

    // Monthly aggregation for trends
    monthlyStats: {
      measures: [
        CUBE.count,
        CUBE.pendingCount,
        CUBE.approvedCount,
        CUBE.rejectedCount
      ],
      dimensions: [
        CUBE.position,
        CUBE.statusCategory
      ],
      timeDimension: CUBE.submittedDate,
      granularity: `month`,
      refreshKey: {
        every: `1 day`,
      },
    },

    // Position and status breakdown
    positionStatusBreakdown: {
      measures: [CUBE.count],
      dimensions: [
        CUBE.position,
        CUBE.status,
        CUBE.assignedTo
      ],
      refreshKey: {
        every: `30 minutes`,
      },
    }
  },

  dataSource: `default`
});
