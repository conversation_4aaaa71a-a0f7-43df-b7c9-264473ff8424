using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace pmcrms_api.Application.Services;

public class ApplicationRoutingService : IApplicationRoutingService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ApplicationRoutingService> _logger;

    public ApplicationRoutingService(IUnitOfWork unitOfWork, ILogger<ApplicationRoutingService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<User?> GetNextEngineerForPositionAsync(PositionType position, RoleType engineerLevel = RoleType.JRENGG_ARCH)
    {
        var targetRole = GetRoleForPosition(position, engineerLevel);
        
        var roleRepository = _unitOfWork.Repository<Role>();
        var userRepository = _unitOfWork.Repository<User>();

        // Find the role
        var roles = await roleRepository.FindAsync(r => r.Name == targetRole.ToString());
        var role = roles.FirstOrDefault();
        
        if (role == null)
        {
            _logger.LogWarning("Role {Role} not found for position {Position}", targetRole, position);
            return null;
        }

        // Find active users with this role
        var engineers = await userRepository.FindAsync(u => u.RoleId == role.Id && u.IsActive);
        var availableEngineers = engineers.ToList();

        if (!availableEngineers.Any())
        {
            _logger.LogWarning("No available engineers found for role {Role}", targetRole);
            return null;
        }

        // For now, use round-robin assignment (can be enhanced with workload balancing)
        var selectedEngineer = availableEngineers.OrderBy(e => e.Id).First();
        
        _logger.LogInformation("Assigned application to engineer {EngineerId} with role {Role} for position {Position}", 
            selectedEngineer.Id, targetRole, position);
        
        return selectedEngineer;
    }

    public async Task<bool> AssignApplicationToEngineerAsync(Domain.Entities.Application application, User engineer)
    {
        try
        {
            application.AssignedToUserId = engineer.Id;
            application.AssignedTo = engineer;
            application.AssignedAt = DateTime.UtcNow;

            _unitOfWork.Repository<Domain.Entities.Application>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Application {ApplicationId} assigned to engineer {EngineerId}", 
                application.Id, engineer.Id);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to assign application {ApplicationId} to engineer {EngineerId}", 
                application.Id, engineer.Id);
            return false;
        }
    }

    public async Task<IEnumerable<User>> GetAvailableEngineersForPositionAsync(PositionType position, RoleType engineerLevel = RoleType.JRENGG_ARCH)
    {
        var targetRole = GetRoleForPosition(position, engineerLevel);
        
        var roleRepository = _unitOfWork.Repository<Role>();
        var userRepository = _unitOfWork.Repository<User>();

        var roles = await roleRepository.FindAsync(r => r.Name == targetRole.ToString());
        var role = roles.FirstOrDefault();
        
        if (role == null)
        {
            return Enumerable.Empty<User>();
        }

        var engineers = await userRepository.FindAsync(u => u.RoleId == role.Id && u.IsActive);
        return engineers;
    }

    public async Task<Domain.Entities.Application> RouteApplicationAsync(Domain.Entities.Application application)
    {
        try
        {
            // Start with Junior Engineer level
            var engineer = await GetNextEngineerForPositionAsync(application.Position, RoleType.JRENGG_ARCH);
            
            // If no Junior Engineer available, try Assistant Engineer
            if (engineer == null)
            {
                engineer = await GetNextEngineerForPositionAsync(application.Position, RoleType.ASSIENGG_ARCH);
            }

            if (engineer != null)
            {
                await AssignApplicationToEngineerAsync(application, engineer);
                _logger.LogInformation("Application {ApplicationNumber} routed to engineer {EngineerId} for position {Position}", 
                    application.ApplicationNumber, engineer.Id, application.Position);
            }
            else
            {
                _logger.LogWarning("No available engineer found for application {ApplicationNumber} with position {Position}", 
                    application.ApplicationNumber, application.Position);
            }

            return application;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to route application {ApplicationId}", application.Id);
            throw;
        }
    }

    private static RoleType GetRoleForPosition(PositionType position, RoleType engineerLevel)
    {
        return position switch
        {
            PositionType.Architect => engineerLevel switch
            {
                RoleType.JRENGG_ARCH => RoleType.JRENGG_ARCH,
                RoleType.ASSIENGG_ARCH => RoleType.ASSIENGG_ARCH,
                _ => RoleType.JRENGG_ARCH
            },
            PositionType.StructuralEngineer => engineerLevel switch
            {
                RoleType.JRENGG_STRU => RoleType.JRENGG_STRU,
                RoleType.ASSIENGG_STRU => RoleType.ASSIENGG_STRU,
                _ => RoleType.JRENGG_STRU
            },
            PositionType.LicenseEngineer => engineerLevel switch
            {
                RoleType.JRENGG_LICE => RoleType.JRENGG_LICE,
                RoleType.ASSIENGG_LICE => RoleType.ASSIENGG_LICE,
                _ => RoleType.JRENGG_LICE
            },
            PositionType.Supervisor1 => engineerLevel switch
            {
                RoleType.JRENGG_SUPER1 => RoleType.JRENGG_SUPER1,
                RoleType.ASSIENGG_SUPER1 => RoleType.ASSIENGG_SUPER1,
                _ => RoleType.JRENGG_SUPER1
            },
            PositionType.Supervisor2 => engineerLevel switch
            {
                RoleType.JRENGG_SUPER2 => RoleType.JRENGG_SUPER2,
                RoleType.ASSIENGG_SUPER2 => RoleType.ASSIENGG_SUPER2,
                _ => RoleType.JRENGG_SUPER2
            },
            _ => RoleType.JRENGG_ARCH // Default to Architect Junior Engineer
        };
    }
}
