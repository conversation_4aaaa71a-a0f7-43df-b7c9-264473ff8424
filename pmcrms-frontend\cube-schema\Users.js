cube(`Users`, {
  sql: `
    SELECT 
      Id,
      FirstName,
      LastName,
      CONCAT(FirstName, ' ', LastName) as <PERSON>Name,
      Email,
      PhoneNumber,
      Role,
      IsActive,
      CreatedDate,
      LastLogin,
      Department,
      CASE WHEN IsActive = 1 THEN 1 ELSE 0 END as ActiveUser,
      CASE WHEN LastLogin >= DATEADD(day, -30, GETUTCDATE()) THEN 1 ELSE 0 END as RecentlyActive
    FROM Users
  `,

  measures: {
    count: {
      type: `count`,
      title: `Total Users`,
      description: `Total number of users in the system`
    },

    activeCount: {
      type: `sum`,
      sql: `ActiveUser`,
      title: `Active Users`,
      description: `Number of active users`
    },

    recentlyActiveCount: {
      type: `sum`,
      sql: `RecentlyActive`,
      title: `Recently Active Users`,
      description: `Users active in the last 30 days`
    },

    // Performance metrics (would require joining with Applications table)
    assignedApplicationsCount: {
      type: `count`,
      sql: `Id`,
      title: `Applications Assigned`,
      description: `Number of applications assigned to users`,
      // This would typically be calculated via a join or separate measure
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `number`,
      primaryKey: true
    },

    firstName: {
      sql: `FirstName`,
      type: `string`,
      title: `First Name`
    },

    lastName: {
      sql: `LastName`,
      type: `string`,
      title: `Last Name`
    },

    fullName: {
      sql: `FullName`,
      type: `string`,
      title: `Full Name`,
      description: `Complete name of the user`
    },

    email: {
      sql: `Email`,
      type: `string`,
      title: `Email Address`
    },

    phoneNumber: {
      sql: `PhoneNumber`,
      type: `string`,
      title: `Phone Number`
    },

    role: {
      sql: `Role`,
      type: `string`,
      title: `User Role`,
      description: `Role of the user (Admin, Junior Engineer, etc.)`
    },

    department: {
      sql: `Department`,
      type: `string`,
      title: `Department`
    },

    isActive: {
      sql: `IsActive`,
      type: `boolean`,
      title: `Is Active`
    },

    createdDate: {
      sql: `CreatedDate`,
      type: `time`,
      title: `Created Date`,
      description: `Date when user account was created`
    },

    lastLogin: {
      sql: `LastLogin`,
      type: `time`,
      title: `Last Login`,
      description: `Date of last login`
    },

    // Derived dimensions
    createdMonth: {
      sql: `FORMAT(CreatedDate, 'yyyy-MM')`,
      type: `string`,
      title: `Created Month`
    },

    roleCategory: {
      sql: `
        CASE 
          WHEN Role = 'ADMIN' THEN 'Administrative'
          WHEN Role IN ('JUNIOR_ENGINEER', 'ASSISTANT_ENGINEER', 'EXECUTIVE_ENGINEER') THEN 'Engineering'
          WHEN Role = 'CITY_ENGINEER' THEN 'City Planning'
          WHEN Role = 'CLERK' THEN 'Support'
          ELSE 'Other'
        END
      `,
      type: `string`,
      title: `Role Category`
    }
  },

  segments: {
    activeUsers: {
      sql: `${CUBE}.IsActive = 1`
    },

    inactiveUsers: {
      sql: `${CUBE}.IsActive = 0`
    },

    recentlyActive: {
      sql: `${CUBE}.LastLogin >= DATEADD(day, -30, GETUTCDATE())`
    },

    engineers: {
      sql: `${CUBE}.Role IN ('JUNIOR_ENGINEER', 'ASSISTANT_ENGINEER', 'EXECUTIVE_ENGINEER', 'CITY_ENGINEER')`
    },

    admins: {
      sql: `${CUBE}.Role = 'ADMIN'`
    }
  },

  preAggregations: {
    usersByRole: {
      measures: [CUBE.count, CUBE.activeCount],
      dimensions: [CUBE.role, CUBE.department],
      refreshKey: {
        every: `1 hour`,
      },
    },

    monthlyUserStats: {
      measures: [CUBE.count, CUBE.recentlyActiveCount],
      dimensions: [CUBE.roleCategory],
      timeDimension: CUBE.createdDate,
      granularity: `month`,
      refreshKey: {
        every: `1 day`,
      },
    }
  },

  dataSource: `default`
});
