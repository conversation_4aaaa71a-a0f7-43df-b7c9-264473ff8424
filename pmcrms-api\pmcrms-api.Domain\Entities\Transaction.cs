using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

/// <summary>
/// Transaction Entity as per PromptFile5.pdf requirements
/// Complete structure matching the prompt specifications
/// </summary>
public class Transaction
{
    [Key]
    public int Id { get; set; }

    // ===========================================
    // CORE TRANSACTION FIELDS (Required by Prompt)
    // ===========================================
    [Required]
    [StringLength(100)]
    public string ApplicationId { get; set; } = string.Empty; // Reference to Application

    [Required]
    [StringLength(100)]
    public string TransactionId { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Amount { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Status { get; set; } = string.Empty;

    [Required]
    public DateTime TransactionDate { get; set; } = DateTime.UtcNow; // Format: dd/MM/yyyy

    public string? Price { get; set; }
    public string? OrderId { get; set; }
    public string? Discount { get; set; }

    // ===========================================
    // BANK DETAILS
    // ===========================================
    public string? BankReceipt { get; set; }
    public string? ReceiptNumber { get; set; }
    public string? NameOfBank { get; set; }
    public string? BankRefNumber { get; set; }

    // ===========================================
    // PAYMENT DETAILS
    // ===========================================
    public string? AmountPaid { get; set; }
    public string? PaymentMode { get; set; } // Reference: [6767ee7ede77446a09d3e15e74]
    public string? Name { get; set; }
    public string? CardType { get; set; }
    public string? Mode { get; set; }
    public string? NameOnCard { get; set; }

    // ===========================================
    // CUSTOMER INFORMATION
    // ===========================================
    public string? FirstName { get; set; }
    public string? LastName { get; set; }

    // ===========================================
    // FILES & DOCUMENTATION
    // ===========================================
    public string? AttachmentFilePath { get; set; } // For image/jpeg attachments

    // ===========================================
    // TRANSACTION STATUS & ERRORS
    // ===========================================
    public string? ErrorMessage { get; set; }
    public string? TransactionDesc { get; set; }
    public string? TransactionError { get; set; }

    // ===========================================
    // SYSTEM FIELDS
    // ===========================================
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }

    // ===========================================
    // NAVIGATION PROPERTIES
    // ===========================================
    public int ApplicationEntityId { get; set; } // FK to Application table
    public Application Application { get; set; } = null!;

    public int? PaymentId { get; set; }
    public Payment? Payment { get; set; }
}
