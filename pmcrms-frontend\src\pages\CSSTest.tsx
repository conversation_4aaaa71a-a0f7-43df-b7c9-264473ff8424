import React from 'react'

const CSSTest: React.FC = () => {
  return (
    <div className="p-8 space-y-4">
      <h1 className="text-3xl font-bold text-center text-blue-600">
        🎨 Tailwind CSS Test Page
      </h1>
      
      {/* Test 1: Basic colors and spacing */}
      <div className="p-6 bg-red-500 text-white rounded-lg">
        ✅ TEST 1: Red background, white text, padding, rounded corners
      </div>
      
      {/* Test 2: Gradient */}
      <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded">
        ✅ TEST 2: Blue to purple gradient
      </div>
      
      {/* Test 3: Flexbox utilities */}
      <div className="flex items-center justify-center h-20 bg-green-400 text-white rounded">
        ✅ TEST 3: Centered with flexbox
      </div>
      
      {/* Test 4: Shadow and borders */}
      <div className="p-4 bg-white border-2 border-gray-300 rounded-lg shadow-lg">
        ✅ TEST 4: White background, border, shadow
      </div>
      
      {/* Test 5: Responsive utilities */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="p-3 bg-yellow-200 rounded">Column 1</div>
        <div className="p-3 bg-yellow-300 rounded">Column 2</div>
        <div className="p-3 bg-yellow-400 rounded">Column 3</div>
      </div>
      
      {/* Test 6: Custom colors from theme */}
      <div 
        className="p-4 text-white rounded"
        style={{ backgroundColor: 'hsl(var(--primary))' }}
      >
        ✅ TEST 6: Custom CSS variable color
      </div>
    </div>
  )
}

export default CSSTest
