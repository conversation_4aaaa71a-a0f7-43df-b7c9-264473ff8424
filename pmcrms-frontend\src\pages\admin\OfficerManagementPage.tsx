import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Badge } from '../../components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog'
import { useToast } from '../../hooks/use-toast'
import { 
  UserPlus, 
  Users, 
  Search, 
  Filter, 
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'
import { api } from '../../lib/api'
import AdminLayout from '../../components/AdminLayout'

interface User {
  id: number
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  role: string
  isActive: boolean
  createdDate: string
  lastLogin: string | null
  department: string
  assignedApplicationsCount: number
}

interface CreateUserRequest {
  email: string
  role: string
}

// Officer role types based on the existing system
const OFFICER_ROLES = [
  { value: 'JRENGG_ARCH', label: 'Junior Engineer - Architect' },
  { value: 'JRENGG_STRU', label: 'Junior Engineer - Structural' },
  { value: 'JRENGG_LICE', label: 'Junior Engineer - License' },
  { value: 'JRENGG_SUPER1', label: 'Junior Engineer - Supervisor 1' },
  { value: 'JRENGG_SUPER2', label: 'Junior Engineer - Supervisor 2' },
  { value: 'ASSIENGG_ARCH', label: 'Assistant Engineer - Architect' },
  { value: 'ASSIENGG_STRU', label: 'Assistant Engineer - Structural' },
  { value: 'ASSIENGG_LICE', label: 'Assistant Engineer - License' },
  { value: 'ASSIENGG_SUPER1', label: 'Assistant Engineer - Supervisor 1' },
  { value: 'ASSIENGG_SUPER2', label: 'Assistant Engineer - Supervisor 2' },
  { value: 'ExecutiveEngineer', label: 'Executive Engineer' },
  { value: 'CityEngineer', label: 'City Engineer' },
  { value: 'Clerk', label: 'Clerk' },
  { value: 'OfflinePaymentOfficer', label: 'Offline Payment Officer' }
]

const OfficerManagementPage: React.FC = () => {
  const { toast } = useToast()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [pendingInvitations, setPendingInvitations] = useState<any[]>([])
  const [reinviting, setReinviting] = useState<number | null>(null)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false)
  const [userApplications, setUserApplications] = useState<any[]>([])
  const [loadingApplications, setLoadingApplications] = useState(false)
  
  const [createForm, setCreateForm] = useState<CreateUserRequest>({
    email: '',
    role: ''
  })

  useEffect(() => {
    loadUsers()
    loadPendingInvitations()
  }, [])

  const loadUsers = async () => {
    try {
      setLoading(true)
      const response = await api.get('/admin/officers')
      
      console.log('API Response:', response) // Debug log
      console.log('Response data:', response.data) // Debug log
      
      if (response.success && response.data) {
        // Map backend response to frontend User interface
        const officerUsers = Array.isArray(response.data) 
          ? response.data.map((officer: any) => ({
              id: officer.id,
              firstName: officer.firstName,
              lastName: officer.lastName,
              email: officer.email,
              phoneNumber: officer.phoneNumber || '',
              role: officer.role,
              isActive: officer.isActive,
              createdDate: officer.createdAt || new Date().toISOString(),
              lastLogin: officer.lastLogin || null,
              department: officer.department || 'N/A',
              assignedApplicationsCount: officer.assignedApplicationsCount || 0
            }))
          : []
        
        console.log('Mapped officers:', officerUsers) // Debug log
        setUsers(officerUsers)
      } else {
        console.log('No data or response not successful:', response) // Debug log
      }
    } catch (error: any) {
      console.error('Failed to load users:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load officers',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadPendingInvitations = async () => {
    try {
      const response = await api.get('/admin/officers/pending')
      
      if (response.success && response.data) {
        setPendingInvitations(Array.isArray(response.data) ? response.data : [])
      }
    } catch (error: any) {
      console.error('Failed to load pending invitations:', error)
      toast({
        title: 'Error',
        description: 'Failed to load pending invitations',
        variant: 'destructive'
      })
    }
  }

  const handleReinvite = async (officerId: number) => {
    try {
      setReinviting(officerId)
      const response = await api.post(`/admin/officers/${officerId}/reinvite`)
      
      if (response.success) {
        toast({
          title: 'Reinvitation Sent',
          description: 'Officer reinvitation sent successfully.',
        })
        // Reload pending invitations
        loadPendingInvitations()
      }
    } catch (error: any) {
      console.error('Failed to reinvite officer:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to send reinvitation',
        variant: 'destructive'
      })
    } finally {
      setReinviting(null)
    }
  }

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!createForm.email || !createForm.role) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      })
      return
    }

    try {
      setCreating(true)
      
      // Prepare request with placeholder names since they'll be filled during profile setup
      const inviteRequest = {
        email: createForm.email,
        role: createForm.role,
        firstName: 'Officer', // Placeholder - will be updated when officer sets up profile
        lastName: 'User', // Placeholder - will be updated when officer sets up profile
        phoneNumber: '' // Optional field
      }
      
      const response = await api.post('/admin/officers/invite', inviteRequest)
      
      if (response.success) {
        toast({
          title: 'Invitation Sent',
          description: 'Officer invitation sent successfully. They will receive an email to set up their account.',
        })
        
        // Reset form and close dialog
        setCreateForm({
          email: '',
          role: ''
        })
        setShowCreateDialog(false)
        
        // Reload the users list and pending invitations
        loadUsers()
        loadPendingInvitations()
      }
    } catch (error: any) {
      console.error('Failed to create user:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create officer',
        variant: 'destructive'
      })
    } finally {
      setCreating(false)
    }
  }

  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    try {
      const user = users.find(u => u.id === userId)
      if (!user) return

      const updateData = {
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        role: user.role,
        isActive: !currentStatus
      }

      await api.put(`/users/${userId}`, updateData)
      
      setUsers(users.map(u => 
        u.id === userId ? { ...u, isActive: !currentStatus } : u
      ))
      
      toast({
        title: 'Success',
        description: `Officer ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
      })
    } catch (error: any) {
      console.error('Failed to update user status:', error)
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update officer status',
        variant: 'destructive'
      })
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    
    return matchesSearch && matchesRole
  })

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'EXECUTIVE_ENGINEER':
      case 'CITY_ENGINEER':
        return 'bg-purple-100 text-purple-800'
      case 'ASSIENGG_ARCH':
      case 'ASSIENGG_STRU':
      case 'ASSIENGG_LICE':
      case 'ASSIENGG_SUPER1':
      case 'ASSIENGG_SUPER2':
        return 'bg-blue-100 text-blue-800'
      case 'JRENGG_ARCH':
      case 'JRENGG_STRU':
      case 'JRENGG_LICE':
      case 'JRENGG_SUPER1':
      case 'JRENGG_SUPER2':
        return 'bg-green-100 text-green-800'
      case 'CLERK':
        return 'bg-gray-100 text-gray-800'
      case 'OFFLINE_PAYMENT_OFFICER':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: string) => {
    const roleObj = OFFICER_ROLES.find(r => r.value === role)
    return roleObj?.label || role
  }

  const handleViewUserDetails = async (user: User) => {
    setSelectedUser(user)
    setShowUserDetailsModal(true)
    setLoadingApplications(true)
    
    try {
      // Fetch applications assigned to this user
      const response = await api.get(`/applications?assignedTo=${user.id}`)
      
      if (response.success && response.data) {
        setUserApplications(Array.isArray(response.data) ? response.data : [])
      }
    } catch (error: any) {
      console.error('Failed to load user applications:', error)
      toast({
        title: 'Error',
        description: 'Failed to load user applications',
        variant: 'destructive'
      })
      setUserApplications([])
    } finally {
      setLoadingApplications(false)
    }
  }

  const formatApplicationStatus = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800'
      case 'under_review':
      case 'document_verification_pending':
      case 'junior_engineer_pending':
      case 'assistant_engineer_pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
      case 'license_issued':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Officer Management</h1>
            <p className="text-muted-foreground">
              Create and manage officer accounts in the system
            </p>
          </div>
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Create Officer
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Officer</DialogTitle>
                <DialogDescription>
                  Send an invitation to an officer to join the system. They will receive an email invitation and can set their own password.
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleCreateUser} className="space-y-4">
                <div>
                  <Label htmlFor="email">Officer Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={createForm.email}
                    onChange={(e) => setCreateForm({ ...createForm, email: e.target.value })}
                    placeholder="Enter officer's email address"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="role">Officer Role *</Label>
                  <Select 
                    value={createForm.role} 
                    onValueChange={(value: string) => setCreateForm({ ...createForm, role: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select officer role" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      {OFFICER_ROLES.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                    disabled={creating}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={creating}>
                    {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Create Officer
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Officers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
              <p className="text-xs text-muted-foreground">
                Active and inactive officers
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Officers</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.filter(u => u.isActive).length}</div>
              <p className="text-xs text-muted-foreground">
                Currently active officers
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Officers</CardTitle>
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.filter(u => !u.isActive).length}</div>
              <p className="text-xs text-muted-foreground">
                Currently inactive officers
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unique Roles</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(users.map(u => u.role)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Different officer types
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pending Invitations */}
        {pendingInvitations.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Pending Officer Invitations</CardTitle>
              <CardDescription>
                Officers who have been invited but haven't completed their setup
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {pendingInvitations.map((invitation: any) => (
                  <div key={invitation.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div>
                          <p className="font-medium">{invitation.email}</p>
                          <p className="text-sm text-muted-foreground">
                            Role: {OFFICER_ROLES.find(r => r.value === invitation.role)?.label || invitation.role}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Invited: {new Date(invitation.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge variant="secondary">{invitation.status}</Badge>
                      </div>
                    </div>
                    <Button
                      onClick={() => handleReinvite(invitation.id)}
                      disabled={reinviting === invitation.id}
                      variant="outline"
                      size="sm"
                    >
                      {reinviting === invitation.id ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        'Reinvite'
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Officers List</CardTitle>
            <CardDescription>
              Manage and view all officers in the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search officers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  <SelectItem value="all">All Roles</SelectItem>
                  {OFFICER_ROLES.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Officers Table */}
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading officers...</span>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-12 text-muted-foreground">
                          {users.length === 0 ? 'No officers found' : 'No officers match your filters'}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.firstName} {user.lastName}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge className={getRoleBadgeColor(user.role)}>
                              {getRoleLabel(user.role)}
                            </Badge>
                          </TableCell>
                          <TableCell>{user.phoneNumber || 'N/A'}</TableCell>
                          <TableCell>
                            <Badge variant={user.isActive ? 'default' : 'secondary'}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(user.createdDate).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewUserDetails(user)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View Details
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => toggleUserStatus(user.id, user.isActive)}
                              >
                                {user.isActive ? (
                                  <>
                                    <EyeOff className="h-4 w-4 mr-1" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Eye className="h-4 w-4 mr-1" />
                                    Activate
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* User Details Modal */}
      <Dialog open={showUserDetailsModal} onOpenChange={setShowUserDetailsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName} - Application Details` : 'User Details'}
            </DialogTitle>
            <DialogDescription>
              {selectedUser && (
                <div className="text-sm text-gray-600 mt-2">
                  <p><strong>Email:</strong> {selectedUser.email}</p>
                  <p><strong>Role:</strong> {getRoleLabel(selectedUser.role)}</p>
                  <p><strong>Phone:</strong> {selectedUser.phoneNumber || 'N/A'}</p>
                  <p><strong>Status:</strong> {selectedUser.isActive ? 'Active' : 'Inactive'}</p>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-4">
            <h3 className="text-lg font-semibold mb-4">Assigned Applications ({userApplications.length})</h3>
            
            {loadingApplications ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading applications...</span>
              </div>
            ) : userApplications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No applications assigned to this user
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {userApplications.map((app: any) => (
                  <div key={app.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-semibold text-lg">Application #{app.id}</h4>
                        <p className="text-gray-600">{app.applicationType}</p>
                      </div>
                      <Badge className={getStatusBadgeColor(app.currentWorkflowState)}>
                        {formatApplicationStatus(app.currentWorkflowState)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p><strong>Applicant:</strong> {app.applicantName}</p>
                        <p><strong>Contact:</strong> {app.contactNumber}</p>
                        <p><strong>Plot No:</strong> {app.plotNumber}</p>
                      </div>
                      <div>
                        <p><strong>Submitted:</strong> {new Date(app.submissionDate).toLocaleDateString()}</p>
                        <p><strong>Updated:</strong> {new Date(app.updatedAt).toLocaleDateString()}</p>
                        {app.reviewDate && (
                          <p><strong>Review Date:</strong> {new Date(app.reviewDate).toLocaleDateString()}</p>
                        )}
                      </div>
                    </div>
                    
                    {app.place && (
                      <div className="mt-3 text-sm">
                        <p><strong>Review Location:</strong> {app.place}</p>
                        {app.roomNumber && <span> - Room {app.roomNumber}</span>}
                      </div>
                    )}
                    
                    {app.notes && (
                      <div className="mt-3 text-sm">
                        <p><strong>Notes:</strong> {app.notes}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  )
}

export default OfficerManagementPage
