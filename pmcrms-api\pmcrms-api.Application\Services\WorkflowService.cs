using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Application.Services;

public class WorkflowService : IWorkflowService
{
    private readonly IUnitOfWork _unitOfWork;

    public WorkflowService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<bool> CanTransitionToAsync(WorkflowStateType currentState, WorkflowStateType nextState, string roleName)
    {
        return WorkflowTransitionRules.CanTransition(currentState, nextState, roleName);
    }

    public async Task<IEnumerable<WorkflowStateType>> GetAvailableStatesAsync(WorkflowStateType currentState, string roleName)
    {
        return WorkflowTransitionRules.GetAvailableStates(currentState, roleName);
    }

    public async Task<bool> ValidateTransitionAsync(Domain.Entities.Application application, WorkflowStateType nextState, string roleName)
    {
        var currentState = (WorkflowStateType)application.WorkflowState.Id;
        return await CanTransitionToAsync(currentState, nextState, roleName);
    }

    public async Task<Domain.Entities.Application> TransitionToAsync(Domain.Entities.Application application, WorkflowStateType nextState, string roleName, string? remarks = null)
    {
        if (!await ValidateTransitionAsync(application, nextState, roleName))
        {
            throw new InvalidOperationException($"Invalid state transition from {application.WorkflowState.Name} to {nextState} for role {roleName}");
        }

        var workflowStateRepository = _unitOfWork.Repository<WorkflowState>();
        var states = await workflowStateRepository.FindAsync(w => w.Id == (int)nextState);
        var newState = states.FirstOrDefault() ?? 
            throw new InvalidOperationException($"Workflow state {nextState} not found");

        application.WorkflowState = newState;
        application.WorkflowStateId = newState.Id;

        // Add state transition history if needed
        // You can create a separate entity for tracking state changes

        return application;
    }

    public async Task InitializeWorkflowAsync()
    {
        var workflowStateRepository = _unitOfWork.Repository<WorkflowState>();
        
        // Check if states already exist
        if (await workflowStateRepository.GetAllAsync() is { } existingStates && existingStates.Any())
        {
            return;
        }

        // Create initial workflow states
        var states = Enum.GetValues<WorkflowStateType>()
            .Select(state => new WorkflowState
            {
                Id = (int)state,
                Name = state.ToString(),
                Description = $"Application is in {state} state",
                Order = (int)state,
                IsFinal = state is WorkflowStateType.FINAL_APPROVE or WorkflowStateType.REJECTED
            });

        foreach (var state in states)
        {
            await workflowStateRepository.AddAsync(state);
        }

        await _unitOfWork.SaveChangesAsync();
    }
}
