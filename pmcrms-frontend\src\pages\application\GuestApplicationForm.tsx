import React, { useState, useEffect, useRef } from 'react'
import { useForm, use<PERSON><PERSON>A<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useNavigate, Link } from 'react-router-dom'
import { But<PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { 
  ArrowLeft, 
  ArrowRight, 
  Plus, 
  Trash2, 
  Calculator, 
  Upload,
  FileText,
  User,
  MapPin,
  CreditCard,
  Shield,
  GraduationCap,
  Briefcase,
  CheckCircle
} from 'lucide-react'
import { useGuestAuth } from '../../hooks/useGuestAuth'
import Logo from '../../components/Logo'

// Position options
const POSITIONS = [
  'Architect',
  'Licence Engineer',
  'Structural Engineer',
  'Supervisor 1',
  'Supervisor 2'
]

// Blood Group options
const BLOOD_GROUPS = [
  'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
]

// Course specialization options
const COURSE_SPECIALIZATIONS = [
  'Civil Engineering',
  'Architecture',
  'Structural Engineering',
  'Electrical Engineering',
  'Mechanical Engineering',
  'Other'
]

// Validation schema
const applicationFormSchema = z.object({
  // Basic Information
  position: z.string().min(1, 'Position is required'),
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  motherName: z.string().min(1, 'Mother name is required'),
  mobileNumber: z.string().min(10, 'Mobile number must be at least 10 digits'),
  emailId: z.string().email('Invalid email address'),
  birthDate: z.string().min(1, 'Birth date is required'),
  bloodGroup: z.string().min(1, 'Blood group is required'),
  height: z.string().min(1, 'Height is required'),
  gender: z.enum(['male', 'female', 'other'], { required_error: 'Gender is required' }),
  electricityBill: z.any().optional(),
  structuralEngineerDoc: z.any().optional(),
  permanentSameAsLocal: z.boolean().default(false),
  
  // Local Address
  localAddress: z.object({
    flatHouseNumber: z.string().min(1, 'Flat/House number is required'),
    streetAddress: z.string().min(1, 'Street address is required'),
    addressLine: z.string().optional(),
    country: z.string().min(1, 'Country is required'),
    state: z.string().min(1, 'State is required'),
    city: z.string().min(1, 'City is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
  }),
  
  // Permanent Address
  permanentAddress: z.object({
    flatHouseNumber: z.string().min(1, 'Flat/House number is required'),
    streetAddress: z.string().min(1, 'Street address is required'),
    address: z.string().optional(),
    country: z.string().min(1, 'Country is required'),
    state: z.string().min(1, 'State is required'),
    city: z.string().min(1, 'City is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
  }),
  
  // PAN Information
  panNumber: z.string().min(10, 'PAN number must be 10 characters'),
  panAttachment: z.any().optional(),
  
  // Aadhar Information
  aadharNumber: z.string().min(12, 'Aadhar number must be 12 digits'),
  aadharAttachment: z.any().optional(),
  
  // Essential Details (Conditional for Architect)
  coaCertificationNumber: z.string().optional(),
  coaCertificate: z.any().optional(),
  coaValidity: z.string().optional(),
  
  // Qualification
  qualification: z.object({
    instituteName: z.string().min(1, 'Institute name is required'),
    universityName: z.string().min(1, 'University name is required'),
    courseSpecialization: z.string().min(1, 'Course specialization is required'),
    certificate: z.any().optional(),
    lastYearMarksheet: z.any().optional(),
    degreeProgram: z.string().min(1, 'Degree program is required'),
    passingMonth: z.string().min(1, 'Passing month is required'),
    passingYear: z.string().min(1, 'Passing year is required'),
  }),
  
  // Experience
  experiences: z.array(z.object({
    companyName: z.string().min(1, 'Company name is required'),
    position: z.string().min(1, 'Position is required'),
    yearsOfExperience: z.string().min(1, 'Years of experience is required'),
    certificate: z.any().optional(),
    fromDate: z.string().min(1, 'From date is required'),
    toDate: z.string().min(1, 'To date is required'),
  })),
  
  // Documents
  documents: z.array(z.object({
    documentName: z.string().min(1, 'Document name is required'),
    attachment: z.any().optional(),
  })),
  
  // Self Declaration
  selfDeclarationFile: z.any().optional(),
  
  // Profile Picture
  profilePicture: z.any().optional(),
})

type ApplicationFormData = z.infer<typeof applicationFormSchema>

const GuestApplicationForm: React.FC = () => {
  const navigate = useNavigate()
  const { guestSession, isSessionValid } = useGuestAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [totalExperience, setTotalExperience] = useState(0)
  const [loading, setLoading] = useState(false)
  const [sessionChecking, setSessionChecking] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [isSaving, setIsSaving] = useState(false) // Track if currently saving
  const lastSaveTimeRef = useRef<number>(0) // Track last save time for throttling
  
  // File state management
  const [files, setFiles] = useState({
    panAttachment: null as File | null,
    aadharAttachment: null as File | null,
    electricityBill: null as File | null,
    structuralEngineerDoc: null as File | null,
    selfDeclarationFile: null as File | null,
    profilePicture: null as File | null,
    qualificationCertificate: null as File | null,
    lastYearMarksheet: null as File | null
  })

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      experiences: [{ companyName: '', position: '', yearsOfExperience: '', fromDate: '', toDate: '' }],
      documents: [{ documentName: '', attachment: null }],
      permanentSameAsLocal: false,
    }
  })

  const { fields: experienceFields, append: appendExperience, remove: removeExperience } = useFieldArray({
    control,
    name: 'experiences'
  })

  const { fields: documentFields, append: appendDocument, remove: removeDocument } = useFieldArray({
    control,
    name: 'documents'
  })

  const watchPosition = watch('position')
  const watchPermanentSameAsLocal = watch('permanentSameAsLocal')
  const watchLocalAddress = watch('localAddress')
  const watchAllFields = watch() // Watch all form fields for autosave

  // Autosave functionality with throttling
  const saveFormData = () => {
    const now = Date.now()
    // Throttle saves to once per 3 seconds minimum
    if (now - lastSaveTimeRef.current < 3000) {
      return
    }
    
    setIsSaving(true) // Show saving indicator
    
    const formData = watchAllFields
    const saveKey = `guest_application_draft_${guestSession?.email || 'unknown'}`
    
    try {
      localStorage.setItem(saveKey, JSON.stringify({
        formData,
        currentStep,
        timestamp: new Date().toISOString()
      }))
      setLastSaved(new Date())
      lastSaveTimeRef.current = now
      console.log('Form data auto-saved at', new Date().toLocaleTimeString())
    } catch (error) {
      console.error('Error saving form data:', error)
    } finally {
      setIsSaving(false) // Hide saving indicator
    }
  }

  const loadFormData = () => {
    const saveKey = `guest_application_draft_${guestSession?.email || 'unknown'}`
    
    try {
      const savedData = localStorage.getItem(saveKey)
      if (savedData) {
        const parsed = JSON.parse(savedData)
        reset(parsed.formData)
        setCurrentStep(parsed.currentStep || 1)
        setLastSaved(new Date(parsed.timestamp))
        return true
      }
    } catch (error) {
      console.error('Error loading saved form data:', error)
    }
    return false
  }

  // File handling functions
  const validateFile = (file: File | null, maxSize = 5 * 1024 * 1024) => { // 5MB default
    if (!file) return true;
    
    if (file.size > maxSize) {
      alert('File size must be less than 5MB');
      return false;
    }
    
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      alert('Only PDF, JPG, JPEG, and PNG files are allowed');
      return false;
    }
    
    return true;
  };

  const handleFileChange = (fieldName: keyof typeof files, file: File | null) => {
    if (file && !validateFile(file)) {
      return;
    }
    
    setFiles(prev => ({
      ...prev,
      [fieldName]: file
    }));
  };

  const clearSavedData = () => {
    const saveKey = `guest_application_draft_${guestSession?.email || 'unknown'}`
    localStorage.removeItem(saveKey)
    setLastSaved(null)
  }

  // Load saved data on component mount
  useEffect(() => {
    if (guestSession?.email) {
      loadFormData()
    }
  }, [guestSession?.email])

  // Autosave when form data changes (debounced) - Re-enabled with longer delay
  useEffect(() => {
    if (!guestSession?.email) return

    const debounceTimer = setTimeout(() => {
      saveFormData()
    }, 8000) // Save 8 seconds after user stops typing (longer delay to prevent spam)

    return () => clearTimeout(debounceTimer)
  }, [watchAllFields, guestSession?.email])

  // Periodic autosave every 10 seconds
  useEffect(() => {
    if (!guestSession?.email) return

    const interval = setInterval(() => {
      saveFormData()
    }, 10000) // 10 seconds

    return () => clearInterval(interval)
  }, [guestSession?.email])

  useEffect(() => {
    console.log('GuestApplicationForm useEffect - guestSession:', guestSession)
    console.log('GuestApplicationForm useEffect - isSessionValid():', isSessionValid())
    
    // Check localStorage directly as backup
    const sessionData = localStorage.getItem('guest_session')
    console.log('Direct localStorage check:', sessionData)
    
    // Add a small delay to allow session to load
    const timer = setTimeout(() => {
      if (!isSessionValid() && !sessionData) {
        console.log('No valid session found after timeout, navigating to login')
        navigate('/login')
        return
      }
      
      console.log('Session found, form ready')
      setSessionChecking(false)
    }, 100) // 100ms delay
    
    return () => clearTimeout(timer)
  }, [navigate, isSessionValid])

  // Copy local address to permanent address when switch is enabled
  useEffect(() => {
    if (watchPermanentSameAsLocal && watchLocalAddress) {
      setValue('permanentAddress.flatHouseNumber', watchLocalAddress.flatHouseNumber || '')
      setValue('permanentAddress.streetAddress', watchLocalAddress.streetAddress || '')
      setValue('permanentAddress.address', watchLocalAddress.addressLine || '')
      setValue('permanentAddress.country', watchLocalAddress.country || '')
      setValue('permanentAddress.state', watchLocalAddress.state || '')
      setValue('permanentAddress.city', watchLocalAddress.city || '')
      setValue('permanentAddress.postalCode', watchLocalAddress.postalCode || '')
    }
  }, [watchPermanentSameAsLocal, watchLocalAddress, setValue])

  // Auto-calculate total experience when experience dates change
  useEffect(() => {
    calculateExperience()
  }, [watch('experiences')])

  const calculateExperience = () => {
    // Calculate total experience based on date ranges from all experience entries
    const total = experienceFields.reduce((sum, _, index) => {
      const fromDate = watch(`experiences.${index}.fromDate`)
      const toDate = watch(`experiences.${index}.toDate`)
      
      if (fromDate && toDate) {
        const from = new Date(fromDate)
        const to = new Date(toDate)
        
        // Calculate years difference
        const yearsDiff = to.getFullYear() - from.getFullYear()
        const monthsDiff = to.getMonth() - from.getMonth()
        const daysDiff = to.getDate() - from.getDate()
        
        // Calculate total experience in years (with decimal precision)
        let experienceYears = yearsDiff
        if (monthsDiff > 0 || (monthsDiff === 0 && daysDiff >= 0)) {
          experienceYears += monthsDiff / 12
        } else {
          experienceYears -= 1
          experienceYears += (12 + monthsDiff) / 12
        }
        
        // Round to 1 decimal place
        return sum + Math.round(experienceYears * 10) / 10
      }
      
      return sum
    }, 0)
    
    setTotalExperience(total)
  }

  const nextStep = async () => {
    console.log('Next button clicked, current step:', currentStep)
    
    // For now, let's skip validation to test navigation
    // TODO: Re-enable validation after testing
    const isValid = true
    
    /* 
    // Validate only fields for the current step
    let isValid = true
    try {
      switch (currentStep) {
        case 1: // Basic Information
          isValid = await trigger([
            'position', 'firstName', 'lastName', 'motherName', 'mobileNumber', 
            'emailId', 'birthDate', 'bloodGroup', 'height', 'gender'
          ])
          break
        case 2: // Address Details
          isValid = await trigger([
            'localAddress.flatHouseNumber', 'localAddress.streetAddress', 
            'localAddress.country', 'localAddress.state', 'localAddress.city', 
            'localAddress.postalCode', 'permanentAddress.flatHouseNumber', 
            'permanentAddress.streetAddress', 'permanentAddress.country', 
            'permanentAddress.state', 'permanentAddress.city', 'permanentAddress.postalCode'
          ])
          break
        case 3: // PAN Information
          isValid = await trigger(['panNumber'])
          break
        case 4: // Aadhar Information
          isValid = await trigger(['aadharNumber'])
          break
        case 5: // Essential Details (Architect only) or Qualification
          if (watchPosition === 'Architect') {
            // Essential details validation (optional fields)
            isValid = true
          } else {
            // Qualification validation
            isValid = await trigger([
              'qualification.instituteName', 'qualification.universityName',
              'qualification.courseSpecialization', 'qualification.degreeProgram',
              'qualification.passingMonth', 'qualification.passingYear'
            ])
          }
          break
        case 6: // Qualification (for Architect) or Experience
          if (watchPosition === 'Architect') {
            // Qualification validation
            isValid = await trigger([
              'qualification.instituteName', 'qualification.universityName',
              'qualification.courseSpecialization', 'qualification.degreeProgram',
              'qualification.passingMonth', 'qualification.passingYear'
            ])
          } else {
            // Experience validation
            isValid = await trigger(['experiences'])
          }
          break
        case 7: // Experience (for Architect) or Documents
          if (watchPosition === 'Architect') {
            // Experience validation
            isValid = await trigger(['experiences'])
          } else {
            // Documents validation
            isValid = await trigger(['documents'])
          }
          break
        case 8: // Documents (for Architect) or Final Review
          if (watchPosition === 'Architect') {
            // Documents validation
            isValid = await trigger(['documents'])
          } else {
            // Final review - no additional validation needed
            isValid = true
          }
          break
        case 9: // Final Review (Architect only)
          isValid = true
          break
        default:
          isValid = true
      }
    } catch (error) {
      console.error('Validation error:', error)
      isValid = false
    }
    */
    
    const maxSteps = watchPosition === 'Architect' ? 9 : 8
    
    console.log('Step validation:', { currentStep, isValid, maxSteps, watchPosition })
    
    if (isValid && currentStep < maxSteps) {
      const newStep = currentStep + 1
      console.log('Moving to step:', newStep)
      setCurrentStep(newStep)
      // Save progress when moving to next step
      saveFormData()
    } else if (!isValid) {
      console.log('Validation failed for step:', currentStep)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const directSubmit = async () => {
    console.log('Direct submit called - bypassing validation!')
    console.log('Guest session:', guestSession)
    console.log('Guest session localStorage:', localStorage.getItem('guest_session'))
    
    const formData = watch() // Get current form data
    await onSubmit(formData as ApplicationFormData)
  }

  const onSubmit = async (data: ApplicationFormData) => {
    console.log('onSubmit called!')
    console.log('Form validation errors:', errors)
    console.log('Files to upload:', files)
    
    try {
      setLoading(true)
      console.log('Submitting form data:', data)
      
      // Create FormData for file uploads
      const submitData = new FormData()
      
      // Add all text fields
      submitData.append('position', data.position)
      submitData.append('firstName', data.firstName)
      submitData.append('middleName', data.middleName || '')
      submitData.append('lastName', data.lastName)
      submitData.append('motherName', data.motherName)
      submitData.append('mobileNumber', data.mobileNumber)
      submitData.append('emailId', data.emailId)
      submitData.append('birthDate', data.birthDate)
      submitData.append('bloodGroup', data.bloodGroup)
      submitData.append('height', data.height)
      submitData.append('gender', data.gender)
      submitData.append('panNumber', data.panNumber)
      submitData.append('aadharNumber', data.aadharNumber)
      submitData.append('permanentSameAsLocal', data.permanentSameAsLocal.toString())
      submitData.append('guestEmail', guestSession?.email || '')
      submitData.append('submittedAt', new Date().toISOString())
      
      // Add address objects as JSON strings
      submitData.append('localAddress', JSON.stringify(data.localAddress))
      submitData.append('permanentAddress', JSON.stringify(data.permanentAddress))
      
      // Add qualification (excluding file fields)
      submitData.append('qualification', JSON.stringify({
        instituteName: data.qualification.instituteName,
        universityName: data.qualification.universityName,
        courseSpecialization: data.qualification.courseSpecialization,
        degreeProgram: data.qualification.degreeProgram,
        passingMonth: data.qualification.passingMonth,
        passingYear: data.qualification.passingYear
      }))
      
      // Add main files (only if they exist)
      if (files.panAttachment) submitData.append('panAttachment', files.panAttachment)
      if (files.aadharAttachment) submitData.append('aadharAttachment', files.aadharAttachment)
      if (files.electricityBill) submitData.append('electricityBill', files.electricityBill)
      if (files.structuralEngineerDoc) submitData.append('structuralEngineerDoc', files.structuralEngineerDoc)
      if (files.selfDeclarationFile) submitData.append('selfDeclarationFile', files.selfDeclarationFile)
      if (files.profilePicture) submitData.append('profilePicture', files.profilePicture)
      if (files.qualificationCertificate) submitData.append('qualification.certificate', files.qualificationCertificate)
      if (files.lastYearMarksheet) submitData.append('qualification.lastYearMarksheet', files.lastYearMarksheet)
      
      // Add experiences
      data.experiences.forEach((exp, index) => {
        submitData.append(`experiences[${index}].companyName`, exp.companyName)
        submitData.append(`experiences[${index}].position`, exp.position)
        submitData.append(`experiences[${index}].yearsOfExperience`, exp.yearsOfExperience)
        submitData.append(`experiences[${index}].fromDate`, exp.fromDate)
        submitData.append(`experiences[${index}].toDate`, exp.toDate)
        if (exp.certificate && exp.certificate instanceof File) {
          submitData.append(`experiences[${index}].certificate`, exp.certificate)
        }
      })
      
      // Add documents
      data.documents.forEach((doc, index) => {
        submitData.append(`documents[${index}].documentName`, doc.documentName || '')
        if (doc.attachment && doc.attachment instanceof File) {
          submitData.append(`documents[${index}].attachment`, doc.attachment)
        }
      })
      
      // Debug: Log FormData contents
      console.log('FormData contents:')
      for (let [key, value] of submitData.entries()) {
        console.log(key, value)
      }

      // Call the API to submit the application
      console.log('Making API call to submit application...')
      
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://localhost:7249/api'
      
      const response = await fetch(`${API_BASE_URL}/applications/guest/submit`, {
        method: 'POST',
        headers: {
          // Don't set Content-Type - let browser set it for FormData
          'X-Guest-Email': guestSession?.email || '',
          'X-Guest-Session': localStorage.getItem('guest_session') || ''
        },
        body: submitData // Send FormData instead of JSON
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const responseData = await response.json()
      console.log('API Response:', responseData)
      
      // Clear saved draft data after successful submission
      clearSavedData()
      
      // Show success message and redirect
      alert('Application submitted successfully!')
      navigate('/dashboard/guest')
      
    } catch (error: any) {
      console.error('Error submitting application:', error)
      
      // Show more specific error message for fetch errors
      if (error?.message) {
        if (error.message.includes('401')) {
          alert('Authentication failed. Please try logging in again as a guest.')
        } else if (error.message.includes('404')) {
          alert('API endpoint not found. Please contact support.')
        } else if (error.message.includes('500')) {
          alert('Server error. Please try again later.')
        } else {
          alert(`Error: ${error.message}`)
        }
      } else {
        alert('Error submitting application. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const steps = [
    { number: 1, title: 'Basic Information', icon: User },
    { number: 2, title: 'Address Details', icon: MapPin },
    { number: 3, title: 'PAN Information', icon: CreditCard },
    { number: 4, title: 'Aadhar Information', icon: Shield },
    ...(watchPosition === 'Architect' ? [{ number: 5, title: 'Essential Details', icon: FileText }] : []),
    { number: watchPosition === 'Architect' ? 6 : 5, title: 'Qualification', icon: GraduationCap },
    { number: watchPosition === 'Architect' ? 7 : 6, title: 'Experience', icon: Briefcase },
    { number: watchPosition === 'Architect' ? 8 : 7, title: 'Documents', icon: FileText },
    { number: watchPosition === 'Architect' ? 9 : 8, title: 'Final Review', icon: CheckCircle },
  ]

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-2 overflow-x-auto px-4">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = currentStep === step.number
            const isCompleted = currentStep > step.number
            
            return (
              <div key={step.number} className="flex items-center flex-shrink-0">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  isActive 
                    ? 'border-blue-600 bg-blue-600 text-white' 
                    : isCompleted 
                      ? 'border-green-600 bg-green-600 text-white' 
                      : 'border-gray-300 text-gray-400'
                }`}>
                  <Icon className="w-4 h-4" />
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-px mx-1 ${
                    isCompleted ? 'bg-green-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>
      <div className="mt-4 text-center">
        <h2 className="text-xl font-semibold text-gray-900">{steps[currentStep - 1].title}</h2>
        <p className="text-sm text-gray-600">Step {currentStep} of {steps.length}</p>
      </div>
    </div>
  )

  const renderBasicInformation = () => (
    <div className="space-y-6">
      {/* Position Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Position *</label>
        <Controller
          name="position"
          control={control}
          render={({ field }) => (
            <select
              {...field}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Position</option>
              {POSITIONS.map(pos => (
                <option key={pos} value={pos}>{pos}</option>
              ))}
            </select>
          )}
        />
        {errors.position && <p className="text-red-500 text-sm mt-1">{errors.position.message}</p>}
      </div>

      {/* Fee Labels */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Licence Engineer Fees</label>
          <p className="text-sm text-gray-600 mt-1">₹5,000</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Structural Engineer Fees</label>
          <p className="text-sm text-gray-600 mt-1">₹7,500</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Supervisor 1 Fees</label>
          <p className="text-sm text-gray-600 mt-1">₹3,000</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Supervisor 2 Fees</label>
          <p className="text-sm text-gray-600 mt-1">₹3,500</p>
        </div>
      </div>

      {/* Required Documents Labels */}
      <div className="space-y-2">
        <h3 className="font-medium text-gray-900">Required Documents:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Architect Required Documents</li>
          <li>• Licence Engineer Required Documents</li>
          <li>• Structural Engineer Required Documents</li>
          <li>• Supervisor 1 Documents</li>
          <li>• Supervisor 2 Documents</li>
        </ul>
      </div>

      {/* Personal Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter first name" />}
          />
          {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
          <Controller
            name="middleName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter middle name" />}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter last name" />}
          />
          {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Mother Name *</label>
          <Controller
            name="motherName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter mother's name" />}
          />
          {errors.motherName && <p className="text-red-500 text-sm mt-1">{errors.motherName.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Mobile Number *</label>
          <Controller
            name="mobileNumber"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter mobile number" />}
          />
          {errors.mobileNumber && <p className="text-red-500 text-sm mt-1">{errors.mobileNumber.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email ID *</label>
          <Controller
            name="emailId"
            control={control}
            render={({ field }) => <Input {...field} type="email" placeholder="Enter email address" />}
          />
          {errors.emailId && <p className="text-red-500 text-sm mt-1">{errors.emailId.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Birth Date *</label>
          <Controller
            name="birthDate"
            control={control}
            render={({ field }) => <Input {...field} type="date" />}
          />
          {errors.birthDate && <p className="text-red-500 text-sm mt-1">{errors.birthDate.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Blood Group *</label>
          <Controller
            name="bloodGroup"
            control={control}
            render={({ field }) => (
              <select {...field} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">Select Blood Group</option>
                {BLOOD_GROUPS.map(bg => (
                  <option key={bg} value={bg}>{bg}</option>
                ))}
              </select>
            )}
          />
          {errors.bloodGroup && <p className="text-red-500 text-sm mt-1">{errors.bloodGroup.message}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Height *</label>
          <Controller
            name="height"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter height (e.g., 5'6'')" />}
          />
          {errors.height && <p className="text-red-500 text-sm mt-1">{errors.height.message}</p>}
        </div>
      </div>

      {/* Gender */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Gender *</label>
        <Controller
          name="gender"
          control={control}
          render={({ field }) => (
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input type="radio" {...field} value="male" className="mr-2" />
                Male
              </label>
              <label className="flex items-center">
                <input type="radio" {...field} value="female" className="mr-2" />
                Female
              </label>
              <label className="flex items-center">
                <input type="radio" {...field} value="other" className="mr-2" />
                Other
              </label>
            </div>
          )}
        />
        {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender.message}</p>}
      </div>

      {/* File Uploads */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Electricity Bill</label>
          <Controller
            name="electricityBill"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <input
                {...field}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={(e) => onChange(e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            )}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Structural Engineer Doc</label>
          <Controller
            name="structuralEngineerDoc"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <input
                {...field}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={(e) => onChange(e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            )}
          />
        </div>
      </div>
    </div>
  )

  // I'll continue with the other sections in the next part due to length...
  
  const renderAddressDetails = () => (
    <div className="space-y-6">
      {/* Permanent Same as Local Switch */}
      <div className="flex items-center space-x-2">
        <Controller
          name="permanentSameAsLocal"
          control={control}
          render={({ field }) => (
            <input
              type="checkbox"
              checked={field.value}
              onChange={field.onChange}
              className="w-4 h-4 text-blue-600"
            />
          )}
        />
        <label className="text-sm font-medium text-gray-700">
          Permanent Address Same as Local Address
        </label>
      </div>

      {/* Local Address */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Local Address</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Flat/House Number *</label>
            <Controller
              name="localAddress.flatHouseNumber"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter flat/house number" />}
            />
            {errors.localAddress?.flatHouseNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.flatHouseNumber.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
            <Controller
              name="localAddress.streetAddress"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter street address" />}
            />
            {errors.localAddress?.streetAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.streetAddress.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Address Line</label>
            <Controller
              name="localAddress.addressLine"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter additional address details" />}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Country *</label>
            <Controller
              name="localAddress.country"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter country" />}
            />
            {errors.localAddress?.country && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.country.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
            <Controller
              name="localAddress.state"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter state" />}
            />
            {errors.localAddress?.state && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.state.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
            <Controller
              name="localAddress.city"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter city" />}
            />
            {errors.localAddress?.city && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.city.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
            <Controller
              name="localAddress.postalCode"
              control={control}
              render={({ field }) => <Input {...field} placeholder="Enter postal code" />}
            />
            {errors.localAddress?.postalCode && (
              <p className="text-red-500 text-sm mt-1">{errors.localAddress.postalCode.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Permanent Address */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Permanent Address</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Flat/House Number *</label>
            <Controller
              name="permanentAddress.flatHouseNumber"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter flat/house number"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.flatHouseNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.flatHouseNumber.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
            <Controller
              name="permanentAddress.streetAddress"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter street address"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.streetAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.streetAddress.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
            <Controller
              name="permanentAddress.address"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter additional address details"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Country *</label>
            <Controller
              name="permanentAddress.country"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter country"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.country && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.country.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
            <Controller
              name="permanentAddress.state"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter state"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.state && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.state.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
            <Controller
              name="permanentAddress.city"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter city"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.city && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.city.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
            <Controller
              name="permanentAddress.postalCode"
              control={control}
              render={({ field }) => (
                <Input 
                  {...field} 
                  placeholder="Enter postal code"
                  disabled={watchPermanentSameAsLocal}
                />
              )}
            />
            {errors.permanentAddress?.postalCode && (
              <p className="text-red-500 text-sm mt-1">{errors.permanentAddress.postalCode.message}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  const renderPANInformation = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">PAN Number *</label>
        <Controller
          name="panNumber"
          control={control}
          render={({ field }) => <Input {...field} placeholder="Enter PAN number" />}
        />
        {errors.panNumber && <p className="text-red-500 text-sm mt-1">{errors.panNumber.message}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">PAN Card Attachment</label>
        <Controller
          name="panAttachment"
          control={control}
          render={({ field: { onChange, value, ...field } }) => (
            <input
              {...field}
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => {
                const file = e.target.files?.[0] || null
                onChange(file) // Update react-hook-form
                handleFileChange('panAttachment', file) // Update our files state
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          )}
        />
      </div>
    </div>
  )

  const renderAadharInformation = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Aadhar Number *</label>
        <Controller
          name="aadharNumber"
          control={control}
          render={({ field }) => <Input {...field} placeholder="Enter Aadhar number" />}
        />
        {errors.aadharNumber && <p className="text-red-500 text-sm mt-1">{errors.aadharNumber.message}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Aadhar Card Attachment</label>
        <Controller
          name="aadharAttachment"
          control={control}
          render={({ field: { onChange, value, ...field } }) => (
            <input
              {...field}
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => {
                const file = e.target.files?.[0] || null
                onChange(file) // Update react-hook-form
                handleFileChange('aadharAttachment', file) // Update our files state
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          )}
        />
      </div>
    </div>
  )

  const renderEssentialDetails = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <p className="text-sm text-blue-800">
          This section is only required for Architect position
        </p>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">COA Certification Number</label>
        <Controller
          name="coaCertificationNumber"
          control={control}
          render={({ field }) => <Input {...field} placeholder="Enter COA certification number" />}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Upload Council of Architecture Certificate</label>
        <Controller
          name="coaCertificate"
          control={control}
          render={({ field: { onChange, value, ...field } }) => (
            <input
              {...field}
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => onChange(e.target.files?.[0])}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          )}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Validity Date</label>
        <Controller
          name="coaValidity"
          control={control}
          render={({ field }) => <Input {...field} type="date" />}
        />
      </div>
    </div>
  )

  const renderQualification = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Institute Name *</label>
          <Controller
            name="qualification.instituteName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter institute name" />}
          />
          {errors.qualification?.instituteName && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.instituteName.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">University Name *</label>
          <Controller
            name="qualification.universityName"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter university name" />}
          />
          {errors.qualification?.universityName && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.universityName.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Course Specialization *</label>
          <Controller
            name="qualification.courseSpecialization"
            control={control}
            render={({ field }) => (
              <select {...field} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">Select Specialization</option>
                {COURSE_SPECIALIZATIONS.map(spec => (
                  <option key={spec} value={spec}>{spec}</option>
                ))}
              </select>
            )}
          />
          {errors.qualification?.courseSpecialization && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.courseSpecialization.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Degree Program *</label>
          <Controller
            name="qualification.degreeProgram"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter degree program" />}
          />
          {errors.qualification?.degreeProgram && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.degreeProgram.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Passing Month *</label>
          <Controller
            name="qualification.passingMonth"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter passing month" />}
          />
          {errors.qualification?.passingMonth && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.passingMonth.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Passing Year *</label>
          <Controller
            name="qualification.passingYear"
            control={control}
            render={({ field }) => <Input {...field} placeholder="Enter passing year" />}
          />
          {errors.qualification?.passingYear && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.passingYear.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Certificate</label>
          <Controller
            name="qualification.certificate"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <input
                {...field}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={(e) => onChange(e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            )}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Last Year Marksheet</label>
          <Controller
            name="qualification.lastYearMarksheet"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <input
                {...field}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={(e) => onChange(e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            )}
          />
        </div>
      </div>
    </div>
  )

  const renderExperience = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Work Experience</h3>
        <Button
          type="button"
          variant="outline"
          onClick={() => appendExperience({ 
            companyName: '', 
            position: '', 
            yearsOfExperience: '', 
            fromDate: '', 
            toDate: '' 
          })}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Experience</span>
        </Button>
      </div>

      {experienceFields.map((field, index) => (
        <Card key={field.id}>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-gray-900">Experience {index + 1}</h4>
              {experienceFields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeExperience(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                <Controller
                  name={`experiences.${index}.companyName`}
                  control={control}
                  render={({ field }) => <Input {...field} placeholder="Enter company name" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Position *</label>
                <Controller
                  name={`experiences.${index}.position`}
                  control={control}
                  render={({ field }) => <Input {...field} placeholder="Enter position" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Years of Experience (Auto-calculated)</label>
                <Controller
                  name={`experiences.${index}.yearsOfExperience`}
                  control={control}
                  render={({ field }) => {
                    const fromDate = watch(`experiences.${index}.fromDate`)
                    const toDate = watch(`experiences.${index}.toDate`)
                    let calculatedYears = 0
                    
                    if (fromDate && toDate) {
                      const from = new Date(fromDate)
                      const to = new Date(toDate)
                      const yearsDiff = to.getFullYear() - from.getFullYear()
                      const monthsDiff = to.getMonth() - from.getMonth()
                      
                      calculatedYears = yearsDiff
                      if (monthsDiff > 0 || (monthsDiff === 0 && to.getDate() >= from.getDate())) {
                        calculatedYears += monthsDiff / 12
                      } else {
                        calculatedYears -= 1
                        calculatedYears += (12 + monthsDiff) / 12
                      }
                      calculatedYears = Math.round(calculatedYears * 10) / 10
                      
                      // Update the field value with calculated years
                      if (field.value !== calculatedYears.toString()) {
                        field.onChange(calculatedYears.toString())
                      }
                    }
                    
                    return (
                      <Input 
                        {...field} 
                        value={calculatedYears.toString()}
                        placeholder="Select dates to calculate" 
                        readOnly 
                        className="bg-gray-50"
                      />
                    )
                  }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">From Date *</label>
                <Controller
                  name={`experiences.${index}.fromDate`}
                  control={control}
                  render={({ field }) => <Input {...field} type="date" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">To Date *</label>
                <Controller
                  name={`experiences.${index}.toDate`}
                  control={control}
                  render={({ field }) => <Input {...field} type="date" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Certificate</label>
                <Controller
                  name={`experiences.${index}.certificate`}
                  control={control}
                  render={({ field: { onChange, value, ...field } }) => (
                    <input
                      {...field}
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={(e) => onChange(e.target.files?.[0])}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="flex items-center space-x-4">
        <Button
          type="button"
          onClick={calculateExperience}
          className="flex items-center space-x-2"
        >
          <Calculator className="w-4 h-4" />
          <span>Calculate Experience</span>
        </Button>
        <div className="text-sm text-gray-600">
          Total Experience: <span className="font-medium">{totalExperience} years</span>
        </div>
      </div>
    </div>
  )

  const renderDocuments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Additional Documents</h3>
        <Button
          type="button"
          variant="outline"
          onClick={() => appendDocument({ documentName: '', attachment: null })}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Document</span>
        </Button>
      </div>

      {documentFields.map((field, index) => (
        <Card key={field.id}>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium text-gray-900">Document {index + 1}</h4>
              {documentFields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeDocument(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Document Name *</label>
                <Controller
                  name={`documents.${index}.documentName`}
                  control={control}
                  render={({ field }) => <Input {...field} placeholder="Enter document name" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Attachment</label>
                <Controller
                  name={`documents.${index}.attachment`}
                  control={control}
                  render={({ field: { onChange, value, ...field } }) => (
                    <input
                      {...field}
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={(e) => onChange(e.target.files?.[0])}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Self Declaration */}
      <div className="mt-8 p-6 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-4">Self Declaration</h4>
        <div className="space-y-4">
          <div className="text-sm text-gray-700">
            <p className="mb-2">I hereby declare that:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>All the information provided by me is true and correct to the best of my knowledge.</li>
              <li>I understand that any false information may lead to rejection of my application.</li>
              <li>I agree to abide by all the rules and regulations of the organization.</li>
            </ul>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Self Declaration File</label>
            <Controller
              name="selfDeclarationFile"
              control={control}
              render={({ field: { onChange, value, ...field } }) => (
                <input
                  {...field}
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => onChange(e.target.files?.[0])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              )}
            />
          </div>
        </div>
      </div>

      {/* Profile Picture */}
      <div className="mt-6">
        <h4 className="font-medium text-gray-900 mb-4">Profile Picture</h4>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Upload Profile Picture</label>
          <Controller
            name="profilePicture"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <input
                {...field}
                type="file"
                accept=".jpg,.jpeg,.png"
                onChange={(e) => onChange(e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            )}
          />
          <p className="text-xs text-gray-500 mt-1">
            Please upload a recent passport-size photograph (JPG, PNG format)
          </p>
        </div>
      </div>
    </div>
  )

  const renderFinalReview = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Review Your Application</h3>
        <p className="text-gray-600">
          Please review all the information you have provided before submitting your application.
        </p>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 mb-2">Important Notes:</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Make sure all required fields are filled correctly</li>
          <li>• Ensure all uploaded documents are clear and readable</li>
          <li>• Double-check your contact information</li>
          <li>• Once submitted, you cannot edit your application</li>
        </ul>
      </div>
    </div>
  )
  
  // Check if session is valid or exists in localStorage
  const sessionData = localStorage.getItem('guest_session')
  if (sessionChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }
  
  if (!isSessionValid() && !sessionData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Please login to access the application form</p>
          <Link to="/login">
            <Button>Go to Login</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Logo size="sm" showText />
              <div className="hidden sm:block h-6 w-px bg-gray-300" />
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold text-gray-900">New Application</h1>
                <p className="text-sm text-gray-500">{guestSession?.email}</p>
              </div>
            </div>
            
            <Link to="/dashboard/guest">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="w-6 h-6" />
                <span>Professional Registration Application</span>
              </div>
              {(lastSaved || isSaving) && (
                <div className="text-xs flex items-center space-x-1">
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                      <span className="text-blue-600">Saving...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-3 h-3 text-green-600" />
                      <span className="text-green-600">Auto-saved {lastSaved?.toLocaleTimeString()}</span>
                    </>
                  )}
                </div>
              )}
            </CardTitle>
            <CardDescription className="flex items-center justify-between">
              <span>Please fill out all required information accurately</span>
              {lastSaved && (
                <span className="text-xs text-gray-500">
                  Draft saved automatically every 10 seconds
                </span>
              )}
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {/* Draft notification */}
            {lastSaved && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        Draft restored from {lastSaved.toLocaleDateString()} at {lastSaved.toLocaleTimeString()}
                      </p>
                      <p className="text-xs text-blue-600">
                        Your progress is automatically saved as you fill out the form
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={clearSavedData}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Clear Draft
                  </Button>
                </div>
              </div>
            )}
            
            <form 
              onSubmit={(e) => {
                console.log('Form submit event triggered!')
                handleSubmit(onSubmit)(e)
              }}
            >
              {renderStepIndicator()}
              
              {currentStep === 1 && renderBasicInformation()}
              {currentStep === 2 && renderAddressDetails()}
              {currentStep === 3 && renderPANInformation()}
              {currentStep === 4 && renderAadharInformation()}
              {currentStep === 5 && (watchPosition === 'Architect' ? renderEssentialDetails() : renderQualification())}
              {currentStep === 6 && (watchPosition === 'Architect' ? renderQualification() : renderExperience())}
              {currentStep === 7 && (watchPosition === 'Architect' ? renderExperience() : renderDocuments())}
              {currentStep === 8 && (watchPosition === 'Architect' ? renderDocuments() : renderFinalReview())}
              
              {/* Show Essential Details step only for Architect */}
              {watchPosition === 'Architect' && currentStep === 9 && renderFinalReview()}
              
              {/* Navigation Buttons */}
              <div className="flex justify-between items-center mt-8 pt-6 border-t">
                <div className="flex items-center space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={saveFormData}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Save Draft
                  </Button>
                </div>
                
                {currentStep < (watchPosition === 'Architect' ? 9 : 8) ? (
                  <Button type="button" onClick={nextStep}>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button 
                      type="submit" 
                      disabled={loading}
                      onClick={() => {
                        console.log('Submit button clicked!')
                        console.log('Form errors:', errors)
                        console.log('Form data:', watch())
                      }}
                    >
                      {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                    
                    {/* Debug: Direct submit button */}
                    <Button 
                      type="button" 
                      disabled={loading}
                      onClick={directSubmit}
                      variant="outline"
                    >
                      Direct Submit (Debug)
                    </Button>
                  </div>
                )}
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default GuestApplicationForm
