using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;
using pmcrms_api.Application.DTOs.Application;
using pmcrms_api.Application.DTOs.Applications;
using pmcrms_api.Infrastructure.Data;
using System.Net;
using System.Security.Claims;
using ApplicationEntity = pmcrms_api.Domain.Entities.Application;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Application management and processing
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Tags("Applications")]
[Produces("application/json")]
public class ApplicationsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IApplicationRoutingService _routingService;
    private readonly ApplicationDbContext _context;

    public ApplicationsController(IUnitOfWork unitOfWork, IApplicationRoutingService routingService, ApplicationDbContext context)
    {
        _unitOfWork = unitOfWork;
        _routingService = routingService;
        _context = context;
    }

    /// <summary>
    /// Get all applications with advanced filtering and pagination
    /// </summary>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="limit">Items per page (default: 20)</param>
    /// <param name="status">Filter by status</param>
    /// <param name="position">Filter by position</param>
    /// <param name="dateFrom">Filter from date</param>
    /// <param name="dateTo">Filter to date</param>
    /// <param name="search">Search term</param>
    /// <param name="assignedTo">Filter by assigned user ID</param>
    /// <returns>Paginated list of applications</returns>
    /// <response code="200">Applications retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(ApplicationsResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetApplications(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20,
        [FromQuery] string? status = null,
        [FromQuery] string? position = null,
        [FromQuery] DateTime? dateFrom = null,
        [FromQuery] DateTime? dateTo = null,
        [FromQuery] string? search = null,
        [FromQuery] int? assignedTo = null)
    {
        try
        {
            var applications = await _unitOfWork.Repository<ApplicationEntity>().GetAllAsync();

            // Get current user information for role-based filtering
            var currentUserId = GetCurrentUserId();
            var currentUserRole = GetCurrentUserRole();

            // Apply role-based filtering
            var filteredApplications = FilterApplicationsByRole(applications.AsQueryable(), currentUserRole, currentUserId);

            // Apply additional filters
            if (!string.IsNullOrEmpty(status))
            {
                if (Enum.TryParse<WorkflowStateType>(status, true, out var statusEnum))
                {
                    filteredApplications = filteredApplications.Where(a => a.CurrentState == statusEnum);
                }
            }

            if (!string.IsNullOrEmpty(position))
            {
                if (Enum.TryParse<PositionType>(position, true, out var positionEnum))
                {
                    filteredApplications = filteredApplications.Where(a => a.Position == positionEnum);
                }
            }

            if (dateFrom.HasValue)
            {
                filteredApplications = filteredApplications.Where(a => a.SubmissionDate >= dateFrom.Value);
            }

            if (dateTo.HasValue)
            {
                filteredApplications = filteredApplications.Where(a => a.SubmissionDate <= dateTo.Value);
            }

            if (!string.IsNullOrEmpty(search))
            {
                filteredApplications = filteredApplications.Where(a => 
                    a.FirstName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    a.LastName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    a.ApplicationNumber.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    a.EmailAddress.Contains(search, StringComparison.OrdinalIgnoreCase));
            }

            if (assignedTo.HasValue)
            {
                filteredApplications = filteredApplications.Where(a => a.AssignedToUserId == assignedTo.Value);
            }

            // Get total count before pagination
            var totalCount = filteredApplications.Count();

            // Apply pagination
            var paginatedApplications = filteredApplications
                .OrderByDescending(a => a.SubmissionDate)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToList();

            // Map to response DTOs
            var applicationDtos = paginatedApplications.Select(app => new ApplicationListDto
            {
                Id = app.Id.ToString(),
                ApplicationNumber = app.ApplicationNumber ?? "N/A",
                ApplicantName = $"{app.FirstName} {app.LastName}",
                Position = app.Position.ToString(),
                Status = app.CurrentState.ToString(),
                SubmittedDate = app.SubmissionDate,
                AssignedTo = app.AssignedTo != null ? $"{app.AssignedTo.FirstName} {app.AssignedTo.LastName}" : "Unassigned",
                LastUpdated = app.ApprovalDate ?? app.SubmissionDate,
                PhoneNumber = app.MobileNumber ?? "N/A",
                Email = app.EmailAddress ?? "N/A",
                Experience = "N/A", // TODO: Calculate from experiences
                CurrentStage = GetCurrentStageDescription(app.CurrentState),
                NextAction = GetNextAction(app.CurrentState),
                DocumentsCount = app.Documents?.Count ?? 0,
                VerifiedDocuments = 0, // TODO: Count verified documents
                ProcessingDays = (DateTime.Now - app.SubmissionDate).Days
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / limit);

            var response = new ApplicationsResponse
            {
                Success = true,
                Data = applicationDtos,
                TotalCount = totalCount,
                PageNumber = page,
                PageSize = limit,
                TotalPages = totalPages,
                HasNextPage = page < totalPages,
                HasPreviousPage = page > 1
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve applications", error = ex.Message });
        }
    }

    private static string GetCurrentStageDescription(WorkflowStateType state)
    {
        return state switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => "Document Verification",
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => "Junior Engineer Review",
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING => "Assistant Engineer Review",
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => "Executive Engineer Review",
            WorkflowStateType.CITY_ENGINEER_PENDING => "City Engineer Review",
            WorkflowStateType.PAYMENT_PENDING => "Payment Processing",
            WorkflowStateType.FINAL_APPROVE => "Completed",
            WorkflowStateType.REJECTED => "Rejected",
            _ => "Unknown"
        };
    }

    private static string GetNextAction(WorkflowStateType state)
    {
        return state switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => "Verify documents",
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => "Junior Engineer review",
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING => "Assistant Engineer approval",
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => "Executive Engineer approval",
            WorkflowStateType.CITY_ENGINEER_PENDING => "City Engineer final approval",
            WorkflowStateType.PAYMENT_PENDING => "Process payment",
            WorkflowStateType.FINAL_APPROVE => "Completed",
            WorkflowStateType.REJECTED => "Rejected",
            _ => "Unknown"
        };
    }

    /// <summary>
    /// Get application by ID
    /// </summary>
    /// <param name="id">Application ID</param>
    /// <returns>Application details</returns>
    /// <response code="200">Application found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Application not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetApplication(int id)
    {
        var application = await _unitOfWork.Repository<ApplicationEntity>().GetByIdAsync(id);
        
        if (application == null)
        {
            return NotFound(new { message = "Application not found" });
        }

        // Get addresses for this application
        var addresses = await _unitOfWork.Repository<Address>()
            .FindAsync(a => a.ApplicationId == id);
        
        var addressList = addresses.ToList();
        var permanentAddress = addressList.FirstOrDefault(a => !a.IsLocal);
        var localAddress = addressList.FirstOrDefault(a => a.IsLocal);

        // Get experiences for this application
        var experiences = await _unitOfWork.Repository<Experience>()
            .FindAsync(e => e.ApplicationId == id);

        var experienceList = experiences.ToList();

        // Get qualifications for this application
        var qualifications = await _unitOfWork.Repository<Qualification>()
            .FindAsync(q => q.ApplicationId == id);

        var qualificationList = qualifications.ToList();

        return Ok(new
        {
            success = true,
            message = "Application details retrieved successfully",
            data = new
            {
                // Basic Information
                application.Id,
                application.ApplicationNumber,
                application.FirstName,
                application.MiddleName,
                application.LastName,
                application.MotherName,
                application.EmailAddress,
                application.MobileNumber,
                
                // Personal Details
                Position = application.Position.ToString(),
                Gender = application.Gender.ToString(),
                BloodGroup = application.BloodGroup.ToString(),
                application.Height,
                BirthDate = application.BirthDate.ToString("yyyy-MM-dd"),
                
                // Identification
                application.PanNumber,
                application.AadharNumber,
                
                // COA Details (for architects)
                application.CoaCertificationNumber,
                CoaValidity = application.CoaValidity?.ToString("yyyy-MM-dd"),
                
                // Application Details
                application.Amount,
                Status = application.CurrentState.ToString(),
                application.SubmissionDate,
                application.ApprovalDate,
                application.Place,
                application.RoomNumber,
                
                // Contact and Review Information
                application.ContactPerson,
                application.ReviewDate,
                
                // Transaction Details
                application.TransactionMode,
                application.TransactionDate,
                application.ChallanNumber,
                
                // Certificate Details
                application.CertificateNumber,
                application.FromDate,
                application.ToYear,
                application.IsPayment,
                
                // File Paths
                application.CertificateFilePath,
                application.SelfDeclarationFormFilePath,
                application.ChallanFilePath,
                application.PanAttachmentFilePath,
                application.AadharAttachmentFilePath,
                application.CoaCertificateFilePath,
                application.ElectricityBillFilePath,
                application.StructuralEngineerDocFilePath,
                application.ProfilePictureFilePath,
                
                // Address Information
                application.PermanentSameAsLocal,
                Addresses = new
                {
                    Permanent = permanentAddress != null ? new
                    {
                        permanentAddress.FlatHouseNumber,
                        StreetAddress = permanentAddress.StreetAddress,
                        permanentAddress.AddressLine,
                        permanentAddress.City,
                        permanentAddress.State,
                        permanentAddress.Country,
                        permanentAddress.PostalCode
                    } : null,
                    Local = localAddress != null ? new
                    {
                        localAddress.FlatHouseNumber,
                        StreetAddress = localAddress.StreetAddress,
                        localAddress.AddressLine,
                        localAddress.City,
                        localAddress.State,
                        localAddress.Country,
                        localAddress.PostalCode
                    } : null
                },
                
                // Professional Experience
                Experiences = experienceList.Select(exp => new
                {
                    exp.Id,
                    exp.CompanyName,
                    exp.Position,
                    exp.YearsOfExperience,
                    exp.CertificateFilePath,
                    exp.FromDate,
                    exp.ToDate,
                    exp.TotalExperience,
                    exp.MonthDifference,
                    exp.YearDifference,
                    exp.ExperienceRequired
                }).ToList(),
                
                // Educational Qualifications
                Qualifications = qualificationList.Select(qual => new
                {
                    qual.Id,
                    qual.InstituteName,
                    qual.UniversityName,
                    qual.CourseSpecialization,
                    qual.DegreeProgram,
                    qual.PassingMonth,
                    qual.PassingYear,
                    qual.CertificateFilePath,
                    qual.LastYearMarksheetFilePath
                }).ToList(),
                
                // Assignment Information
                application.AssignedToUserId,
                AssignedTo = application.AssignedTo != null ? new
                {
                    application.AssignedTo.Id,
                    application.AssignedTo.FirstName,
                    application.AssignedTo.LastName,
                    application.AssignedTo.Email
                } : null,
                application.AssignedAt
            }
        });
    }

    /// <summary>
    /// Create a new application
    /// </summary>
    /// <param name="request">Application creation request</param>
    /// <returns>Created application details</returns>
    /// <response code="201">Application created successfully</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.Created)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> CreateApplication([FromBody] CreateApplicationRequest request)
    {
        // Parse enums from request
        if (!Enum.TryParse<BloodGroup>(request.BloodGroup, true, out var bloodGroup))
            bloodGroup = BloodGroup.OPositive; // Default value
        
        if (!Enum.TryParse<Gender>(request.Gender, true, out var gender))
            gender = Gender.Male; // Default value
        
        if (!Enum.TryParse<PositionType>(request.Position, true, out var position))
            position = PositionType.Architect; // Default value

        var application = new ApplicationEntity
        {
            ApplicationNumber = Guid.NewGuid().ToString(),
            FirstName = request.FirstName,
            LastName = request.LastName,
            EmailAddress = request.EmailAddress,
            MobileNumber = request.MobileNumber,
            Position = position,
            Amount = 0, // Default amount - will be calculated based on position
            CurrentState = WorkflowStateType.DOCUMENT_VERIFICATION_PENDING,
            SubmissionDate = DateTime.UtcNow,
            UserId = 1, // TODO: Get from authenticated user
            WorkflowStateId = 1, // TODO: Get default workflow state
            MotherName = request.MotherName,
            Place = request.Place,
            RoomNumber = request.RoomNumber,
            BloodGroup = bloodGroup,
            Height = request.Height,
            Gender = gender,
            PermanentSameAsLocal = request.PermanentSameAsLocal
        };

        await _unitOfWork.Repository<ApplicationEntity>().AddAsync(application);
        await _unitOfWork.SaveChangesAsync();

        // Create addresses
        var addresses = new List<Address>();

        // Create permanent address
        var permanentAddress = new Address
        {
            ApplicationId = application.Id,
            FlatHouseNumber = request.PermanentAddress.FlatHouseNumber,
            StreetAddress = request.PermanentAddress.StreetAddress,
            AddressLine = request.PermanentAddress.AddressLine,
            City = request.PermanentAddress.City,
            State = request.PermanentAddress.State,
            Country = request.PermanentAddress.Country,
            PostalCode = request.PermanentAddress.PostalCode,
            IsLocal = false
        };
        addresses.Add(permanentAddress);

        // Create local address
        if (request.PermanentSameAsLocal)
        {
            // Copy permanent address as local address
            var localAddress = new Address
            {
                ApplicationId = application.Id,
                FlatHouseNumber = request.PermanentAddress.FlatHouseNumber,
                StreetAddress = request.PermanentAddress.StreetAddress,
                AddressLine = request.PermanentAddress.AddressLine,
                City = request.PermanentAddress.City,
                State = request.PermanentAddress.State,
                Country = request.PermanentAddress.Country,
                PostalCode = request.PermanentAddress.PostalCode,
                IsLocal = true
            };
            addresses.Add(localAddress);
        }
        else if (request.LocalAddress != null)
        {
            var localAddress = new Address
            {
                ApplicationId = application.Id,
                FlatHouseNumber = request.LocalAddress.FlatHouseNumber,
                StreetAddress = request.LocalAddress.StreetAddress,
                AddressLine = request.LocalAddress.AddressLine,
                City = request.LocalAddress.City,
                State = request.LocalAddress.State,
                Country = request.LocalAddress.Country,
                PostalCode = request.LocalAddress.PostalCode,
                IsLocal = true
            };
            addresses.Add(localAddress);
        }

        // Save addresses
        foreach (var address in addresses)
        {
            await _unitOfWork.Repository<Address>().AddAsync(address);
        }
        await _unitOfWork.SaveChangesAsync();

        // Create experiences
        if (request.Experiences?.Any() == true)
        {
            foreach (var experienceDto in request.Experiences)
            {
                var experience = new Experience
                {
                    ApplicationId = application.Id,
                    CompanyName = experienceDto.CompanyName,
                    Position = experienceDto.Position,
                    YearsOfExperience = experienceDto.YearsOfExperience,
                    CertificateFilePath = experienceDto.CertificateFilePath,
                    FromDate = experienceDto.FromDate,
                    ToDate = experienceDto.ToDate,
                    ExperienceRequired = experienceDto.ExperienceRequired
                };
                
                await _unitOfWork.Repository<Experience>().AddAsync(experience);
            }
            await _unitOfWork.SaveChangesAsync();
        }

        // Route the application to the appropriate engineer based on position
        try
        {
            await _routingService.RouteApplicationAsync(application);
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the application creation
            // TODO: Add proper logging
            Console.WriteLine($"Failed to route application: {ex.Message}");
        }

        return CreatedAtAction(nameof(GetApplication), new { id = application.Id }, new
        {
            application.Id,
            application.ApplicationNumber,
            application.ApplicantName,
            Purpose = application.Position.ToString(), // Use Position instead of Purpose
            application.Amount,
            Status = application.CurrentState.ToString(),
            application.SubmissionDate,
            application.ApprovalDate
        });
    }

    /// <summary>
    /// Update application status
    /// </summary>
    /// <param name="id">Application ID</param>
    /// <param name="request">Status update request</param>
    /// <returns>Success message</returns>
    /// <response code="200">Status updated successfully</response>
    /// <response code="400">Invalid status</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Application not found</response>
    /// <response code="500">Internal server error</response>
    /// <summary>
    /// Update application status and assignment
    /// </summary>
    /// <param name="id">Application ID</param>
    /// <param name="request">Status update request</param>
    /// <returns>Updated application status</returns>
    /// <response code="200">Status updated successfully</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Application not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("{id}/status")]
    [Authorize(Roles = "Admin,Officer")]
    [ProducesResponseType(typeof(UpdateApplicationStatusResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> UpdateApplicationStatus(int id, [FromBody] UpdateApplicationStatusRequest request)
    {
        try
        {
            var application = await _unitOfWork.Repository<ApplicationEntity>().GetByIdAsync(id);
            
            if (application == null)
            {
                return NotFound(new { success = false, message = "Application not found" });
            }

            // Parse the status string to WorkflowStateType enum
            if (Enum.TryParse<WorkflowStateType>(request.Status, out var status))
            {
                application.CurrentState = status;
            }
            else
            {
                return BadRequest(new { success = false, message = "Invalid status" });
            }

            // Handle assignment if provided
            string assignedToName = "Unassigned";
            if (request.AssignedToUserId.HasValue)
            {
                var assignedUser = await _unitOfWork.Repository<User>().GetByIdAsync(request.AssignedToUserId.Value);
                if (assignedUser != null)
                {
                    application.AssignedToUserId = request.AssignedToUserId.Value;
                    application.AssignedTo = assignedUser;
                    application.AssignedAt = DateTime.UtcNow;
                    assignedToName = $"{assignedUser.FirstName} {assignedUser.LastName}";
                }
            }

            application.ApprovalDate = DateTime.UtcNow; // Update last modified time
            
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            var response = new UpdateApplicationStatusResponse
            {
                Success = true,
                Data = new UpdateApplicationStatusData
                {
                    Id = application.Id.ToString(),
                    Status = application.CurrentState.ToString(),
                    AssignedTo = assignedToName,
                    LastUpdated = application.ApprovalDate ?? DateTime.UtcNow
                },
                Message = "Application status updated successfully"
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to update application status", error = ex.Message });
        }
    }

    /// <summary>
    /// Get experiences for a specific application
    /// </summary>
    /// <param name="id">Application ID</param>
    /// <returns>List of experiences with calculated fields</returns>
    [HttpGet("{id}/experiences")]
    [ProducesResponseType(typeof(IEnumerable<object>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    public async Task<IActionResult> GetApplicationExperiences(int id)
    {
        var application = await _unitOfWork.Repository<ApplicationEntity>().GetByIdAsync(id);
        if (application == null)
        {
            return NotFound(new { message = "Application not found" });
        }

        var experiences = await _unitOfWork.Repository<Experience>()
            .FindAsync(e => e.ApplicationId == id);

        return Ok(experiences.Select(exp => new
        {
            exp.Id,
            exp.CompanyName,
            exp.Position,
            exp.YearsOfExperience,
            exp.CertificateFilePath,
            exp.FromDate,
            exp.ToDate,
            exp.TotalExperience,
            exp.MonthDifference,
            exp.YearDifference,
            exp.ExperienceRequired
        }));
    }

    #region Helper Methods

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value ?? "User";
    }

    private IQueryable<ApplicationEntity> FilterApplicationsByRole(IQueryable<ApplicationEntity> applications, string role, int userId)
    {
        switch (role)
        {
            case "Admin":
                // Admins see all applications
                return applications;

            case "JRENGG_ARCH": // Junior Engineer - Architect
                return applications.Where(a => 
                    a.Position == PositionType.Architect &&
                    (a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING || 
                     a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING) &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "JRENGG_STRU": // Junior Engineer - Structural
                return applications.Where(a => 
                    a.Position == PositionType.StructuralEngineer &&
                    (a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING || 
                     a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING) &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "JRENGG_LICE": // Junior Engineer - License
                return applications.Where(a => 
                    a.Position == PositionType.LicenseEngineer &&
                    (a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING || 
                     a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING) &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "JRENGG_SUPER1": // Junior Engineer - Supervisor1
                return applications.Where(a => 
                    a.Position == PositionType.Supervisor1 &&
                    (a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING || 
                     a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING) &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "JRENGG_SUPER2": // Junior Engineer - Supervisor2
                return applications.Where(a => 
                    a.Position == PositionType.Supervisor2 &&
                    (a.CurrentState == WorkflowStateType.DOCUMENT_VERIFICATION_PENDING || 
                     a.CurrentState == WorkflowStateType.JUNIOR_ENGINEER_PENDING) &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ASSIENGG_ARCH": // Assistant Engineer - Architect
                return applications.Where(a => 
                    a.Position == PositionType.Architect &&
                    a.CurrentState == WorkflowStateType.ASSISTANT_ENGINEER_PENDING &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ASSIENGG_STRU": // Assistant Engineer - Structural
                return applications.Where(a => 
                    a.Position == PositionType.StructuralEngineer &&
                    a.CurrentState == WorkflowStateType.ASSISTANT_ENGINEER_PENDING &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ASSIENGG_LICE": // Assistant Engineer - License
                return applications.Where(a => 
                    a.Position == PositionType.LicenseEngineer &&
                    a.CurrentState == WorkflowStateType.ASSISTANT_ENGINEER_PENDING &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ASSIENGG_SUPER1": // Assistant Engineer - Supervisor1
                return applications.Where(a => 
                    a.Position == PositionType.Supervisor1 &&
                    a.CurrentState == WorkflowStateType.ASSISTANT_ENGINEER_PENDING &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ASSIENGG_SUPER2": // Assistant Engineer - Supervisor2
                return applications.Where(a => 
                    a.Position == PositionType.Supervisor2 &&
                    a.CurrentState == WorkflowStateType.ASSISTANT_ENGINEER_PENDING &&
                    (a.AssignedToUserId == userId || a.AssignedToUserId == null));

            case "ExecutiveEngineer": // Executive Engineer
                return applications.Where(a => 
                    a.CurrentState == WorkflowStateType.EXECUTIVE_ENGINEER_PENDING ||
                    a.CurrentState == WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING);

            case "CityEngineer": // City Engineer
                return applications.Where(a => 
                    a.CurrentState == WorkflowStateType.CITY_ENGINEER_PENDING ||
                    a.CurrentState == WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING);

            case "Clerk": // Clerk
                return applications.Where(a => 
                    a.CurrentState == WorkflowStateType.CLERK_PENDING ||
                    a.CurrentState == WorkflowStateType.FINAL_APPROVE);

            case "OfflinePaymentOfficer": // Payment Officer
                return applications.Where(a => 
                    a.CurrentState == WorkflowStateType.PAYMENT_PENDING);

            case "User": // User (Applicant)
                return applications.Where(a => 
                    a.UserId == userId && a.CurrentState == WorkflowStateType.PAYMENT_PENDING);

            default:
                // Default: only show applications assigned to this user
                return applications.Where(a => a.AssignedToUserId == userId);
        }
    }

    /// <summary>
    /// Schedule appointment for document verification
    /// </summary>
    /// <param name="request">Appointment scheduling request</param>
    /// <returns>Appointment details</returns>
    /// <response code="200">Appointment scheduled successfully</response>
    /// <response code="400">Invalid request or application not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("schedule-appointment")]
    [ProducesResponseType(typeof(ScheduleAppointmentResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ScheduleAppointmentResponse>> ScheduleAppointment([FromBody] ScheduleAppointmentRequest request)
    {
        try
        {
            // Get current user
            var userId = GetCurrentUserId();
            if (userId == 0)
            {
                return Unauthorized(new ScheduleAppointmentResponse 
                { 
                    Success = false, 
                    Message = "Unauthorized: User ID not found" 
                });
            }

            // Validate and get application
            var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(a => a.Id == request.ApplicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                return BadRequest(new ScheduleAppointmentResponse 
                { 
                    Success = false, 
                    Message = $"Application with ID {request.ApplicationId} not found" 
                });
            }

            // Check if application is in correct state for appointment scheduling
            if (application.CurrentState != WorkflowStateType.DOCUMENT_VERIFICATION_PENDING)
            {
                return BadRequest(new ScheduleAppointmentResponse 
                { 
                    Success = false, 
                    Message = $"Application is in {application.CurrentState} state. Only applications in DOCUMENT_VERIFICATION_PENDING state can have appointments scheduled." 
                });
            }

            // Parse the review date from ISO format
            DateTime parsedReviewDate;
            if (!DateTime.TryParse(request.ReviewDate, out parsedReviewDate))
            {
                return BadRequest(new ScheduleAppointmentResponse 
                { 
                    Success = false, 
                    Message = $"Invalid date format. Please provide a valid date and time." 
                });
            }

            // Convert to UTC for PostgreSQL compatibility
            parsedReviewDate = DateTime.SpecifyKind(parsedReviewDate, DateTimeKind.Utc);

            // Update application with appointment details
            var previousStatus = application.CurrentState.ToString();
            application.ReviewDate = request.ReviewDate;
            application.ContactPerson = request.ContactPerson;
            application.Place = request.Place;
            application.RoomNumber = request.RoomNumber;
            application.CurrentState = WorkflowStateType.JUNIOR_ENGINEER_PENDING;

            // Add workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                FromState = WorkflowStateType.DOCUMENT_VERIFICATION_PENDING,
                ToState = WorkflowStateType.JUNIOR_ENGINEER_PENDING,
                TransitionDate = DateTime.UtcNow,
                ActionByUserId = userId,
                Remarks = $"Appointment scheduled - Contact: {request.ContactPerson}, Place: {request.Place}, Room: {request.RoomNumber}"
            });

            // Create appointment record
            var appointment = new Appointment
            {
                ApplicationId = application.Id,
                UserId = userId,
                Title = $"Document Verification - {application.ApplicationNumber}",
                Description = $"Document verification appointment for {application.ApplicantName}",
                AppointmentDate = parsedReviewDate,
                Status = "Scheduled",
                Location = $"{request.Place}, Room: {request.RoomNumber}",
                Notes = request.Notes,
                CreatedAt = DateTime.UtcNow
            };

            // Save appointment directly using DbContext
            _context.Appointments.Add(appointment);

            // Update application
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            // Return response
            var response = new ScheduleAppointmentResponse
            {
                Success = true,
                Message = "Appointment scheduled successfully",
                Data = new AppointmentDetails
                {
                    ApplicationId = application.Id,
                    ApplicationNumber = application.ApplicationNumber,
                    ApplicantName = application.ApplicantName,
                    ReviewDate = application.ReviewDate ?? "",
                    ContactPerson = application.ContactPerson ?? "",
                    Place = application.Place,
                    RoomNumber = application.RoomNumber,
                    PreviousStatus = previousStatus,
                    NewStatus = application.CurrentState.ToString(),
                    ScheduledAt = DateTime.UtcNow
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new ScheduleAppointmentResponse 
            { 
                Success = false, 
                Message = $"Internal server error: {ex.Message}" 
            });
        }
    }

    /// <summary>
    /// Complete document verification and move to Assistant Engineer
    /// </summary>
    /// <param name="applicationId">Application ID</param>
    /// <returns>Success response</returns>
    /// <response code="200">Document verification completed successfully</response>
    /// <response code="400">Invalid request or application not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("{applicationId}/complete-verification")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> CompleteDocumentVerification(int applicationId)
    {
        try
        {
            // Get current user
            var userId = GetCurrentUserId();
            if (userId == 0)
            {
                return Unauthorized(new { success = false, message = "Unauthorized: User ID not found" });
            }

            // Get application
            var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(a => a.Id == applicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                return BadRequest(new { success = false, message = $"Application with ID {applicationId} not found" });
            }

            // Check if application is in correct state
            if (application.CurrentState != WorkflowStateType.JUNIOR_ENGINEER_PENDING && 
                application.CurrentState != WorkflowStateType.DOCUMENT_VERIFICATION_PENDING)
            {
                return BadRequest(new { 
                    success = false, 
                    message = $"Application is in {application.CurrentState} state. Only applications in JUNIOR_ENGINEER_PENDING or DOCUMENT_VERIFICATION_PENDING state can be completed." 
                });
            }

            // Move to next stage based on position
            var nextState = WorkflowStateType.ASSISTANT_ENGINEER_PENDING;
            
            // Update application state
            var previousState = application.CurrentState;
            application.CurrentState = nextState;

            // Add workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                FromState = previousState,
                ToState = nextState,
                TransitionDate = DateTime.UtcNow,
                ActionByUserId = userId,
                Remarks = "Document verification completed by Junior Engineer"
            });

            // Update application
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new { 
                success = true, 
                message = "Document verification completed successfully",
                data = new {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    previousStatus = previousState.ToString(),
                    newStatus = nextState.ToString(),
                    completedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = $"Internal server error: {ex.Message}" });
        }
    }

    /// <summary>
    /// Complete Assistant Engineer review and move to Executive Engineer
    /// </summary>
    /// <param name="applicationId">Application ID</param>
    /// <returns>Success response</returns>
    /// <response code="200">Assistant Engineer review completed successfully</response>
    /// <response code="400">Invalid request or application not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("{applicationId}/complete-assistant-review")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> CompleteAssistantEngineeerReview(int applicationId)
    {
        try
        {
            // Get current user
            var userId = GetCurrentUserId();
            if (userId == 0)
            {
                return Unauthorized(new { success = false, message = "Unauthorized: User ID not found" });
            }

            // Verify user has Assistant Engineer role
            var userRole = GetCurrentUserRole();
            if (!userRole.StartsWith("ASSIENGG_"))
            {
                return BadRequest(new { success = false, message = "Access denied: Only Assistant Engineers can complete this action" });
            }

            // Get application
            var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(a => a.Id == applicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                return BadRequest(new { success = false, message = $"Application with ID {applicationId} not found" });
            }

            // Check if application is in correct state
            if (application.CurrentState != WorkflowStateType.ASSISTANT_ENGINEER_PENDING)
            {
                return BadRequest(new { 
                    success = false, 
                    message = $"Application is in {application.CurrentState} state. Only applications in ASSISTANT_ENGINEER_PENDING state can be approved." 
                });
            }

            // Move to Executive Engineer stage
            var nextState = WorkflowStateType.EXECUTIVE_ENGINEER_PENDING;
            
            // Update application state
            var previousState = application.CurrentState;
            application.CurrentState = nextState;

            // Add workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                FromState = previousState,
                ToState = nextState,
                TransitionDate = DateTime.UtcNow,
                ActionByUserId = userId,
                Remarks = "Application approved by Assistant Engineer"
            });

            // Update application
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new
            {
                success = true,
                message = "Assistant Engineer review completed successfully",
                data = new
                {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    previousStatus = previousState.ToString(),
                    newStatus = nextState.ToString(),
                    approvedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = $"Internal server error: {ex.Message}" });
        }
    }

    /// <summary>
    /// Complete Executive Engineer review and move to City Engineer
    /// </summary>
    /// <param name="applicationId">Application ID</param>
    /// <returns>Success response</returns>
    /// <response code="200">Executive Engineer review completed successfully</response>
    /// <response code="400">Invalid request or application not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("{applicationId}/complete-executive-review")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> CompleteExecutiveEngineerReview(int applicationId)
    {
        try
        {
            // Get current user
            var userId = GetCurrentUserId();
            if (userId == 0)
            {
                return Unauthorized(new { success = false, message = "Unauthorized: User ID not found" });
            }

            // Verify user has Executive Engineer role
            var userRole = GetCurrentUserRole();
            if (userRole != "ExecutiveEngineer")
            {
                return BadRequest(new { success = false, message = "Access denied: Only Executive Engineers can complete this action" });
            }

            // Get application
            var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(a => a.Id == applicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                return BadRequest(new { success = false, message = $"Application with ID {applicationId} not found" });
            }

            // Check if application is in correct state
            if (application.CurrentState != WorkflowStateType.EXECUTIVE_ENGINEER_PENDING)
            {
                return BadRequest(new { 
                    success = false, 
                    message = $"Application is in {application.CurrentState} state. Only applications in EXECUTIVE_ENGINEER_PENDING state can be authorized." 
                });
            }

            // Move to City Engineer stage
            var nextState = WorkflowStateType.CITY_ENGINEER_PENDING;
            
            // Update application state
            var previousState = application.CurrentState;
            application.CurrentState = nextState;

            // Add workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                FromState = previousState,
                ToState = nextState,
                TransitionDate = DateTime.UtcNow,
                ActionByUserId = userId,
                Remarks = "Application authorized by Executive Engineer"
            });

            // Update application
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new
            {
                success = true,
                message = "Executive Engineer review completed successfully",
                data = new
                {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    previousStatus = previousState.ToString(),
                    newStatus = nextState.ToString(),
                    authorizedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = $"Internal server error: {ex.Message}" });
        }
    }

    /// <summary>
    /// Complete City Engineer review and issue certificate (final stage)
    /// </summary>
    /// <param name="applicationId">Application ID</param>
    /// <returns>Success response</returns>
    /// <response code="200">City Engineer review completed and certificate issued</response>
    /// <response code="400">Invalid request or application not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("{applicationId}/complete-city-engineer-review")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> CompleteCityEngineerReview(int applicationId)
    {
        try
        {
            // Get current user
            var userId = GetCurrentUserId();
            if (userId == 0)
            {
                return Unauthorized(new { success = false, message = "Unauthorized: User ID not found" });
            }

            // Verify user has City Engineer role
            var userRole = GetCurrentUserRole();
            if (userRole != "CityEngineer")
            {
                return BadRequest(new { success = false, message = "Access denied: Only City Engineers can complete this action" });
            }

            // Get application
            var applications = await _unitOfWork.Repository<ApplicationEntity>().FindAsync(a => a.Id == applicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                return BadRequest(new { success = false, message = $"Application with ID {applicationId} not found" });
            }

            // Check if application is in correct state
            if (application.CurrentState != WorkflowStateType.CITY_ENGINEER_PENDING)
            {
                return BadRequest(new { 
                    success = false, 
                    message = $"Application is in {application.CurrentState} state. Only applications in CITY_ENGINEER_PENDING state can be approved for certificate issuance." 
                });
            }

            // Complete the workflow and move to Payment stage
            var nextState = WorkflowStateType.PAYMENT_PENDING;
            
            // Update application state
            var previousState = application.CurrentState;
            application.CurrentState = nextState;

            // Add workflow history
            application.WorkflowStateHistory.Add(new WorkflowStateHistory
            {
                FromState = previousState,
                ToState = nextState,
                TransitionDate = DateTime.UtcNow,
                ActionByUserId = userId,
                Remarks = "Certificate issued by City Engineer - Application moved to Payment stage"
            });

            // Update application
            _unitOfWork.Repository<ApplicationEntity>().Update(application);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new
            {
                success = true,
                message = "City Engineer review completed and application moved to Payment stage",
                data = new
                {
                    applicationId = application.Id,
                    applicationNumber = application.ApplicationNumber,
                    previousStatus = previousState.ToString(),
                    newStatus = nextState.ToString(),
                    processedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = $"Internal server error: {ex.Message}" });
        }
    }

    #endregion
}
