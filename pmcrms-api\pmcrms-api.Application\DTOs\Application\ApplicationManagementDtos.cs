namespace pmcrms_api.Application.DTOs.Application;

// Applications list response DTOs
public class ApplicationsResponse
{
    public bool Success { get; set; }
    public List<ApplicationListDto> Data { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

public class ApplicationListDto
{
    public string Id { get; set; } = string.Empty;
    public string ApplicationNumber { get; set; } = string.Empty;
    public string ApplicantName { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime SubmittedDate { get; set; }
    public string AssignedTo { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string CurrentStage { get; set; } = string.Empty;
    public string NextAction { get; set; } = string.Empty;
    public int DocumentsCount { get; set; }
    public int VerifiedDocuments { get; set; }
    public int ProcessingDays { get; set; }
}

// Application status update DTOs
public class UpdateApplicationStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int? AssignedToUserId { get; set; }
}

public class UpdateApplicationStatusResponse
{
    public bool Success { get; set; }
    public UpdateApplicationStatusData Data { get; set; } = null!;
    public string Message { get; set; } = string.Empty;
}

public class UpdateApplicationStatusData
{
    public string Id { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string AssignedTo { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}
