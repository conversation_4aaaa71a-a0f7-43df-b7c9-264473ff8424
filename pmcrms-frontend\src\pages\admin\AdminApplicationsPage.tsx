import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { 
  FileText,
  Filter,
  Download,
  Eye,
  Edit,
  MoreHorizontal,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { InputField, SelectField } from '../../components/ui/forms'
import { AdminAPIService } from '../../lib/adminAuth'
import { WorkflowState, PositionType } from '../../types'
import AdminLayout from '../../components/AdminLayout'

interface Application {
  id: string
  applicationNumber: string
  applicantName: string
  position: PositionType
  status: WorkflowState
  submittedDate: string
  assignedTo?: string
  lastUpdated: string
}

const AdminApplicationsPage: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    status: '',
    position: '',
    search: '',
    dateFrom: '',
    dateTo: ''
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const [selectedApplications, setSelectedApplications] = useState<string[]>([])

  // Mock data for development
  const mockApplications: Application[] = [
    {
      id: '1',
      applicationNumber: 'PMC-2025-0150',
      applicantName: 'John Doe',
      position: 'Architect',
      status: 'DOCUMENT_VERIFICATION_PENDING',
      submittedDate: '2025-08-20',
      assignedTo: 'Jane Smith',
      lastUpdated: '2025-08-22'
    },
    {
      id: '2',
      applicationNumber: 'PMC-2025-0151',
      applicantName: 'Alice Johnson',
      position: 'License Engineer',
      status: 'FINAL_APPROVE',
      submittedDate: '2025-08-18',
      assignedTo: 'Bob Wilson',
      lastUpdated: '2025-08-21'
    },
    {
      id: '3',
      applicationNumber: 'PMC-2025-0152',
      applicantName: 'Robert Brown',
      position: 'Structural Engineer',
      status: 'JUNIOR_ENGINEER_PENDING',
      submittedDate: '2025-08-15',
      assignedTo: 'Carol Davis',
      lastUpdated: '2025-08-19'
    },
    {
      id: '4',
      applicationNumber: 'PMC-2025-0153',
      applicantName: 'Emily White',
      position: 'Supervisor1',
      status: 'PAYMENT_PENDING',
      submittedDate: '2025-08-22',
      assignedTo: 'David Lee',
      lastUpdated: '2025-08-23'
    },
    {
      id: '5',
      applicationNumber: 'PMC-2025-0154',
      applicantName: 'Michael Green',
      position: 'Supervisor2',
      status: 'EXECUTIVE_ENGINEER_PENDING',
      submittedDate: '2025-08-21',
      lastUpdated: '2025-08-23'
    }
  ]

  useEffect(() => {
    loadApplications()
  }, [filters, pagination.page])

  const loadApplications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Try to fetch from API first
      try {
        const params = {
          page: pagination.page,
          limit: pagination.limit,
          ...filters
        }
        const response = await AdminAPIService.getApplications(params) as any
        setApplications(response.data || [])
        setPagination(prev => ({
          ...prev,
          total: response.total || 0,
          totalPages: Math.ceil((response.total || 0) / prev.limit)
        }))
      } catch (apiError) {
        // Use mock data if API fails
        console.warn('API call failed, using mock data:', apiError)
        setApplications(mockApplications)
        setPagination(prev => ({
          ...prev,
          total: mockApplications.length,
          totalPages: Math.ceil(mockApplications.length / prev.limit)
        }))
      }
    } catch (error: any) {
      setError(error.message || 'Failed to load applications')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: WorkflowState) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (status) {
      case 'FINAL_APPROVE':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'DOCUMENT_VERIFICATION_PENDING':
        return `${baseClasses} bg-orange-100 text-orange-800`
      case 'JUNIOR_ENGINEER_PENDING':
      case 'ASSISTANT_ENGINEER_PENDING':
        return `${baseClasses} bg-blue-100 text-blue-800`
      case 'EXECUTIVE_ENGINEER_PENDING':
      case 'CITY_ENGINEER_PENDING':
        return `${baseClasses} bg-purple-100 text-purple-800`
      case 'PAYMENT_PENDING':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'CLERK_PENDING':
        return `${baseClasses} bg-indigo-100 text-indigo-800`
      case 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING':
      case 'CITY_DIGITAL_SIGNATURE_PENDING':
        return `${baseClasses} bg-pink-100 text-pink-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const getStatusDisplayName = (status: WorkflowState): string => {
    switch (status) {
      case 'DOCUMENT_VERIFICATION_PENDING':
        return 'Document Verification'
      case 'JUNIOR_ENGINEER_PENDING':
        return 'Junior Engineer Review'
      case 'ASSISTANT_ENGINEER_PENDING':
        return 'Assistant Engineer Review'
      case 'EXECUTIVE_ENGINEER_PENDING':
        return 'Executive Engineer Review'
      case 'CITY_ENGINEER_PENDING':
        return 'City Engineer Review'
      case 'PAYMENT_PENDING':
        return 'Payment Pending'
      case 'CLERK_PENDING':
        return 'Clerk Processing'
      case 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING':
        return 'Executive Digital Signature'
      case 'CITY_DIGITAL_SIGNATURE_PENDING':
        return 'City Digital Signature'
      case 'FINAL_APPROVE':
        return 'Final Approved'
      default:
        return status.replace(/_/g, ' ').toLowerCase()
    }
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSelectApplication = (id: string) => {
    setSelectedApplications(prev => 
      prev.includes(id) 
        ? prev.filter(appId => appId !== id)
        : [...prev, id]
    )
  }

  const handleSelectAll = () => {
    if (selectedApplications.length === applications.length) {
      setSelectedApplications([])
    } else {
      setSelectedApplications(applications.map(app => app.id))
    }
  }

  const positionOptions = [
    { value: '', label: 'All Positions' },
    { value: 'Architect', label: 'Architect' },
    { value: 'License Engineer', label: 'License Engineer' },
    { value: 'Structural Engineer', label: 'Structural Engineer' },
    { value: 'Supervisor1', label: 'Supervisor1' },
    { value: 'Supervisor2', label: 'Supervisor2' }
  ]

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'DOCUMENT_VERIFICATION_PENDING', label: 'Document Verification' },
    { value: 'JUNIOR_ENGINEER_PENDING', label: 'Junior Engineer Review' },
    { value: 'ASSISTANT_ENGINEER_PENDING', label: 'Assistant Engineer Review' },
    { value: 'EXECUTIVE_ENGINEER_PENDING', label: 'Executive Engineer Review' },
    { value: 'CITY_ENGINEER_PENDING', label: 'City Engineer Review' },
    { value: 'PAYMENT_PENDING', label: 'Payment Pending' },
    { value: 'CLERK_PENDING', label: 'Clerk Processing' },
    { value: 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING', label: 'Executive Digital Signature' },
    { value: 'CITY_DIGITAL_SIGNATURE_PENDING', label: 'City Digital Signature' },
    { value: 'FINAL_APPROVE', label: 'Final Approved' }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Applications Management</h1>
            <p className="text-gray-600">View and manage all applications in the system</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>New Application</span>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <InputField
                label="Search"
                placeholder="Search by name or number..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
              
              <SelectField
                label="Status"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                options={statusOptions}
              />
              
              <SelectField
                label="Position"
                value={filters.position}
                onChange={(e) => handleFilterChange('position', e.target.value)}
                options={positionOptions}
              />
              
              <InputField
                label="Date From"
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              />
              
              <InputField
                label="Date To"
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              />
            </div>
            
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-600">
                {selectedApplications.length > 0 && (
                  <span>{selectedApplications.length} application(s) selected</span>
                )}
              </div>
              <div className="flex space-x-2">
                {selectedApplications.length > 0 && (
                  <>
                    <Button variant="outline" size="sm">Bulk Assign</Button>
                    <Button variant="outline" size="sm">Bulk Update</Button>
                  </>
                )}
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setFilters({ status: '', position: '', search: '', dateFrom: '', dateTo: '' })}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Applications Table */}
        <Card>
          <CardHeader>
            <CardTitle>Applications ({pagination.total})</CardTitle>
            <CardDescription>
              Showing {applications.length} of {pagination.total} applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading applications...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-600">
                <p>Error: {error}</p>
                <Button onClick={loadApplications} className="mt-2">Retry</Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedApplications.length === applications.length && applications.length > 0}
                          onChange={handleSelectAll}
                          className="rounded border-gray-300"
                        />
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center space-x-1">
                          <span>Application Number</span>
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Applicant
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Position
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Submitted
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigned To
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {applications.map((application) => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4">
                          <input
                            type="checkbox"
                            checked={selectedApplications.includes(application.id)}
                            onChange={() => handleSelectApplication(application.id)}
                            className="rounded border-gray-300"
                          />
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {application.applicationNumber}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{application.applicantName}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{application.position}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={getStatusBadge(application.status)}>
                            {getStatusDisplayName(application.status)}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(application.submittedDate).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.assignedTo || '-'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {applications.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No applications found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Try adjusting your filters or search criteria.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} results
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <span className="text-sm">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

export default AdminApplicationsPage
