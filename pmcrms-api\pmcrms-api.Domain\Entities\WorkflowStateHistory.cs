using System.ComponentModel.DataAnnotations;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Domain.Entities;

public class WorkflowStateHistory
{
    [Key]
    public int Id { get; set; }

    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;

    public WorkflowStateType FromState { get; set; }
    public WorkflowStateType ToState { get; set; }

    public int ActionByUserId { get; set; }
    public User ActionByUser { get; set; } = null!;

    [StringLength(500)]
    public string? Remarks { get; set; }

    public DateTime TransitionDate { get; set; } = DateTime.UtcNow;
}
