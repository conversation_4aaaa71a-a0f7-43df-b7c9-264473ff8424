using pmcrms_api.Domain.Models;

namespace pmcrms_api.Application.Interfaces
{
    public interface IEmailService
    {
        Task SendEmailAsync(EmailMessage message);
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
        Task SendEmailWithAttachmentAsync(EmailMessage message, byte[] attachment, string fileName, string contentType = "application/pdf");
    }
}
