/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_NODE_ENV: string
  readonly VITE_ENABLE_OTP_LOGIN: string
  readonly VITE_ENABLE_NOTIFICATIONS: string
  readonly VITE_ENABLE_WEBSOCKETS: string
  readonly VITE_CUBEJS_TOKEN: string
  readonly VITE_CUBEJS_API_URL: string
  readonly VITE_RAZORPAY_KEY: string
  readonly VITE_GOOGLE_MAPS_KEY: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
