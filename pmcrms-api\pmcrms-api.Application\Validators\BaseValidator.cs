using FluentValidation;
using FluentValidation.Results;

namespace pmcrms_api.Application.Validators
{
    public abstract class BaseValidator<T> : AbstractValidator<T>
    {
        protected override void RaiseValidationException(ValidationContext<T> context, FluentValidation.Results.ValidationResult result)
        {
            throw new pmcrms_api.Application.Exceptions.ValidationException(result.Errors.ToList());
        }
    }
}
