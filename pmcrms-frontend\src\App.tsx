import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Toaster } from './components/ui/toaster'

// Import pages
import LandingPage from './pages/LandingPage'
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'
import OfficerLoginPage from './pages/auth/OfficerLoginPage'
import OfficerPasswordSetupPage from './pages/auth/OfficerPasswordSetupPage'
import OfficerProfilePage from './pages/officer/OfficerProfilePage'
import AdminLoginPage from './pages/auth/AdminLoginPage'
import ApplicationForm from './pages/application/ApplicationForm'
import ApplicationStatus from './pages/application/ApplicationStatus'
import GuestApplicationForm from './pages/application/GuestApplicationForm'
import GuestApplicationView from './pages/application/GuestApplicationView'
import GuestLogin from './pages/auth/GuestLogin'

// Import dashboards
import JuniorEngineerDashboard from './pages/dashboard/JuniorEngineerDashboard'
import AssistantEngineerDashboard from './pages/dashboard/AssistantEngineerDashboard'
import ExecutiveEngineerDashboard from './pages/dashboard/ExecutiveEngineerDashboard'
import CityEngineerDashboard from './pages/dashboard/CityEngineerDashboard'
import ClerkDashboard from './pages/dashboard/ClerkDashboard'
import AdminDashboard from './pages/dashboard/AdminDashboard'
import GuestDashboard from './pages/dashboard/GuestDashboard'

// Import admin pages
import AdminApplicationsPage from './pages/admin/AdminApplicationsPage'
import AdminReportsPage from './pages/admin/AdminReportsPage'
import OfficerManagementPage from './pages/admin/OfficerManagementPage'

// Import test page
import ApiTest from './pages/ApiTest'

// Import components
import ProtectedRoute from './components/ProtectedRoute'
import AdminProtectedRoute from './components/AdminProtectedRoute'
import Layout from './components/Layout'
import DashboardRouter from './components/DashboardRouter'
import CSSTest from './pages/CSSTest'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="App">
          <Routes>
            {/* CSS Test Route */}
            <Route path="/csstest" element={<CSSTest />} />
            <Route path="/api-test" element={<ApiTest />} />
            
            {/* Public Routes */}
            <Route path="/" element={<LandingPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/officer/login" element={<OfficerLoginPage />} />
            <Route path="/officer/setup-password" element={<OfficerPasswordSetupPage />} />
            <Route path="/officer/profile" element={
              <ProtectedRoute allowedRoles={[
                'JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2',
                'ASSIENGG_ARCH', 'ASSIENGG_STRU', 'ASSIENGG_LICE', 'ASSIENGG_SUPER1', 'ASSIENGG_SUPER2',
                'ExecutiveEngineer', 'CityEngineer', 'CLERK', 'OFFLINE_PAYMENT_OFFICER'
              ]}>
                <OfficerProfilePage />
              </ProtectedRoute>
            } />
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/guest" element={<GuestLogin />} />
            <Route path="/apply" element={<ApplicationForm />} />
            <Route path="/application/new" element={<GuestApplicationForm />} />
            <Route path="/application/view/:applicationNumber" element={<GuestApplicationView />} />
            <Route path="/status" element={<ApplicationStatus />} />
            <Route path="/dashboard/guest" element={<GuestDashboard />} />

            {/* Protected Routes - Junior Engineers */}
            <Route 
              path="/dashboard/junior" 
              element={
                <ProtectedRoute allowedRoles={['JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2']}>
                  <Layout>
                    <JuniorEngineerDashboard />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* Protected Routes - Assistant Engineers */}
            <Route 
              path="/dashboard/assistant" 
              element={
                <ProtectedRoute allowedRoles={['ASSIENGG_ARCH', 'ASSIENGG_STRU', 'ASSIENGG_LICE', 'ASSIENGG_SUPER1', 'ASSIENGG_SUPER2']}>
                  <Layout>
                    <AssistantEngineerDashboard />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* Protected Routes - Executive Engineer */}
            <Route 
              path="/dashboard/executive" 
              element={
                <ProtectedRoute allowedRoles={['ExecutiveEngineer']}>
                  <Layout>
                    <ExecutiveEngineerDashboard />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* Protected Routes - City Engineer */}
            <Route 
              path="/dashboard/city" 
              element={
                <ProtectedRoute allowedRoles={['CityEngineer']}>
                  <Layout>
                    <CityEngineerDashboard />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* Protected Routes - Clerk */}
            <Route 
              path="/dashboard/clerk" 
              element={
                <ProtectedRoute allowedRoles={['CLERK']}>
                  <Layout>
                    <ClerkDashboard />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* Protected Routes - Admin */}
            <Route 
              path="/admin/dashboard" 
              element={
                <AdminProtectedRoute>
                  <AdminDashboard />
                </AdminProtectedRoute>
              } 
            />

            <Route 
              path="/admin/applications" 
              element={
                <AdminProtectedRoute>
                  <AdminApplicationsPage />
                </AdminProtectedRoute>
              } 
            />

            <Route 
              path="/admin/reports" 
              element={
                <AdminProtectedRoute>
                  <AdminReportsPage />
                </AdminProtectedRoute>
              } 
            />

            <Route 
              path="/admin/officers" 
              element={
                <AdminProtectedRoute>
                  <OfficerManagementPage />
                </AdminProtectedRoute>
              } 
            />

            {/* General dashboard route for regular users */}
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute allowedRoles={['PUBLIC_USER', 'JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2', 'ASSIENGG_ARCH', 'ASSIENGG_STRU', 'ASSIENGG_LICE', 'ASSIENGG_SUPER1', 'ASSIENGG_SUPER2', 'ExecutiveEngineer', 'CityEngineer', 'CLERK']}>
                  <Layout>
                    <DashboardRouter />
                  </Layout>
                </ProtectedRoute>
              } 
            />

            {/* 404 Route */}
            <Route 
              path="*" 
              element={
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                    <p className="text-gray-600 mb-8">Page not found</p>
                    <a href="/" className="text-primary hover:text-primary-600">
                      Go back to home
                    </a>
                  </div>
                </div>
              } 
            />
          </Routes>
          
          <Toaster />
        </div>
      </Router>
    </QueryClientProvider>
  )
}

export default App
