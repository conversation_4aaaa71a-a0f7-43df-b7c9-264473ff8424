import cubejs from '@cubejs-client/core';

// Cube.js configuration - using import.meta.env for Vite
const cubeApi = cubejs(
  import.meta.env.VITE_CUBEJS_TOKEN || 'demo-token', // Replace with your Cube.js token
  {
    apiUrl: import.meta.env.VITE_CUBEJS_API_URL || 'http://localhost:4000/cubejs-api/v1'
  }
);

export default cubeApi;

// Pre-defined queries for common reports
export const CUBE_QUERIES = {
  // Applications by status (workflow stages)
  applicationsByStatus: {
    measures: ['Applications.count'],
    dimensions: ['Applications.status'],
  },
  
  // Applications by position
  applicationsByPosition: {
    measures: ['Applications.count'],
    dimensions: ['Applications.position'],
  },
  
  // Applications by status and position (drill-down)
  applicationsByStatusAndPosition: {
    measures: ['Applications.count'],
    dimensions: ['Applications.status', 'Applications.position'],
  },
  
  // Applications over time
  applicationsOverTime: {
    measures: ['Applications.count'],
    timeDimensions: [{
      dimension: 'Applications.submittedDate',
      granularity: 'month',
      dateRange: 'last 12 months'
    }],
  },
  
  // Applications by officer performance
  applicationsByOfficer: {
    measures: ['Applications.count', 'Applications.averageProcessingTime'],
    dimensions: ['Applications.assignedTo', 'Applications.status'],
  },
  
  // Dashboard overview
  dashboardOverview: {
    measures: [
      'Applications.count',
      'Applications.pendingCount',
      'Applications.approvedCount',
      'Applications.rejectedCount',
      'Applications.averageProcessingTime'
    ],
  }
};
