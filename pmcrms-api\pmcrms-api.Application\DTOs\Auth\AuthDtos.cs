namespace pmcrms_api.Application.DTOs.Auth;

public record SendOtpRequest(string Email, string PhoneNumber);
public record VerifyOtpRequest(string Email, string Otp);
public record LoginRequest(string Email, string Password);
public record SetPasswordRequest(string Email, string Password, string ConfirmPassword);
public record CheckStatusRequest(string Email);
public record ValidateSetupTokenRequest(string Token, string Email);
public record SetupPasswordWithTokenRequest(string Token, string Email, string Password, string ConfirmPassword);
public record InviteOfficerRequest(string Email, string FirstName, string LastName, string Role, string PhoneNumber);
public record AuthResponse(string Token, string Email, string FirstName, string LastName, string Role);

// Guest OTP DTOs
public record SendGuestOtpRequest(string? ApplicationNumber = null, string? MobileNumber = null, string? Email = null);
public record VerifyGuestOtpRequest(string Identifier, string Otp); // Identifier can be ApplicationNumber or Email

// Admin Authentication DTOs
public class AdminLoginResponse
{
    public bool Success { get; set; }
    public AdminLoginData Data { get; set; } = null!;
    public string Message { get; set; } = string.Empty;
}

public class AdminLoginData
{
    public string Token { get; set; } = string.Empty;
    public AdminUser User { get; set; } = null!;
}

public class AdminUser
{
    public int Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
}

// Token verification DTOs
public class TokenVerificationResponse
{
    public bool Success { get; set; }
    public TokenVerificationData Data { get; set; } = null!;
}

public class TokenVerificationData
{
    public bool Valid { get; set; }
    public AdminUser User { get; set; } = null!;
}
