import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs'
import { Badge } from '../../components/ui/badge'
import { FileText, CheckCircle, Clock, AlertCircle, User, MapPin, Calendar, Loader2 } from 'lucide-react'
import { useAuthStore } from '../../store'
import api from '../../lib/api'
import ScheduleAppointmentModal from '../../components/ScheduleAppointmentModal'
import ApplicationDetailsModal from '../../components/ApplicationDetailsModal'

interface Application {
  id: number
  applicationNumber: string
  applicantName?: string
  firstName?: string
  lastName?: string
  position: string
  status?: string
  workflowState?: string
  submittedDate: string
  permanentAddress?: string
  phoneNumber?: string
  reviewDate?: string
  contactPerson?: string
  place?: string
  roomNumber?: string
}

const JuniorEngineerDashboard: React.FC = () => {
  const [documentVerificationApps, setDocumentVerificationApps] = useState<Application[]>([])
  const [juniorEngineerApps, setJuniorEngineerApps] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showScheduleModal, setShowScheduleModal] = useState(false)
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedApplicationId, setSelectedApplicationId] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState('document-verification')
  const { token } = useAuthStore()

  const loadApplications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await api.get('/applications', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      
      console.log('Applications loaded:', response.data)
      
      // Handle the response structure - data is nested inside response.data.data
      const responseData = response.data
      if (responseData.success && Array.isArray(responseData.data)) {
        const allApps = responseData.data.map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState, // Use status field as workflowState
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        
        console.log('Processed applications:', allApps)
        
        // Separate applications by workflow state
        const docVerificationApps = allApps.filter((app: Application) => 
          app.workflowState === 'DOCUMENT_VERIFICATION_PENDING'
        )
        const juniorEngApps = allApps.filter((app: Application) => 
          app.workflowState === 'JUNIOR_ENGINEER_PENDING'
        )
        
        console.log('Doc verification apps:', docVerificationApps)
        console.log('Junior engineer apps:', juniorEngApps)
        
        setDocumentVerificationApps(docVerificationApps)
        setJuniorEngineerApps(juniorEngApps)
      } else {
        const allApps = (responseData || []).map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState,
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        
        // Separate applications by workflow state
        const docVerificationApps = allApps.filter((app: Application) => 
          app.workflowState === 'DOCUMENT_VERIFICATION_PENDING'
        )
        const juniorEngApps = allApps.filter((app: Application) => 
          app.workflowState === 'JUNIOR_ENGINEER_PENDING'
        )
        
        setDocumentVerificationApps(docVerificationApps)
        setJuniorEngineerApps(juniorEngApps)
      }
    } catch (err: any) {
      console.error('Failed to load applications:', err)
      setError(err.response?.data?.message || 'Failed to load applications')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (token) {
      loadApplications()
    }
  }, [token])

  const handleScheduleAppointment = (application: Application) => {
    setSelectedApplication(application)
    setShowScheduleModal(true)
  }

  const handleViewDetails = (applicationId: number) => {
    console.log('handleViewDetails called with applicationId:', applicationId)
    setSelectedApplicationId(applicationId)
    setShowDetailsModal(true)
    console.log('State updated - showDetailsModal should be true')
  }

  const handleCompleteVerification = async (applicationId: number) => {
    try {
      const response = await api.post(`/applications/${applicationId}/complete-verification`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        // Remove from junior engineer apps and reload applications
        setJuniorEngineerApps(prev => prev.filter(app => app.id !== applicationId))
        loadApplications()
      }
    } catch (err: any) {
      console.error('Failed to complete verification:', err)
      setError(err.response?.data?.message || 'Failed to complete verification')
    }
  }

  const handleAppointmentScheduled = () => {
    // Reload applications to show updated status and refresh both tabs
    loadApplications()
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Junior Engineer Dashboard</h1>
        <p className="text-gray-600">Manage document verification and initial reviews</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Applications awaiting review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Verified Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">Documents verified today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Need Attention</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Applications with issues</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45</div>
            <p className="text-xs text-muted-foreground">Applications processed</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information - Remove after testing */}
      <Card className="mb-4">
        <CardContent className="pt-4">
          <div className="text-sm space-y-2">
            <div>Total Apps: {documentVerificationApps.length + juniorEngineerApps.length}</div>
            <div>Document Verification Apps: {documentVerificationApps.length}</div>
            <div>Junior Engineer Apps: {juniorEngineerApps.length}</div>
            <div className="mt-2">
              <strong>Doc Verification IDs:</strong> {documentVerificationApps.map(app => app.id).join(', ')}
            </div>
            <div>
              <strong>Junior Engineer IDs:</strong> {juniorEngineerApps.map(app => app.id).join(', ')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="document-verification" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Document Verification ({documentVerificationApps.length})
          </TabsTrigger>
          <TabsTrigger value="junior-engineer" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Junior Engineer Pending ({juniorEngineerApps.length})
          </TabsTrigger>
        </TabsList>

        {/* Document Verification Tab */}
        <TabsContent value="document-verification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Applications Pending Document Verification
              </CardTitle>
              <CardDescription>
                Applications that need appointment scheduling and document verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading applications...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Applications</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadApplications} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : documentVerificationApps.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications for Document Verification</h3>
                  <p className="text-gray-600 mb-4">
                    No applications are currently assigned to you for document verification.
                  </p>
                  <Button onClick={loadApplications} variant="outline">
                    Refresh
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {documentVerificationApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold">{app.firstName} {app.lastName}</span>
                          <span className="text-sm text-gray-500">#{app.applicationNumber}</span>
                        </div>
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                          Document Verification Needed
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>Position: {app.position}</span>
                        </div>
                        {app.permanentAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span>{app.permanentAddress.substring(0, 30)}...</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(app.submittedDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3 flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleViewDetails(app.id)}
                        >
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          View Documents
                        </Button>
                        <Button size="sm" onClick={() => handleScheduleAppointment(app)}>
                          Schedule Appointment
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  <div className="text-center pt-4">
                    <Button onClick={loadApplications} variant="outline">
                      Refresh Applications
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Junior Engineer Review Tab */}
        <TabsContent value="junior-engineer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Applications for Junior Engineer Review
              </CardTitle>
              <CardDescription>
                Applications with scheduled appointments awaiting your document verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading applications...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Applications</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadApplications} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : juniorEngineerApps.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications for Review</h3>
                  <p className="text-gray-600 mb-4">
                    No applications are currently awaiting your review.
                  </p>
                  <Button onClick={loadApplications} variant="outline">
                    Refresh
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {juniorEngineerApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold">{app.firstName} {app.lastName}</span>
                          <span className="text-sm text-gray-500">#{app.applicationNumber}</span>
                        </div>
                        <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                          Review Pending
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>Position: {app.position}</span>
                        </div>
                        {app.permanentAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span>{app.permanentAddress.substring(0, 30)}...</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(app.submittedDate).toLocaleDateString()}</span>
                        </div>
                      </div>

                      {/* Show appointment details */}
                      {app.reviewDate && (
                        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <h4 className="font-semibold text-blue-800 mb-2">Appointment Scheduled</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm text-blue-700">
                            <div>Date: {app.reviewDate}</div>
                            <div>Contact: {app.contactPerson}</div>
                            <div>Place: {app.place}</div>
                            <div>Room: {app.roomNumber}</div>
                          </div>
                        </div>
                      )}
                      
                      <div className="mt-3 flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleViewDetails(app.id)}
                        >
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          View Documents
                        </Button>
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => handleCompleteVerification(app.id)}
                        >
                          Document Verified
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  <div className="text-center pt-4">
                    <Button onClick={loadApplications} variant="outline">
                      Refresh Applications
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Schedule Appointment Modal */}
      {showScheduleModal && selectedApplication && (
        <ScheduleAppointmentModal
          isOpen={showScheduleModal}
          onClose={() => {
            setShowScheduleModal(false)
            setSelectedApplication(null)
          }}
          application={selectedApplication}
          onScheduled={handleAppointmentScheduled}
        />
      )}

      {/* Application Details Modal */}
      <ApplicationDetailsModal
        applicationId={selectedApplicationId}
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false)
          setSelectedApplicationId(null)
        }}
      />
    </div>
  )
}

export default JuniorEngineerDashboard
