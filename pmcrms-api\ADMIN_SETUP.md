# PMCRMS Admin Setup Guide

## Setting Up Admin Credentials

There are two ways to set up admin credentials in the PMCRMS system:

### Method 1: Automatic Seeding (When Database is Available)

When you run the application with a working PostgreSQL database:

1. **Update Connection String** in `appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Database=PmcrmsDb;Username=postgres;Password=your_password"
     }
   }
   ```

2. **Start the Application**:
   ```bash
   cd pmcrms-api.API
   dotnet run
   ```

3. **Default Admin Credentials** will be automatically created:
   - **Email**: `<EMAIL>`
   - **Password**: `Admin@123!`

4. **⚠️ IMPORTANT**: Change the default password immediately after first login!

### Method 2: Manual Setup via API (Recommended)

If you prefer manual control or the database isn't available initially:

1. **Start the Application** (works without database):
   ```bash
   cd pmcrms-api.API
   dotnet run
   ```

2. **Access Swagger UI**: Navigate to `http://localhost:5048/swagger`

3. **Check Setup Status**:
   ```
   GET /api/AdminSetup/status
   ```

4. **Initialize Admin User**:
   ```
   POST /api/AdminSetup/initialize
   ```
   This will:
   - Create the database and tables if they don't exist
   - Create all role types (Admin, Officer, User, etc.)
   - Create default admin user with credentials:
     - Email: `<EMAIL>`
     - Password: `Admin@123!`

### Step 3: Login and Change Password

1. **Login via API**:
   ```
   POST /api/Auth/login
   {
     "email": "<EMAIL>",
     "password": "Admin@123!"
   }
   ```

2. **Copy the JWT Token** from the response

3. **Authorize in Swagger**: Click "Authorize" button and enter: `Bearer YOUR_TOKEN_HERE`

4. **Change Password**:
   ```
   POST /api/AdminSetup/change-password
   {
     "currentPassword": "Admin@123!",
     "newPassword": "YourSecurePassword123!",
     "confirmPassword": "YourSecurePassword123!"
   }
   ```

## Password Requirements

New passwords must:
- Be at least 8 characters long
- Contain at least one uppercase letter (A-Z)
- Contain at least one lowercase letter (a-z)
- Contain at least one number (0-9)
- Contain at least one special character (@$!%*?&)

## Example Strong Passwords

- `MySecure123!`
- `Admin2024@Safe`
- `Pmcrms$2025Strong`

## Security Best Practices

1. **Change Default Password**: Always change the default `Admin@123!` password
2. **Use Strong Passwords**: Follow the password complexity requirements
3. **Regular Updates**: Change admin password periodically
4. **Secure Storage**: Never store passwords in plain text
5. **Access Control**: Only give admin access to trusted personnel

## API Endpoints Summary

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/AdminSetup/status` | GET | Check setup status | No |
| `/api/AdminSetup/initialize` | POST | Create admin user | No |
| `/api/AdminSetup/change-password` | POST | Change admin password | Yes (Admin) |
| `/api/Auth/login` | POST | Login with credentials | No |

## Troubleshooting

### Database Connection Issues
- Ensure PostgreSQL is running
- Check connection string in `appsettings.json`
- Verify database credentials

### Admin User Already Exists
- Use the login endpoint with existing credentials
- Use the change password endpoint if you know current password
- Contact system administrator if password is forgotten

### Permission Issues
- Ensure you're using the Admin role
- Check JWT token is valid and not expired
- Verify authorization header format: `Bearer YOUR_TOKEN`

## Next Steps

After setting up admin credentials:

1. **Create Additional Users**: Use the Admin endpoints to create officers and users
2. **Configure System Settings**: Update email, payment gateway settings
3. **Test API Endpoints**: Verify all functionality works as expected
4. **Set Up Frontend**: Configure your frontend application to use these APIs

## Support

For additional help:
- Check application logs for detailed error messages
- Use Swagger UI at `http://localhost:5048/swagger` for API testing
- Refer to the comprehensive API documentation
