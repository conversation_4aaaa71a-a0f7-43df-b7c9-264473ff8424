using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using pmcrms_api.Application.DTOs;
using pmcrms_api.Application.Interfaces;
using System.Reflection;
using Microsoft.Extensions.Logging;

namespace pmcrms_api.Infrastructure.Services
{
    public class RecommendationFormService : IRecommendationFormService
    {
        private readonly ILogger<RecommendationFormService> _logger;
        private readonly byte[] _logo;

        static RecommendationFormService()
        {
            // Register fonts - handle gracefully if not found
            try
            {
                var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("pmcrms_api.Infrastructure.Fonts.times.ttf");
                if (timesFont != null)
                {
                    FontManager.RegisterFont(timesFont);
                }

                var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("pmcrms_api.Infrastructure.Fonts.Mangal.ttf");
                if (mangalFont != null)
                {
                    FontManager.RegisterFont(mangalFont);
                }
            }
            catch (Exception)
            {
                // Font registration failed - will use system default fonts
            }
        }

        public RecommendationFormService(ILogger<RecommendationFormService> logger)
        {
            _logger = logger;
            
            // Initialize with default logo or load from configuration
            try
            {
                var logoStream = Assembly.GetExecutingAssembly().GetManifestResourceStream("pmcrms_api.Infrastructure.Fonts.PMCLogo.png");
                if (logoStream != null)
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        logoStream.CopyTo(memoryStream);
                        _logo = memoryStream.ToArray();
                    }
                }
                else
                {
                    _logo = new byte[0]; // Empty array if logo not found
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not load logo for recommendation form");
                _logo = new byte[0];
            }
        }

        public async Task<byte[]> GenerateRecommendationFormAsync(RecommendationFormModel model)
        {
            try
            {
                var document = new RecommendationFormDocument(model, _logo);
                return await Task.Run(() => document.GeneratePdf());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating recommendation form for {Name}", model.Name);
                throw;
            }
        }
    }

    public class RecommendationFormDocument : IDocument
    {
        private readonly RecommendationFormModel _model;
        private readonly byte[] _logo;

        public RecommendationFormDocument(RecommendationFormModel model, byte[] logo)
        {
            _model = model;
            _logo = logo;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Size(PageSizes.Legal);
                    page.Background()
                        .Padding(20)
                        .Border(1);
                    page.Margin(1, Unit.Inch);
                    page.Margin(30);

                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                });
        }

        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                column.Spacing(10);

                column.Item().Row(row =>
                {
                    // First column: Text aligned to the left
                    row.RelativeItem()
                        .AlignLeft()
                        .Text("मा. शहर अभियंता\nपुणे महानगरपालिका")
                        .Bold()
                        .FontFamily("Mangal")
                        .FontSize(12)
                        .LineHeight(0.8f);

                    // Second column: Empty to center the logo properly
                    row.RelativeItem();
                });

                // New User Info Section
                column.Item()
                    .PaddingLeft(20)
                    .PaddingRight(20)
                    .AlignLeft()
                    .Text(text =>
                    {
                        text.Span($"                   यांजकडे सादर....")
                            .FontSize(14)
                            .FontFamily("Mangal");
                        
                        DateTime date = _model.Date;
                        int year = date.Year;
                        int toYear = year + 2;
                        
                        text.Span($"\n    विषय:- जानेवारी {year} ते डिसेंबर {toYear} करीता {_model.Position} नवीन परवान्याबाबत.")
                            .FontSize(12)
                            .FontFamily("Mangal");
                        
                        text.Span($"\n    विषयांकित प्रकरणी खाली निर्देशित व्यक्तीने जानेवारी {year} ते डिसेंबर {toYear} या कालावधीकरीता पुणे महानगरपालिकेच्या मा. शहर अभियंता कार्यालयाकडे {_model.Position} (नवीन) परवान्याकरिता  अर्ज केला आहे.\n")
                            .FontSize(12)
                            .FontFamily("Mangal")
                            .LineHeight(0.8f);

                        text.Span($"          अर्जदाराचे नाव - ")
                            .FontSize(12)
                            .FontFamily("Mangal")
                            .LineHeight(0.8f);

                        text.Span(_model.Name)
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold()
                            .LineHeight(0.8f);

                        text.Span($"\n          अर्जदाराचे शिक्षण - ")
                            .FontSize(12)
                            .FontFamily("Mangal")
                            .LineHeight(0.8f);

                        text.Span($"1) {_model.Qualification[0]}")
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold()
                            .LineHeight(0.8f);

                        if (_model.Qualification.Count > 1 && !string.IsNullOrWhiteSpace(_model.Qualification[1]))
                        {
                            text.Span($"\n                                                     2) {_model.Qualification[1]}")
                                .FontSize(12)
                                .FontFamily("Times New Roman")
                                .Bold()
                                .LineHeight(0.8f);
                        }
                        else
                        {
                            text.Span($"\n")
                                .FontSize(12)
                                .FontFamily("Times New Roman")
                                .Bold();
                        }

                        text.Span($"\n          पत्ता : ")
                            .FontSize(12)
                            .FontFamily("Mangal");

                        text.Span($"1) {_model.Address1}")
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold();
                        
                        if (!_model.IsBothAddressSame)
                        {
                            text.Span($"\n                                2) {_model.Address2}")
                                .FontSize(12)
                                .FontFamily("Times New Roman")
                                .Bold();
                        }
                        else
                        {
                            text.Span($"\n ")
                                .FontSize(12)
                                .FontFamily("Times New Roman")
                                .Bold();
                        }

                        text.Span($"\n          मोबाईलनं.- ")
                            .FontSize(12)
                            .FontFamily("Mangal");
                        
                        text.Span(_model.MobileNumber)
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold()
                            .LineHeight(0.8f);

                        text.Span("\n          आवश्यक अनुभव - २ वर्षे (युडीसीपीआर २०२० मधील अपेंडिक्स 'सी', सी-४.१")
                            .FontFamily("Mangal")
                            .FontSize(12)
                            .LineHeight(0.8f);

                        text.Span("(ii)")
                            .FontFamily("Times New Roman")
                            .FontSize(12);
                        
                        text.Span(" नुसार)")
                            .FontFamily("Mangal")
                            .FontSize(12)
                            .LineHeight(0.8f);

                        text.Span($"\n          अनुभव- ")
                            .FontSize(12)
                            .FontFamily("Mangal")
                            .LineHeight(0.8f);

                        text.Span(_model.YearDifference ?? "0")
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold();

                        text.Span($" वर्षे ")
                            .FontSize(12)
                            .FontFamily("Mangal");

                        text.Span(_model.MonthDifference ?? "0")
                            .FontSize(12)
                            .FontFamily("Times New Roman")
                            .Bold();

                        text.Span($" महिने")
                            .FontSize(12)
                            .FontFamily("Mangal");
                    });
            });
        }

        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().PaddingTop(5).PaddingLeft(20).PaddingRight(20).Text(text =>
                {
                    var num = "";

                    switch (_model.Position)
                    {
                        case "स्ट्रक्चरल इंजिनिअर":
                            num = "4";
                            break;
                        case "लायसन्स इंजिनिअर":
                            num = "3";
                            break;
                        case "सुपरवायझर1":
                            num = "5.1";
                            break;
                        case "सुपरवायझर2":
                            num = "5.1";
                            break;
                    }

                    text.Line($"    उपरोक्त नमूद केलेल्या व्यक्तीचा मागणी अर्ज, शैक्षणिक पात्रता, अनुभव व पत्त्याचा पुरावा इ. कागदपत्राची तपासणी केली ती बरोबर व नियमानुसार आहेत. त्यानुसार वरील अर्जदाराची मान्य युडीसीपीआर २०२० मधील अपेंडिक्स सी, सी-{num} नुसार पुणे महानगरपालिकेच्या {_model.Position} (नवीन) परवाना धारण करण्यास आवश्यक शैक्षणिक पात्रता व अनुभव असल्याने त्यांचा अर्ज आपले मान्यतेकरिता सादर करीत आहोत.")
                        .FontFamily("Mangal")
                        .FontSize(12)
                        .LineHeight(0.8f);

                    DateTime date = _model.Date;
                    int year = date.Year;
                    int toYear = year + 2;

                    text.Span("     तरी सदर प्रकरणी ")
                        .FontFamily("Mangal")
                        .FontSize(12)
                        .LineHeight(0.8f);

                    text.Span(_model.Name + " ")
                        .FontSize(12)
                        .FontFamily("Times New Roman")
                        .Bold()
                        .LineHeight(0.8f);

                    text.Span($" यांचेकडून जानेवारी {year} ते डिसेंबर {toYear} या कालावधी करिता आवश्यक ती फी भरून घेवून {_model.Position} (नवीन) परवाना देणेबाबत मान्यता मिळणेस विनंती आहे.")
                        .FontFamily("Mangal")
                        .FontSize(12)
                        .LineHeight(0.8f);
                });

                column.Item().PaddingLeft(20).PaddingRight(20).Text(text =>
                {
                    text.Line("मा.स.कळावे.")
                        .FontFamily("Mangal")
                        .FontSize(12)
                        .LineHeight(0.8f);
                });

                // Fixed-position signature/footer section
                column.Item().Extend().AlignBottom().Column(column =>
                {
                    column.Item().PaddingLeft(20).PaddingRight(20).Column(column2 =>
                    {
                        column2.Item().PaddingTop(80).Row(row =>
                        {
                            row.RelativeItem(1).Column(col =>
                            {
                                col.Item()
                                    .PaddingTop(10)
                                    .Height(60);

                                col.Item()
                                    .AlignCenter()
                                    .Text(text =>
                                    {
                                        text.Line($"({_model.JrEnggName})")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("शाखा अभियंता")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("शहर-अभियंता कार्यालय")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("पुणे महानगरपालिका")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                    });
                            });

                            row.RelativeItem(1).Column(col =>
                            {
                                col.Item()
                                    .PaddingTop(20)
                                    .Height(60);

                                col.Item()
                                    .AlignCenter()
                                    .Text(text =>
                                    {
                                        text.Line($"({_model.AssEnggName})")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("उपअभियंता")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("पुणे महानगरपालिका")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                    });
                            });
                        });

                        column2.Item()
                            .AlignLeft()
                            .Text(text =>
                            {
                                text.Line("प्रस्तुत प्रकरणी उपरोक्त प्रमाणे छाननी झाली असल्याने मान्यतेस शिफारस आहे.")
                                    .FontFamily("Mangal")
                                    .FontSize(12)
                                    .LineHeight(0.8f);
                            });
                        
                        column2.Item()
                            .AlignRight()
                            .PaddingRight(110)
                            .PaddingTop(30)
                            .PaddingBottom(0)
                            .Text(text =>
                            {
                                text.Line("क्ष मान्य")
                                    .FontFamily("Mangal")
                                    .FontSize(12)
                                    .LineHeight(0.2f);
                            });
                        
                        column2.Item().Row(row =>
                        {
                            row.RelativeItem(1).Column(col =>
                            {
                                col.Item()
                                    .PaddingTop(10)
                                    .Height(60);

                                col.Item()
                                    .AlignCenter()
                                    .Text(text =>
                                    {
                                        text.Line($"({_model.ExeEnggName})")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("कार्यकारी अभियंता")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("पुणे महानगरपालिका")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                    });
                            });

                            row.RelativeItem(1).Column(col =>
                            {
                                col.Item()
                                    .PaddingTop(10)
                                    .Height(60);

                                col.Item()
                                    .AlignCenter()
                                    .Text(text =>
                                    {
                                        text.Line($"({_model.CityEnggName})")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("शहर अभियंता ")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                        text.Line("पुणे महानगरपालिका")
                                            .FontFamily("Mangal")
                                            .FontSize(12)
                                            .LineHeight(0.8f);
                                    });
                            });
                        });
                    });
                });
            });
        }
    }
}
