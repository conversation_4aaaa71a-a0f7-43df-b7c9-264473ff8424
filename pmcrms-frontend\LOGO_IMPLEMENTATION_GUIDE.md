# Custom Logo Implementation Guide

## 📍 Logo Locations in PMCRMS Frontend

### 1. **Logo Component** (`src/components/Logo.tsx`)
- **Main logo component** with customizable props
- Supports different sizes: `sm`, `md`, `lg`, `xl`
- Variants: `light`, `dark`, `color`
- Option to show/hide text
- Built-in SVG logo (customize with your design)

### 2. **Where Logo is Currently Used**
- ✅ **TopBar** (`src/components/TopBar.tsx`) - Dashboard header
- ✅ **Landing Page** (`src/pages/LandingPage.tsx`) - Main header
- ✅ **Guest Login** (`src/pages/auth/GuestLogin.tsx`) - Authentication pages
- 🔄 **Sidebar** - Can be added for navigation
- 🔄 **Other Auth Pages** - Login, Register pages

## 🎨 **How to Replace with Your Logo**

### Option 1: Image File (Recommended)
```tsx
// 1. Add your logo files to public folder:
// - public/logo.png (main logo)
// - public/logo-light.png (for dark backgrounds)
// - public/favicon.ico (browser tab icon)

// 2. Update Logo.tsx to use image:
<img 
  src="/logo.png" 
  alt="PMCRMS Logo" 
  className={getSizeClasses(size)}
/>
```

### Option 2: Custom SVG
```tsx
// Update the LogoIcon component in src/components/Logo.tsx
const LogoIcon = () => (
  <svg className={`${getSizeClasses(size)} ${className}`}>
    {/* Your custom SVG paths here */}
  </svg>
)
```

## 📁 **Recommended File Structure**
```
public/
├── logo.png                 # Main logo (PNG/SVG)
├── logo-light.png          # Light variant for dark backgrounds
├── logo-icon.png           # Icon only version
└── favicon.ico             # Browser tab icon

src/assets/images/
├── logo-variants/
│   ├── logo-full.svg       # Full logo with text
│   ├── logo-icon.svg       # Icon only
│   └── logo-horizontal.svg # Horizontal layout
```

## 🎯 **Logo Usage Examples**

### TopBar (Dashboard Header)
```tsx
<Logo size="md" showText={true} variant="color" />
```

### Landing Page Hero
```tsx
<Logo size="xl" showText={false} className="mr-4" />
```

### Authentication Pages
```tsx
<Logo size="lg" showText={false} />
```

### Sidebar/Navigation
```tsx
<Logo size="sm" showText={true} variant="light" />
```

## 🔧 **Customization Options**

### Props Available
- `size`: `'sm' | 'md' | 'lg' | 'xl'`
- `showText`: `boolean` (show/hide "PMCRMS" text)
- `variant`: `'light' | 'dark' | 'color'`
- `className`: Additional CSS classes

### Size Guidelines
- **sm**: 24px (sidebar, compact spaces)
- **md**: 32px (topbar, forms)
- **lg**: 40px (page headers)
- **xl**: 48px (hero sections)

## 🎨 **Brand Colors**
Current theme uses:
- Primary: `#1e40af` (Blue)
- Text: `text-primary`, `text-gray-900`
- Backgrounds: `bg-white`, `bg-gray-50`

## 📱 **Responsive Behavior**
- Logo automatically scales on mobile
- Text can be hidden on small screens
- Icon-only version for compact layouts

## 🚀 **Next Steps**
1. **Add your logo files** to `public/` folder
2. **Update Logo.tsx** to use your image/SVG
3. **Test on all pages** where logo appears
4. **Update favicon** in `public/favicon.ico`
5. **Add to other auth pages** if needed

## ⚡ **Quick Start**
To immediately use your logo:
1. Place `logo.png` in `public/` folder
2. Uncomment the image code in `Logo.tsx`
3. Comment out the SVG code
4. Build and test!
