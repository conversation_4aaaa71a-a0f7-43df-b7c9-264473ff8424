// Workflow History Analytics Cube
// This cube tracks workflow state transitions and timing

cube(`WorkflowHistory`, {
  sql: `
    SELECT 
      wh.Id,
      wh.<PERSON>Id,
      a.<PERSON>N<PERSON>ber,
      CONCAT(a.FirstName, ' ', a.LastName) as Applicant<PERSON><PERSON>,
      a.<PERSON>sition,
      wh.FromStatus,
      wh.ToStatus,
      wh.Changed<PERSON>y,
      wh.ChangedByUserId,
      CONCAT(u.FirstName, ' ', u.LastName) as Changed<PERSON><PERSON><PERSON><PERSON>,
      wh.ChangedDate,
      wh.<PERSON><PERSON>,
      wh.IsAutomaticTransition,
      -- Calculate time spent in previous stage
      LAG(wh.ChangedDate) OVER (PARTITION BY wh.ApplicationId ORDER BY wh.ChangedDate) as PreviousStageDate,
      CASE 
        WHEN LAG(wh.ChangedDate) OVER (PARTITION BY wh.ApplicationId ORDER BY wh.ChangedDate) IS NOT NULL 
        THEN DATEDIFF(day, LAG(wh.ChangedDate) OVER (PARTITION BY wh.ApplicationId ORDER BY wh.ChangedDate), wh.ChangedDate)
        ELSE 0
      END as DaysInPreviousStage,
      -- Status mappings
      CASE wh.FromStatus
        WHEN 1 THEN 'DOCUMENT_VERIFICATION_PENDING'
        WHEN 2 THEN 'JUNIOR_ENGINEER_PENDING'
        WHEN 3 THEN 'ASSISTANT_ENGINEER_PENDING'
        WHEN 4 THEN 'EXECUTIVE_ENGINEER_PENDING'
        WHEN 5 THEN 'FINAL_APPROVE'
        WHEN 6 THEN 'CITY_ENGINEER_PENDING'
        WHEN 7 THEN 'PAYMENT_PENDING'
        WHEN 8 THEN 'REJECTED'
        ELSE 'UNKNOWN'
      END as FromStatusName,
      CASE wh.ToStatus
        WHEN 1 THEN 'DOCUMENT_VERIFICATION_PENDING'
        WHEN 2 THEN 'JUNIOR_ENGINEER_PENDING'
        WHEN 3 THEN 'ASSISTANT_ENGINEER_PENDING'
        WHEN 4 THEN 'EXECUTIVE_ENGINEER_PENDING'
        WHEN 5 THEN 'FINAL_APPROVE'
        WHEN 6 THEN 'CITY_ENGINEER_PENDING'
        WHEN 7 THEN 'PAYMENT_PENDING'
        WHEN 8 THEN 'REJECTED'
        ELSE 'UNKNOWN'
      END as ToStatusName,
      -- Position mapping
      CASE a.Position
        WHEN 1 THEN 'Architect'
        WHEN 2 THEN 'License Engineer'
        WHEN 3 THEN 'Structural Engineer'
        WHEN 4 THEN 'Supervisor1'
        WHEN 5 THEN 'Supervisor2'
        ELSE 'Unknown'
      END as PositionName,
      -- Time periods
      YEAR(wh.ChangedDate) as TransitionYear,
      MONTH(wh.ChangedDate) as TransitionMonth,
      DATEPART(quarter, wh.ChangedDate) as TransitionQuarter,
      DATEPART(weekday, wh.ChangedDate) as TransitionDayOfWeek,
      DATEPART(hour, wh.ChangedDate) as TransitionHour
    FROM ApplicationWorkflowHistory wh
    LEFT JOIN Applications a ON wh.ApplicationId = a.Id
    LEFT JOIN Users u ON wh.ChangedByUserId = u.Id
  `,

  measures: {
    transitionCount: {
      type: `count`,
      title: `Total Transitions`,
      description: `Total number of workflow state transitions`
    },

    uniqueApplications: {
      type: `countDistinct`,
      sql: `ApplicationId`,
      title: `Unique Applications`,
      description: `Number of unique applications with transitions`
    },

    averageStageTime: {
      type: `avg`,
      sql: `DaysInPreviousStage`,
      title: `Average Stage Time (Days)`,
      description: `Average time spent in each workflow stage`
    },

    totalStageTime: {
      type: `sum`,
      sql: `DaysInPreviousStage`,
      title: `Total Stage Time (Days)`,
      description: `Total time spent across all stages`
    },

    fastTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.DaysInPreviousStage <= 3` }
      ],
      title: `Fast Transitions (≤3 days)`,
      description: `Transitions completed within 3 days`
    },

    slowTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.DaysInPreviousStage > 14` }
      ],
      title: `Slow Transitions (>14 days)`,
      description: `Transitions taking more than 14 days`
    },

    automaticTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.IsAutomaticTransition = 1` }
      ],
      title: `Automatic Transitions`,
      description: `System-generated transitions`
    },

    manualTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.IsAutomaticTransition = 0` }
      ],
      title: `Manual Transitions`,
      description: `User-initiated transitions`
    },

    approvalTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.ToStatus = 5` } // FINAL_APPROVE
      ],
      title: `Approval Transitions`,
      description: `Transitions to approved status`
    },

    rejectionTransitions: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.ToStatus = 8` } // REJECTED
      ],
      title: `Rejection Transitions`,
      description: `Transitions to rejected status`
    },

    stageEfficiency: {
      type: `number`,
      sql: `
        CASE 
          WHEN COUNT(*) > 0 THEN 
            (COUNT(CASE WHEN ${CUBE}.DaysInPreviousStage <= 7 THEN 1 END) * 100.0 / COUNT(*))
          ELSE 0 
        END
      `,
      title: `Stage Efficiency (%)`,
      description: `Percentage of transitions completed within 7 days`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `number`,
      primaryKey: true,
      shown: false
    },

    applicationId: {
      sql: `ApplicationId`,
      type: `number`,
      title: `Application ID`,
      description: `ID of the application`
    },

    applicationNumber: {
      sql: `ApplicationNumber`,
      type: `string`,
      title: `Application Number`,
      description: `Application number reference`
    },

    applicantName: {
      sql: `ApplicantName`,
      type: `string`,
      title: `Applicant Name`,
      description: `Name of the applicant`
    },

    positionName: {
      sql: `PositionName`,
      type: `string`,
      title: `Position`,
      description: `Position applied for`
    },

    fromStatusName: {
      sql: `FromStatusName`,
      type: `string`,
      title: `From Status`,
      description: `Previous workflow status`
    },

    toStatusName: {
      sql: `ToStatusName`,
      type: `string`,
      title: `To Status`,
      description: `New workflow status`
    },

    transitionType: {
      sql: `
        CASE 
          WHEN FromStatusName IS NULL THEN 'Initial Submission'
          WHEN ToStatusName = 'FINAL_APPROVE' THEN 'Approval'
          WHEN ToStatusName = 'REJECTED' THEN 'Rejection'
          ELSE 'Progress'
        END
      `,
      type: `string`,
      title: `Transition Type`,
      description: `Type of workflow transition`
    },

    changedBy: {
      sql: `ChangedBy`,
      type: `string`,
      title: `Changed By`,
      description: `User who made the transition`
    },

    changedByUser: {
      sql: `ChangedByUser`,
      type: `string`,
      title: `Changed By User`,
      description: `Full name of user who made transition`
    },

    isAutomaticTransition: {
      sql: `
        CASE IsAutomaticTransition
          WHEN 1 THEN 'Automatic'
          ELSE 'Manual'
        END
      `,
      type: `string`,
      title: `Transition Method`,
      description: `Whether transition was automatic or manual`
    },

    changedDate: {
      sql: `ChangedDate`,
      type: `time`,
      title: `Transition Date`,
      description: `Date and time of transition`
    },

    transitionYear: {
      sql: `TransitionYear`,
      type: `number`,
      title: `Transition Year`,
      description: `Year when transition occurred`
    },

    transitionMonth: {
      sql: `TransitionMonth`,
      type: `number`,
      title: `Transition Month`,
      description: `Month when transition occurred`
    },

    transitionQuarter: {
      sql: `TransitionQuarter`,
      type: `number`,
      title: `Transition Quarter`,
      description: `Quarter when transition occurred`
    },

    transitionDayOfWeek: {
      sql: `
        CASE TransitionDayOfWeek
          WHEN 1 THEN 'Sunday'
          WHEN 2 THEN 'Monday'
          WHEN 3 THEN 'Tuesday'
          WHEN 4 THEN 'Wednesday'
          WHEN 5 THEN 'Thursday'
          WHEN 6 THEN 'Friday'
          WHEN 7 THEN 'Saturday'
        END
      `,
      type: `string`,
      title: `Day of Week`,
      description: `Day of week when transition occurred`
    },

    transitionHour: {
      sql: `TransitionHour`,
      type: `number`,
      title: `Hour of Day`,
      description: `Hour when transition occurred (0-23)`
    },

    stageTimeCategory: {
      sql: `
        CASE 
          WHEN DaysInPreviousStage <= 1 THEN '0-1 days'
          WHEN DaysInPreviousStage <= 3 THEN '2-3 days'
          WHEN DaysInPreviousStage <= 7 THEN '4-7 days'
          WHEN DaysInPreviousStage <= 14 THEN '8-14 days'
          WHEN DaysInPreviousStage <= 30 THEN '15-30 days'
          ELSE '30+ days'
        END
      `,
      type: `string`,
      title: `Stage Time Category`,
      description: `Categorized time spent in stage`
    },

    workflowStage: {
      sql: `
        CASE ToStatusName
          WHEN 'DOCUMENT_VERIFICATION_PENDING' THEN 'Stage 1: Document Verification'
          WHEN 'JUNIOR_ENGINEER_PENDING' THEN 'Stage 2: Junior Engineer Review'
          WHEN 'ASSISTANT_ENGINEER_PENDING' THEN 'Stage 3: Assistant Engineer Review'
          WHEN 'EXECUTIVE_ENGINEER_PENDING' THEN 'Stage 4: Executive Engineer Review'
          WHEN 'CITY_ENGINEER_PENDING' THEN 'Stage 5: City Engineer Review'
          WHEN 'PAYMENT_PENDING' THEN 'Stage 6: Payment Processing'
          WHEN 'FINAL_APPROVE' THEN 'Stage 7: Final Approval'
          WHEN 'REJECTED' THEN 'Rejected'
          ELSE 'Unknown Stage'
        END
      `,
      type: `string`,
      title: `Workflow Stage`,
      description: `Descriptive workflow stage name`
    }
  },

  segments: {
    recentTransitions: {
      sql: `${CUBE}.changedDate >= DATEADD(month, -3, GETUTCDATE())`,
      title: `Recent Transitions`,
      description: `Transitions from last 3 months`
    },

    businessHours: {
      sql: `${CUBE}.TransitionHour BETWEEN 9 AND 17`,
      title: `Business Hours`,
      description: `Transitions during business hours (9 AM - 5 PM)`
    },

    weekdays: {
      sql: `${CUBE}.TransitionDayOfWeek BETWEEN 2 AND 6`,
      title: `Weekdays`,
      description: `Transitions on weekdays (Monday-Friday)`
    },

    approvalPath: {
      sql: `${CUBE}.ToStatusName = 'FINAL_APPROVE'`,
      title: `Approval Path`,
      description: `Transitions leading to approval`
    },

    rejectionPath: {
      sql: `${CUBE}.ToStatusName = 'REJECTED'`,
      title: `Rejection Path`,
      description: `Transitions leading to rejection`
    },

    engineerStages: {
      sql: `${CUBE}.ToStatusName LIKE '%ENGINEER%'`,
      title: `Engineer Stages`,
      description: `Transitions to engineer review stages`
    }
  },

  preAggregations: {
    // Main workflow analytics
    main: {
      measures: [
        CUBE.transitionCount,
        CUBE.averageStageTime,
        CUBE.stageEfficiency
      ],
      dimensions: [
        CUBE.fromStatusName,
        CUBE.toStatusName,
        CUBE.transitionType,
        CUBE.workflowStage
      ],
      timeDimension: CUBE.changedDate,
      granularity: `day`,
      partitionGranularity: `month`,
      refreshKey: {
        every: `30 minutes`
      }
    },

    // Stage performance analysis
    stagePerformance: {
      measures: [
        CUBE.transitionCount,
        CUBE.averageStageTime,
        CUBE.fastTransitions,
        CUBE.slowTransitions
      ],
      dimensions: [
        CUBE.fromStatusName,
        CUBE.toStatusName,
        CUBE.stageTimeCategory
      ],
      refreshKey: {
        every: `1 hour`
      }
    },

    // User performance tracking
    userPerformance: {
      measures: [
        CUBE.transitionCount,
        CUBE.averageStageTime
      ],
      dimensions: [
        CUBE.changedByUser,
        CUBE.toStatusName,
        CUBE.isAutomaticTransition
      ],
      refreshKey: {
        every: `1 hour`
      }
    },

    // Time-based patterns
    timePatterns: {
      measures: [CUBE.transitionCount],
      dimensions: [
        CUBE.transitionDayOfWeek,
        CUBE.transitionHour,
        CUBE.transitionType
      ],
      timeDimension: CUBE.changedDate,
      granularity: `month`,
      refreshKey: {
        every: `2 hours`
      }
    },

    // Position-wise workflow analysis
    positionWorkflow: {
      measures: [
        CUBE.transitionCount,
        CUBE.averageStageTime,
        CUBE.approvalTransitions,
        CUBE.rejectionTransitions
      ],
      dimensions: [
        CUBE.positionName,
        CUBE.workflowStage,
        CUBE.transitionType
      ],
      refreshKey: {
        every: `1 hour`
      }
    }
  }
});
