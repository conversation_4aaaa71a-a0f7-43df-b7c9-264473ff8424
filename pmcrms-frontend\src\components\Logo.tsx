import React from 'react'
import pmcLogo from '../assets/images/pmc_logo.png'

interface LogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showText?: boolean
  variant?: 'light' | 'dark' | 'color'
}

const Logo: React.FC<LogoProps> = ({ 
  className = '', 
  size = 'md', 
  showText = true,
  variant = 'color'
}) => {
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm': return 'h-8'
      case 'md': return 'h-10'
      case 'lg': return 'h-12'
      case 'xl': return 'h-16'
      default: return 'h-10'
    }
  }

  const getTextClasses = (size: string) => {
    switch (size) {
      case 'sm': return 'text-lg'
      case 'md': return 'text-xl'
      case 'lg': return 'text-2xl'
      case 'xl': return 'text-3xl'
      default: return 'text-xl'
    }
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Custom Logo Image */}
      <img 
        src={pmcLogo} 
        alt="PMC Logo" 
        className={`${getSizeClasses(size)} w-auto object-contain`}
      />
      
      {/* Logo Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold ${getTextClasses(size)} ${
            variant === 'light' ? 'text-white' : 
            variant === 'dark' ? 'text-gray-900' : 
            'text-primary'
          }`}>
            PMCRMS
          </span>
          <span className="text-xs text-gray-500 -mt-1">
            Professional Registration
          </span>
        </div>
      )}
    </div>
  )
}

export default Logo
