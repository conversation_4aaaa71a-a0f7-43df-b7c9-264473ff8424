using pmcrms_api.Domain.Entities;

namespace pmcrms_api.Application.Interfaces
{
    public interface IPaymentService
    {
        Task<(bool success, string? paymentUrl, string? error)> InitiatePaymentAsync(
            Transaction transaction,
            string returnUrl,
            string notifyUrl);

        Task<(bool success, string? error)> HandleCallbackAsync(
            Dictionary<string, string> callbackParams);

        Task<(bool success, string? error)> VerifyPaymentAsync(
            string transactionId);
    }
}
