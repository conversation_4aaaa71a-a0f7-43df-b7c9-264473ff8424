namespace pmcrms_api.Application.Exceptions
{
    public class ValidationException : Exception
    {
        private readonly IDictionary<string, string[]> _errors = new Dictionary<string, string[]>();

        public ValidationException() : base("One or more validation failures have occurred.")
        {
            _errors = new Dictionary<string, string[]>();
        }

        public ValidationException(IDictionary<string, string[]> failures) : base("One or more validation failures have occurred.")
        {
            _errors = failures;
        }

        public ValidationException(IEnumerable<FluentValidation.Results.ValidationFailure> failures)
            : base("One or more validation failures have occurred.")
        {
            _errors = failures
                .GroupBy(e => e.PropertyName, e => e.ErrorMessage)
                .ToDictionary(g => g.Key, g => g.<PERSON>y());
        }

        public IDictionary<string, string[]> Errors => _errors;
    }

    public class NotFoundException : Exception
    {
        public NotFoundException(string name, object key)
            : base($"Entity \"{name}\" ({key}) was not found.")
        {
        }
    }

    public class UnauthorizedException : Exception
    {
        public UnauthorizedException(string message) : base(message)
        {
        }
    }

    public class ForbiddenException : Exception
    {
        public ForbiddenException(string message) : base(message)
        {
        }
    }

    public class BadRequestException : Exception
    {
        public BadRequestException(string message) : base(message)
        {
        }
    }
}
