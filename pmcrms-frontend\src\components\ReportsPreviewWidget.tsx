import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip } from 'recharts';
import { BarChart3Icon, TrendingUpIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data for preview (replace with real API calls)
const positionData = [
  { name: 'Architect', value: 450, color: '#8884d8' },
  { name: 'License Engineer', value: 380, color: '#82ca9d' },
  { name: 'Structural Engineer', value: 200, color: '#ffc658' },
  { name: 'Supervisor1', value: 120, color: '#ff7c7c' },
  { name: 'Supervisor2', value: 100, color: '#8dd1e1' }
];

const statusData = [
  { name: 'Doc Verify', value: 45, color: '#fbbf24' },
  { name: 'Junior Eng', value: 32, color: '#60a5fa' },
  { name: 'Assistant Eng', value: 28, color: '#34d399' },
  { name: 'Executive Eng', value: 15, color: '#a78bfa' },
  { name: 'Payment', value: 12, color: '#fb923c' },
  { name: 'Approved', value: 980, color: '#10b981' }
];

interface ReportsPreviewWidgetProps {
  className?: string;
}

const ReportsPreviewWidget: React.FC<ReportsPreviewWidgetProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <BarChart3Icon className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Analytics Overview</h3>
        </div>
        <Link
          to="/admin/reports"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          View Full Reports
          <TrendingUpIcon className="h-4 w-4 ml-1" />
        </Link>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">1,250</div>
          <div className="text-xs text-gray-600">Total Applications</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">132</div>
          <div className="text-xs text-gray-600">In Progress</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">980</div>
          <div className="text-xs text-gray-600">Approved</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">15.5</div>
          <div className="text-xs text-gray-600">Avg Days</div>
        </div>
      </div>

      {/* Mini Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Position Distribution */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Position Distribution</h4>
          <div className="h-32">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={positionData}
                  cx="50%"
                  cy="50%"
                  innerRadius={20}
                  outerRadius={50}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {positionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [value, 'Applications']}
                  contentStyle={{
                    fontSize: '12px',
                    padding: '8px',
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '6px'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Status Distribution */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Status Distribution</h4>
          <div className="h-32">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={statusData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <XAxis 
                  dataKey="name" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 10 }}
                  angle={-45}
                  textAnchor="end"
                  height={40}
                />
                <YAxis hide />
                <Tooltip 
                  formatter={(value: number) => [value, 'Applications']}
                  contentStyle={{
                    fontSize: '12px',
                    padding: '8px',
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '6px'
                  }}
                />
                <Bar dataKey="value" fill="#3b82f6" radius={[2, 2, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex flex-wrap gap-x-4 gap-y-2 text-xs">
          {positionData.slice(0, 3).map((item, index) => (
            <div key={index} className="flex items-center">
              <div 
                className="w-3 h-3 rounded mr-2" 
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-gray-600">{item.name}: {item.value}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Click charts to drill down into detailed analytics
          </p>
          <Link
            to="/admin/reports"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Open Analytics
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ReportsPreviewWidget;
