import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs'
import { Badge } from '../../components/ui/badge'
import { FileText, CheckCircle, Clock, AlertCircle, User, MapPin, Calendar, Loader2, TrendingUp } from 'lucide-react'
import { useAuthStore } from '../../store'
import api from '../../lib/api'
import ApplicationDetailsModal from '../../components/ApplicationDetailsModal'

interface Application {
  id: number
  applicationNumber: string
  applicantName?: string
  firstName?: string
  lastName?: string
  position: string
  status?: string
  workflowState?: string
  submittedDate: string
  permanentAddress?: string
  phoneNumber?: string
  reviewDate?: string
  contactPerson?: string
  place?: string
  roomNumber?: string
}

const AssistantEngineerDashboard: React.FC = () => {
  const [assistantEngineerPendingApps, setAssistantEngineerPendingApps] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedApplicationId, setSelectedApplicationId] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState('assistant-engineer-pending')
  const { token } = useAuthStore()

  const loadApplications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await api.get('/applications', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      
      console.log('Applications loaded:', response.data)
      
      // Handle the response structure - data is nested inside response.data.data
      const responseData = response.data
      if (responseData.success && Array.isArray(responseData.data)) {
        const allApps = responseData.data.map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState, // Use status field as workflowState
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        
        console.log('Processed applications:', allApps)
        
        // Filter applications by workflow state for Assistant Engineer
        const assistantEngApps = allApps.filter((app: Application) => 
          app.workflowState === 'ASSISTANT_ENGINEER_PENDING'
        )
        
        console.log('Assistant Engineer apps:', assistantEngApps)
        
        setAssistantEngineerPendingApps(assistantEngApps)
      } else {
        const allApps = (responseData || []).map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState,
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        
        const assistantEngApps = allApps.filter((app: Application) => 
          app.workflowState === 'ASSISTANT_ENGINEER_PENDING'
        )
        
        setAssistantEngineerPendingApps(assistantEngApps)
      }
    } catch (err: any) {
      console.error('Failed to load applications:', err)
      setError(err.response?.data?.message || 'Failed to load applications')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (token) {
      loadApplications()
    }
  }, [token])

  const handleViewDetails = (applicationId: number) => {
    console.log('handleViewDetails called with applicationId:', applicationId)
    setSelectedApplicationId(applicationId)
    setShowDetailsModal(true)
  }

  const handleCompleteReview = async (applicationId: number) => {
    try {
      const response = await api.post(`/applications/${applicationId}/complete-assistant-review`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        // Reload applications to show updated status
        loadApplications()
      }
    } catch (err: any) {
      console.error('Failed to complete review:', err)
      setError(err.response?.data?.message || 'Failed to complete review')
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Assistant Engineer Dashboard</h1>
        <p className="text-gray-600">Review and approve applications from Junior Engineers</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assistantEngineerPendingApps.length}</div>
            <p className="text-xs text-muted-foreground">Applications awaiting review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reviewed Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Applications reviewed today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">15</div>
            <p className="text-xs text-muted-foreground">Applications approved this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5</div>
            <p className="text-xs text-muted-foreground">Avg days per application</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information - Remove after testing */}
      <Card className="mb-4">
        <CardContent className="pt-4">
          <div className="text-sm space-y-2">
            <div>Assistant Engineer Pending Apps: {assistantEngineerPendingApps.length}</div>
            <div>
              <strong>Assistant Engineer IDs:</strong> {assistantEngineerPendingApps.map(app => app.id).join(', ')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-1">
          <TabsTrigger value="assistant-engineer-pending" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Assistant Engineer Review ({assistantEngineerPendingApps.length})
          </TabsTrigger>
        </TabsList>

        {/* Assistant Engineer Pending Tab */}
        <TabsContent value="assistant-engineer-pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Applications for Assistant Engineer Review
              </CardTitle>
              <CardDescription>
                Applications verified by Junior Engineers awaiting your final review and approval
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading applications...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Applications</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadApplications} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : assistantEngineerPendingApps.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications for Review</h3>
                  <p className="text-gray-600 mb-4">
                    No applications are currently awaiting your review.
                  </p>
                  <Button onClick={loadApplications} variant="outline">
                    Refresh
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {assistantEngineerPendingApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold">{app.firstName} {app.lastName}</span>
                          <span className="text-sm text-gray-500">#{app.applicationNumber}</span>
                        </div>
                        <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                          Assistant Review Needed
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>Position: {app.position}</span>
                        </div>
                        {app.permanentAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span>{app.permanentAddress.substring(0, 30)}...</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(app.submittedDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3 flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleViewDetails(app.id)}
                        >
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          View Documents
                        </Button>
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => handleCompleteReview(app.id)}
                        >
                          Approve Application
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  <div className="text-center pt-4">
                    <Button onClick={loadApplications} variant="outline">
                      Refresh Applications
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Application Details Modal */}
      <ApplicationDetailsModal
        applicationId={selectedApplicationId}
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false)
          setSelectedApplicationId(null)
        }}
      />
    </div>
  )
}

export default AssistantEngineerDashboard
