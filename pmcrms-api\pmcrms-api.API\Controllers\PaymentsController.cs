using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Application.Services;
using pmcrms_api.Domain.Entities;
using System.Net;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Payment processing and management
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Tags("Payments")]
[Produces("application/json")]
public class PaymentsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICertificateService _certificateService;
    private readonly ILogger<PaymentsController> _logger;

    public PaymentsController(IUnitOfWork unitOfWork, ICertificateService certificateService, ILogger<PaymentsController> logger)
    {
        _unitOfWork = unitOfWork;
        _certificateService = certificateService;
        _logger = logger;
    }

    /// <summary>
    /// Get all payments
    /// </summary>
    /// <returns>List of all payments</returns>
    /// <response code="200">Payments retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [Authorize(Roles = "Admin,Officer")]
    [ProducesResponseType(typeof(IEnumerable<object>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetPayments()
    {
        var payments = await _unitOfWork.Repository<Payment>().GetAllAsync();
        
        return Ok(payments.Select(payment => new
        {
            payment.Id,
            payment.Amount,
            payment.Status,
            payment.PaymentNumber,
            payment.PaymentMethod,
            payment.PaymentDate,
            payment.CreatedAt
        }));
    }

    /// <summary>
    /// Get payment by ID
    /// </summary>
    /// <param name="id">Payment ID</param>
    /// <returns>Payment details</returns>
    /// <response code="200">Payment found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Payment not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetPayment(int id)
    {
        var payment = await _unitOfWork.Repository<Payment>().GetByIdAsync(id);
        
        if (payment == null)
        {
            return NotFound(new { message = "Payment not found" });
        }

        return Ok(new
        {
            payment.Id,
            payment.Amount,
            payment.Status,
            payment.PaymentNumber,
            payment.PaymentMethod,
            payment.PaymentDate,
            payment.CreatedAt
        });
    }

    /// <summary>
    /// Initiate a new payment
    /// </summary>
    /// <param name="request">Payment initiation request</param>
    /// <returns>Payment details with transaction ID</returns>
    /// <response code="201">Payment initiated successfully</response>
    /// <response code="400">Invalid payment data</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("initiate")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.Created)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> InitiatePayment([FromBody] InitiatePaymentRequest request)
    {
        if (request.Amount <= 0)
        {
            return BadRequest(new { message = "Amount must be greater than zero" });
        }

        var payment = new Payment
        {
            Amount = request.Amount,
            Status = "Pending",
            PaymentMethod = request.PaymentMethod,
            PaymentNumber = Guid.NewGuid().ToString(),
            PaymentDate = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            ApplicationId = 1 // TODO: Get from request or authenticated user
        };

        await _unitOfWork.Repository<Payment>().AddAsync(payment);
        await _unitOfWork.SaveChangesAsync();

        return CreatedAtAction(nameof(GetPayment), new { id = payment.Id }, new
        {
            payment.Id,
            payment.Amount,
            payment.Status,
            payment.PaymentNumber,
            payment.PaymentMethod,
            payment.PaymentDate,
            payment.CreatedAt
        });
    }

    /// <summary>
    /// Update payment status
    /// </summary>
    /// <param name="id">Payment ID</param>
    /// <param name="request">Payment status update request</param>
    /// <returns>Success message</returns>
    /// <response code="200">Payment status updated successfully</response>
    /// <response code="400">Invalid status</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Payment not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("{id}/status")]
    [Authorize(Roles = "Admin,Officer")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> UpdatePaymentStatus(int id, [FromBody] UpdatePaymentStatusRequest request)
    {
        var payment = await _unitOfWork.Repository<Payment>().GetByIdAsync(id);
        
        if (payment == null)
        {
            return NotFound(new { message = "Payment not found" });
        }

        var validStatuses = new[] { "Pending", "Completed", "Failed", "Cancelled" };
        if (!validStatuses.Contains(request.Status))
        {
            return BadRequest(new { message = "Invalid payment status" });
        }

        payment.Status = request.Status;
        _unitOfWork.Repository<Payment>().Update(payment);
        await _unitOfWork.SaveChangesAsync();

        // Generate certificate when payment is completed
        if (request.Status == "Completed")
        {
            try
            {
                _logger.LogInformation($"Payment {id} completed. Generating certificate...");
                var certificateBytes = await _certificateService.GenerateCertificateAsync(id);
                
                // You can save the certificate or send it via email here
                // For now, we'll just log that it was generated
                _logger.LogInformation($"Certificate generated successfully for payment {id}. Size: {certificateBytes.Length} bytes");
                
                return Ok(new { 
                    message = "Payment status updated successfully", 
                    certificateGenerated = true,
                    certificateSize = certificateBytes.Length 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to generate certificate for payment {id}");
                return Ok(new { 
                    message = "Payment status updated successfully", 
                    certificateGenerated = false,
                    error = "Certificate generation failed"
                });
            }
        }

        return Ok(new { message = "Payment status updated successfully" });
    }

    /// <summary>
    /// Process payment callback from payment gateway
    /// </summary>
    /// <param name="request">Payment callback data</param>
    /// <returns>Acknowledgment response</returns>
    /// <response code="200">Callback processed successfully</response>
    /// <response code="400">Invalid callback data</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("callback")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> PaymentCallback([FromBody] PaymentCallbackRequest request)
    {
        // TODO: Implement payment gateway callback processing
        // Verify callback authenticity and update payment status
        
        await Task.CompletedTask; // Remove this when implementing actual async operations
        return Ok(new { message = "Callback received and processed" });
    }

    /// <summary>
    /// Download certificate for completed payment
    /// </summary>
    /// <param name="id">Payment ID</param>
    /// <returns>Certificate PDF file</returns>
    /// <response code="200">Certificate downloaded successfully</response>
    /// <response code="400">Payment not completed or certificate not available</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Payment not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id}/certificate")]
    [Authorize]
    [ProducesResponseType(typeof(FileResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> DownloadCertificate(int id)
    {
        try
        {
            var payment = await _unitOfWork.Repository<Payment>().GetByIdAsync(id);
            
            if (payment == null)
            {
                return NotFound(new { message = "Payment not found" });
            }

            if (payment.Status != "Completed")
            {
                return BadRequest(new { message = "Certificate is only available for completed payments" });
            }

            var certificateBytes = await _certificateService.GenerateCertificateAsync(id);
            
            return File(certificateBytes, "application/pdf", $"Certificate_Payment_{id}.pdf");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error downloading certificate for payment {id}");
            return StatusCode(500, new { message = "Error generating certificate" });
        }
    }
}

/// <summary>
/// Request model for initiating a payment
/// </summary>
public class InitiatePaymentRequest
{
    /// <summary>
    /// Payment amount
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    /// Payment method (e.g., "Credit Card", "UPI", "Net Banking")
    /// </summary>
    public string PaymentMethod { get; set; } = string.Empty;
}

/// <summary>
/// Request model for updating payment status
/// </summary>
public class UpdatePaymentStatusRequest
{
    /// <summary>
    /// New payment status
    /// </summary>
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Request model for payment gateway callback
/// </summary>
public class PaymentCallbackRequest
{
    /// <summary>
    /// Transaction ID from payment gateway
    /// </summary>
    public string TransactionId { get; set; } = string.Empty;
    
    /// <summary>
    /// Payment status from gateway
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// Gateway response data
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();
}
