import React, { useState, useEffect } from 'react'
import { <PERSON>, useNavigate, useSearchParams } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Shield, ArrowLeft, Loader2 } from 'lucide-react'
import { api } from '../../lib/api'
import { useAuthStore } from '../../store'
import { UserRole } from '../../types/types'

// Types for API responses
interface AuthResponse {
  token: string
  email: string
  firstName: string
  lastName: string
  role: string
}

interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

interface CheckStatusResponse {
  needsPasswordSetup: boolean
}

const OfficerLoginPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { setUser, setTokens } = useAuthStore()
  const [step, setStep] = useState<'email' | 'password' | 'set-password'>('email')
  const [loading, setLoading] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')

  // Pre-fill email from URL parameters if available
  useEffect(() => {
    const emailParam = searchParams.get('email')
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam))
      // If email is pre-filled, start with password step
      setStep('password')
    }
  }, [searchParams])

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setLoading(true)
    setError('')

    try {
      // Check if officer exists and needs password setup
      const response = await api.post('/auth/officer/check-status', { email }) as ApiResponse<CheckStatusResponse>
      
      if (response.success) {
        if (response.data.needsPasswordSetup) {
          setStep('set-password')
        } else {
          setStep('password')
        }
      }
    } catch (error: any) {
      if (error.response?.status === 404) {
        setError('Officer not found. Please check your email or contact admin.')
      } else {
        setError('Failed to verify officer. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || !password) return

    setLoading(true)
    setError('')

    try {
      const response = await api.post('/auth/officer/login', { 
        email, 
        password 
      }) as ApiResponse<AuthResponse>
      
      if (response.success) {
        const { token, email: userEmail, firstName, lastName, role } = response.data
        setTokens({ token, refreshToken: '' })
        setUser({ 
          id: '', // We don't get ID from login response, but it will be updated by token
          email: userEmail, 
          firstName, 
          lastName, 
     role: (role === 'EXECUTIVE_ENGINEER' ? 'ExecutiveEngineer' :
       role === 'CITY_ENGINEER' ? 'CityEngineer' :
       role) as UserRole,
          isEmailVerified: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
        navigate('/dashboard')  // Let DashboardRouter handle role-based routing
      }
    } catch (error: any) {
      setError('Invalid credentials. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || !password || !confirmPassword) return

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await api.post('/auth/officer/set-password', {
        email,
        password,
        confirmPassword
      })
      
      if (response.success) {
        setStep('password')
        setPassword('')
        setConfirmPassword('')
        setError('')
        alert('Password set successfully! Please login with your new password.')
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to set password. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Shield className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">Officer Login</CardTitle>
          <CardDescription>
            {step === 'email' && 'Enter your officer email address'}
            {step === 'password' && 'Enter your password to continue'}
            {step === 'set-password' && 'Set up your account password'}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {error && (
            <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          {step === 'email' && (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <Label htmlFor="email">Officer Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  'Continue'
                )}
              </Button>
            </form>
          )}

          {step === 'password' && (
            <form onSubmit={handlePasswordLogin} className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                className="w-full"
                onClick={() => setStep('email')}
              >
                Use Different Email
              </Button>
            </form>
          )}

          {step === 'set-password' && (
            <form onSubmit={handleSetPassword} className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="password">New Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter new password"
                  required
                />
              </div>
              <div>
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm new password"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting Password...
                  </>
                ) : (
                  'Set Password'
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                className="w-full"
                onClick={() => setStep('email')}
              >
                Use Different Email
              </Button>
            </form>
          )}
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default OfficerLoginPage
