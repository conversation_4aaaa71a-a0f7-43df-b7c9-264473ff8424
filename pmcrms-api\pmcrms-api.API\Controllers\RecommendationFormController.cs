using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.DTOs;
using pmcrms_api.Application.Interfaces;

namespace pmcrms_api.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class RecommendationFormController : ControllerBase
    {
        private readonly IRecommendationFormService _recommendationFormService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RecommendationFormController> _logger;

        public RecommendationFormController(
            IRecommendationFormService recommendationFormService,
            IUnitOfWork unitOfWork,
            ILogger<RecommendationFormController> logger)
        {
            _recommendationFormService = recommendationFormService;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Generate recommendation form PDF for an application
        /// </summary>
        /// <param name="applicationId">Application ID</param>
        /// <returns>PDF file</returns>
        [HttpGet("application/{applicationId}")]
        public async Task<IActionResult> GenerateRecommendationFormForApplication(int applicationId)
        {
            try
            {
                var application = await _unitOfWork.Repository<pmcrms_api.Domain.Entities.Application>().GetByIdAsync(applicationId);
                if (application == null)
                {
                    return NotFound($"Application with ID {applicationId} not found");
                }

                // Map application data to recommendation form model
                var recommendationModel = new RecommendationFormModel
                {
                    Name = application.ApplicantName,
                    Address1 = "Address not available in current schema", // You'll need to add address fields to Application entity
                    Address2 = "Address not available in current schema",
                    Position = "स्ट्रक्चरल इंजिनिअर", // Default position, should come from application data
                    Date = application.SubmissionDate,
                    Qualification = new List<string> { "Qualification not available in current schema" }, // You'll need to add qualification field
                    MobileNumber = application.MobileNumber ?? "Not available", // Updated property name
                    MonthDifference = "0", // You'll need to calculate this from application data
                    YearDifference = "0", // You'll need to calculate this from application data
                    IsBothAddressSame = true,
                    JrEnggName = "शाखा अभियंता", // These should come from configuration or database
                    AssEnggName = "उपअभियंता",
                    ExeEnggName = "कार्यकारी अभियंता",
                    CityEnggName = "शहर अभियंता"
                };

                var pdfBytes = await _recommendationFormService.GenerateRecommendationFormAsync(recommendationModel);

                return File(pdfBytes, "application/pdf", $"RecommendationForm_{application.Id}_{DateTime.Now:yyyyMMdd}.pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating recommendation form for application {ApplicationId}", applicationId);
                return StatusCode(500, "An error occurred while generating the recommendation form");
            }
        }

        /// <summary>
        /// Generate recommendation form PDF with custom data
        /// </summary>
        /// <param name="model">Recommendation form data</param>
        /// <returns>PDF file</returns>
        [HttpPost("generate")]
        public async Task<IActionResult> GenerateRecommendationForm([FromBody] RecommendationFormModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var pdfBytes = await _recommendationFormService.GenerateRecommendationFormAsync(model);

                return File(pdfBytes, "application/pdf", $"RecommendationForm_{model.Name}_{DateTime.Now:yyyyMMdd}.pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating recommendation form for {Name}", model.Name);
                return StatusCode(500, "An error occurred while generating the recommendation form");
            }
        }

        /// <summary>
        /// Test endpoint to generate a sample recommendation form
        /// </summary>
        /// <returns>PDF file</returns>
        [HttpGet("test")]
        [AllowAnonymous] // Remove this in production
        public async Task<IActionResult> GenerateTestRecommendationForm()
        {
            try
            {
                var testModel = new RecommendationFormModel
                {
                    Name = "Abhishek Hattekar",
                    Date = new DateTime(2024, 3, 13),
                    Address1 = "305, Gera 77, Kalyani Nagar, Pune-6305, Gera 77,",
                    Address2 = "305, Gera 77, Kalyani Nagar, Pune-6,Gera 77, Kalyani Nagar, Pune-6,Gera 77, Kalyani Nagar, Pune-6,Gera 77, Kalyani Nagar, Pune-6",
                    Position = "स्ट्रक्चरल इंजिनिअर",
                    JrEnggName = "राजेंद्र फुंदे",
                    AssEnggName = "राहुल सोरटे",
                    ExeEnggName = "मुकुंद शिंदे",
                    CityEnggName = "प्रशांत वाघमारे",
                    Qualification = new List<string> { "M.Com", "B.Com" },
                    MobileNumber = "7894544111",
                    MonthDifference = "3",
                    YearDifference = "5",
                    IsBothAddressSame = false
                };

                var pdfBytes = await _recommendationFormService.GenerateRecommendationFormAsync(testModel);

                return File(pdfBytes, "application/pdf", $"TestRecommendationForm_{DateTime.Now:yyyyMMdd}.pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating test recommendation form");
                return StatusCode(500, "An error occurred while generating the test recommendation form");
            }
        }
    }
}
