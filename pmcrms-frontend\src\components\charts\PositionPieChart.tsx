import React from 'react';
import { useCubeQuery } from '@cubejs-client/react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Too<PERSON><PERSON>, Legend } from 'recharts';

interface PositionPieChartProps {
  query: any;
  title?: string;
  height?: number;
  onDrillDown?: (position: string) => void;
}

// Color palette for different positions
const POSITION_COLORS: Record<string, string> = {
  'Architect': '#8884d8',
  'License Engineer': '#82ca9d',
  'Structural Engineer': '#ffc658',
  'Supervisor1': '#ff7c7c',
  'Supervisor2': '#8dd1e1',
  'Other': '#d084d0'
};

const PositionPieChart: React.FC<PositionPieChartProps> = ({
  query,
  title = 'Applications by Position',
  height = 400,
  onDrillDown
}) => {
  const { resultSet, isLoading, error } = useCubeQuery(query);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-red-600">
        <div className="text-center">
          <p className="text-lg font-semibold">Error loading chart data</p>
          <p className="text-sm">{error.toString()}</p>
        </div>
      </div>
    );
  }

  if (!resultSet) {
    return (
      <div className="flex items-center justify-center h-96 text-gray-500">
        <p>No data available</p>
      </div>
    );
  }

  const chartData = resultSet.chartPivot().map((row: any) => {
    const position = row.x || 'Unknown';
    const count = parseInt(row['Applications.count']) || 0;
    
    return {
      name: position,
      value: count,
      color: POSITION_COLORS[position] || '#d084d0'
    };
  });

  const handleCellClick = (data: any) => {
    if (onDrillDown) {
      onDrillDown(data.name);
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const percentage = ((data.value / chartData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1);
      
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{data.payload.name}</p>
          <p className="text-blue-600">Applications: {data.value}</p>
          <p className="text-gray-600">Percentage: {percentage}%</p>
          {onDrillDown && (
            <p className="text-xs text-gray-500 mt-1">Click to drill down</p>
          )}
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices < 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  const totalApplications = chartData.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="text-sm text-gray-600">
          Total: {totalApplications.toLocaleString()} applications
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={CustomLabel}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
            onClick={handleCellClick}
            style={{ cursor: onDrillDown ? 'pointer' : 'default' }}
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.color}
                stroke="#fff"
                strokeWidth={2}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            verticalAlign="bottom" 
            height={36}
            formatter={(value: string, entry: any) => (
              <span style={{ color: entry.color }}>{value}</span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>

      {/* Summary Statistics */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        {chartData.slice(0, 4).map((item, index) => (
          <div key={index} className="text-center">
            <div 
              className="w-4 h-4 rounded-full mx-auto mb-1" 
              style={{ backgroundColor: item.color }}
            ></div>
            <p className="text-xs text-gray-600">{item.name}</p>
            <p className="text-sm font-semibold text-gray-900">{item.value}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PositionPieChart;
