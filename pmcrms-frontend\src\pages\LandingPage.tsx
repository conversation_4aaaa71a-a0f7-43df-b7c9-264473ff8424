import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { FileText, Search, Download, Users, Award, Shield, Mail, KeyRound, ArrowRight } from 'lucide-react'
import Logo from '../components/Logo'

const LandingPage: React.FC = () => {
  const [applicationNumber, setApplicationNumber] = React.useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Logo size="lg" showText={true} />
            <div className="flex space-x-4">
              <Link to="/login">
                <Button variant="outline">Login</Button>
              </Link>
              <Link to="/register">
                <Button>Register</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              Professional Municipal Corporation
            </h1>
            <h2 className="text-3xl font-bold text-primary mt-3 sm:text-4xl md:text-5xl">
              Registration Management System
            </h2>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              Streamlined online registration system for Architects, Engineers, and Supervisors 
              in Municipal Corporation projects. Apply, track, and manage your professional registrations digitally.
            </p>
            
            <div className="mt-10">
              <Link to="/apply">
                <Button size="lg" className="mr-4">
                  <FileText className="mr-2 h-5 w-5" />
                  Start Application
                </Button>
              </Link>
              <Link to="/status">
                <Button variant="outline" size="lg">
                  <Search className="mr-2 h-5 w-5" />
                  Check Status
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Status Check */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900">Quick Application Status Check</h3>
            <p className="mt-4 text-lg text-gray-600">
              Enter your application number to get instant status updates
            </p>
          </div>
          
          <div className="max-w-md mx-auto">
            <div className="flex space-x-4">
              <Input
                placeholder="Enter Application Number"
                value={applicationNumber}
                onChange={(e) => setApplicationNumber(e.target.value)}
                className="flex-1"
              />
              <Link to={`/status?app=${applicationNumber}`}>
                <Button disabled={!applicationNumber}>
                  <Search className="mr-2 h-4 w-4" />
                  Check Status
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900">Key Features</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Online Application
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Submit your registration application online with easy-to-use forms and document upload
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="mr-2 h-5 w-5 text-primary" />
                  Real-time Tracking
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Track your application status in real-time with detailed workflow information
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="mr-2 h-5 w-5 text-primary" />
                  Digital Certificates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Download your digital certificates instantly upon approval with secure verification
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5 text-primary" />
                  Multi-role Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Support for Architects, Engineers, Supervisors with role-based workflows
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="mr-2 h-5 w-5 text-primary" />
                  Digital Signatures
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Secure digital signatures from authorized officials ensuring document authenticity
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5 text-primary" />
                  Secure & Reliable
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Bank-grade security with encrypted data storage and secure payment processing
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Guest Certificate Download */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Guest Certificate Download</h3>
            <p className="text-lg text-gray-600 mb-8">
              Already have a registration? Access your certificate with OTP verification via email
            </p>
            <div className="bg-white rounded-lg p-6 max-w-md mx-auto mb-6 shadow-lg">
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span>Enter Email</span>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2">
                  <KeyRound className="h-4 w-4 text-green-600" />
                  <span>Receive OTP</span>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2">
                  <Download className="h-4 w-4 text-purple-600" />
                  <span>Download</span>
                </div>
              </div>
            </div>
            <Link to="/guest">
              <Button variant="outline" size="lg">
                <Mail className="mr-2 h-5 w-5" />
                Guest Login with OTP
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h4 className="text-xl font-semibold mb-4">Professional Municipal Corporation Registration Management System</h4>
            <p className="text-gray-400">
              © 2024 Municipal Corporation. All rights reserved. | 
              <a href="#" className="ml-2 hover:text-white">Privacy Policy</a> | 
              <a href="#" className="ml-2 hover:text-white">Terms of Service</a> |
              <a href="#" className="ml-2 hover:text-white">Contact Support</a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
