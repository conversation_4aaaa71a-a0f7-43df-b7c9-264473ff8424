// Cube.js Server Entry Point
const CubejsServer = require('@cubejs-backend/server');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const config = require('./cube.js');

// Load environment variables
require('dotenv').config();

// Create Express app
const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: process.env.RATE_LIMIT_MESSAGE || 'Too many requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/cubejs-api', limiter);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:3000',
      ...(process.env.ADDITIONAL_CORS_ORIGINS ? process.env.ADDITIONAL_CORS_ORIGINS.split(',') : [])
    ];
    
    // Allow requests with no origin (mobile apps, curl, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Custom middleware for logging
app.use((req, res, next) => {
  const start = Date.now();
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${res.statusCode} (${duration}ms)`);
  });
  
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  const healthcheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    environment: process.env.NODE_ENV || 'development',
    version: require('./package.json').version
  };

  try {
    // You can add more health checks here (database, cache, etc.)
    res.status(200).json(healthcheck);
  } catch (error) {
    healthcheck.message = 'Error';
    healthcheck.error = error.message;
    res.status(503).json(healthcheck);
  }
});

// Custom JWT validation function
const checkAuth = async (req, auth) => {
  if (!auth) {
    throw new Error('Authorization header is required');
  }

  try {
    // Extract token from Bearer header
    const token = auth.replace('Bearer ', '');
    
    // Verify JWT token (you should use the same secret as your ASP.NET Core API)
    const decoded = jwt.verify(token, process.env.CUBEJS_API_SECRET);
    
    // Extract user information from token
    return {
      userId: decoded.sub || decoded.userId || 'unknown',
      role: decoded.role || 'user',
      email: decoded.email,
      // Add any other claims you need
    };
  } catch (error) {
    console.error('JWT validation error:', error);
    throw new Error('Invalid authorization token');
  }
};

// Cube.js server configuration
const server = new CubejsServer({
  // Database configuration
  dbType: process.env.CUBEJS_DB_TYPE || 'mssql',
  
  // Schema location
  schemaPath: './CubeJS-Setup',
  
  // API Secret for JWT
  apiSecret: process.env.CUBEJS_API_SECRET,
  
  // Custom authentication check
  checkAuth: checkAuth,
  
  // Context enrichment
  contextToAppId: ({ userId, role }) => `pmcrms_${userId}_${role}`,
  
  contextToOrchestratorId: ({ userId, role }) => `orchestrator_${role}`,
  
  // Pre-aggregation refresh
  orchestratorOptions: {
    redisPrefix: 'PMCRMS_CUBEJS',
    queryCacheOptions: {
      refreshKeyRenewalThreshold: 30,
      backgroundRenew: true,
    },
  },
  
  // Cache configuration
  cacheAndQueueDriver: process.env.NODE_ENV === 'production' ? 'redis' : 'memory',
  
  // Development mode
  devMode: process.env.NODE_ENV !== 'production',
  
  // Custom logger
  logger: (msg, params) => {
    if (process.env.LOG_QUERIES === 'true' || process.env.NODE_ENV !== 'production') {
      console.log(`[CUBEJS] ${msg}`, params);
    }
  },
  
  // Query rewrite for security
  queryRewrite: (query, { userId, role }) => {
    // Add security filters based on user role
    if (role === 'GUEST') {
      // Guests can only see approved applications
      if (query.measures || query.dimensions) {
        query.filters = query.filters || [];
        query.filters.push({
          member: 'Applications.status',
          operator: 'equals',
          values: ['FINAL_APPROVE']
        });
      }
    }
    
    return query;
  },
  
  // External database connection for pre-aggregations (production)
  externalDriverFactory: () => {
    if (process.env.NODE_ENV === 'production' && process.env.PREAGG_DB_TYPE) {
      return {
        type: process.env.PREAGG_DB_TYPE,
        host: process.env.PREAGG_DB_HOST,
        port: parseInt(process.env.PREAGG_DB_PORT),
        database: process.env.PREAGG_DB_NAME,
        user: process.env.PREAGG_DB_USER,
        password: process.env.PREAGG_DB_PASS,
      };
    }
    return undefined;
  },
  
  // Telemetry
  telemetry: process.env.CUBEJS_TELEMETRY !== 'false',
  
  // Web sockets for real-time updates
  webSocketsBasePath: process.env.CUBEJS_WEB_SOCKETS_BASE_PATH || '/',
});

// Add Cube.js middleware to Express app
app.use('/cubejs-api', server.getMiddleware());

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Cube.js Server Error:', error);
  
  const errorResponse = {
    error: {
      message: error.message || 'Internal server error',
      type: error.constructor.name,
      timestamp: new Date().toISOString(),
    },
  };

  // Don't expose stack traces in production
  if (process.env.NODE_ENV !== 'production') {
    errorResponse.error.stack = error.stack;
  }

  res.status(error.status || 500).json(errorResponse);
});

// Start server
const port = process.env.CUBEJS_API_PORT || 4000;
const host = process.env.CUBEJS_API_HOST || '0.0.0.0';

app.listen(port, host, () => {
  console.log(`🚀 Cube.js server is running on ${host}:${port}`);
  console.log(`📊 Cube.js Playground: http://${host}:${port}`);
  console.log(`🔍 Health check: http://${host}:${port}/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  if (process.env.NODE_ENV === 'production') {
    console.log('✅ Running in production mode');
  } else {
    console.log('🛠️  Running in development mode');
    console.log('🎮 Playground enabled at root URL');
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

module.exports = app;
