@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: Inter, system-ui, sans-serif;
  }
}

/* Additional component styling fixes */
.card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

/* Custom styles for PMCRMS */
.application-number {
  @apply font-mono text-sm font-semibold;
}

.status-timeline {
  @apply relative;
}

.status-timeline::before {
  @apply absolute left-4 top-8 h-full w-0.5;
  background-color: hsl(var(--border));
  content: '';
}

.workflow-step {
  @apply relative pl-10 pb-8;
}

.workflow-step::before {
  @apply absolute left-3 top-1 h-2 w-2 rounded-full border-2;
  border-color: hsl(var(--background));
  background-color: hsl(var(--muted));
  content: '';
}

.workflow-step.active::before {
  background-color: #1e40af; /* primary-600 */
}

.workflow-step.completed::before {
  background-color: #059669; /* secondary-600 */
}

.print-only {
  @apply hidden;
}

@media print {
  .no-print {
    @apply hidden !important;
  }
  
  .print-only {
    @apply block !important;
  }
  
  body {
    color: black;
    background-color: white;
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1e40af;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File upload styles */
.file-upload-zone {
  @apply border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer;
  border-color: hsl(var(--border));
}

.file-upload-zone:hover {
  border-color: #1e40af; /* primary-600 */
}

.file-upload-zone.dragover {
  border-color: #1e40af; /* primary-600 */
  background-color: rgba(30, 64, 175, 0.05); /* primary/5 */
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .mobile-stack {
    @apply flex-col space-y-2 space-x-0;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

/* Focus styles for accessibility */
.focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #1e40af, 0 0 0 4px rgba(30, 64, 175, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .status-badge {
    @apply border-2 border-current;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
