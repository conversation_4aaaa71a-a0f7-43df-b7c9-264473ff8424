using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Configuration;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Models;

namespace pmcrms_api.Infrastructure.Services
{
    public class SmtpEmailService : IEmailService
    {
        private readonly string _smtpServer;
        private readonly int _smtpPort;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _senderEmail;
        private readonly string _senderName;

        public SmtpEmailService(IConfiguration configuration)
        {
            _smtpServer = configuration["SmtpSettings:Server"] ?? throw new ArgumentNullException("SmtpSettings:Server");
            _smtpPort = int.Parse(configuration["SmtpSettings:Port"] ?? "587");
            _smtpUsername = configuration["SmtpSettings:Username"] ?? throw new ArgumentNullException("SmtpSettings:Username");
            _smtpPassword = configuration["SmtpSettings:Password"] ?? throw new ArgumentNullException("SmtpSettings:Password");
            _senderEmail = configuration["SmtpSettings:SenderEmail"] ?? throw new ArgumentNullException("SmtpSettings:SenderEmail");
            _senderName = configuration["SmtpSettings:SenderName"] ?? "PMCRMS Portal";
        }

        public async Task SendEmailAsync(EmailMessage message)
        {
            using var mailMessage = new MailMessage
            {
                From = new MailAddress(_senderEmail, _senderName),
                Subject = message.Subject,
                Body = message.Body,
                IsBodyHtml = message.IsHtml
            };

            mailMessage.To.Add(message.To);

            foreach (var attachment in message.Attachments)
            {
                using var ms = new MemoryStream(attachment.Content);
                mailMessage.Attachments.Add(new Attachment(ms, attachment.FileName, attachment.ContentType));
            }

            using var smtpClient = new SmtpClient(_smtpServer, _smtpPort)
            {
                EnableSsl = true,
                Credentials = new NetworkCredential(_smtpUsername, _smtpPassword)
            };

            await smtpClient.SendMailAsync(mailMessage);
        }

        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            var message = new EmailMessage
            {
                To = to,
                Subject = subject,
                Body = body,
                IsHtml = isHtml
            };

            await SendEmailAsync(message);
        }

        public async Task SendEmailWithAttachmentAsync(EmailMessage message, byte[] attachment, string fileName, string contentType = "application/pdf")
        {
            message.Attachments.Add(new EmailAttachment
            {
                Content = attachment,
                FileName = fileName,
                ContentType = contentType
            });

            await SendEmailAsync(message);
        }
    }
}
