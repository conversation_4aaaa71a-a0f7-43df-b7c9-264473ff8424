# Guest OTP Authentication System

## Overview
The PMCRMS frontend now supports OTP-only authentication for guest users who want to check their application status and download certificates without creating an account.

## Features Implemented

### 🔐 OTP-Only Authentication
- **No Username/Password**: Guest users only need their email address
- **Email OTP Verification**: 6-digit OTP sent to user's email
- **Session Management**: Temporary guest sessions with expiration
- **Secure Access**: Session-based access to application data

### 📱 User Experience
1. **Email Entry**: User enters their email address
2. **OTP Delivery**: System sends 6-digit OTP to email
3. **OTP Verification**: User enters OTP to authenticate
4. **Session Creation**: Guest session created with expiration
5. **Application Access**: Access to status check and downloads

### 🔧 Implementation Details

#### Components Updated
- **`GuestLogin.tsx`**: Complete OTP authentication flow
- **`ApplicationStatus.tsx`**: Guest-enabled status checking
- **`LandingPage.tsx`**: Enhanced guest access section

#### New Hook Created
- **`useGuestAuth.ts`**: Manages guest session state
  - Session validation
  - Automatic cleanup on expiration
  - Local storage management

#### API Functions Added
```typescript
guestAuth.sendOtp(email)           // Send OTP to email
guestAuth.verifyOtp(email, otp)    // Verify OTP and create session
guestAuth.getApplicationStatus()   // Get application data for guest
guestAuth.downloadCertificate()    // Download certificate for guest
```

### 🎯 Security Features
- **Session Expiration**: Automatic cleanup of expired sessions
- **OTP Resend Cooldown**: 60-second cooldown between OTP requests
- **Input Validation**: Zod schema validation for email and OTP
- **Error Handling**: Comprehensive error messages and states

### 🎨 UI/UX Enhancements
- **Two-Step Process**: Clear email → OTP flow
- **Visual Indicators**: Step-by-step process visualization
- **Loading States**: Proper loading indicators during API calls
- **Resend Functionality**: Easy OTP resend with cooldown timer
- **Status Badges**: Color-coded application status indicators

## Backend API Requirements

The frontend expects these API endpoints to be implemented:

### Authentication Endpoints
```
POST /api/auth/guest/send-otp
Body: { email: string }
Response: { message: string }

POST /api/auth/guest/verify-otp  
Body: { email: string, otp: string }
Response: { sessionId: string, expiresAt: string }
```

### Guest Access Endpoints
```
GET /api/applications/guest/{applicationNumber}
Query: sessionId
Response: { applicationData... }

GET /api/applications/guest/{applicationNumber}/certificate
Query: sessionId
Response: PDF file blob
```

## Usage Flow

### 1. Landing Page
- Users see enhanced "Guest Login with OTP" section
- Visual flow: Email → OTP → Download
- Clear call-to-action button

### 2. Guest Login (`/guest`)
- Email input with validation
- OTP input with 6-digit formatting
- Resend functionality with cooldown
- Error handling and feedback

### 3. Application Status (`/status`)
- Requires guest authentication
- Application number search
- Detailed status display
- Certificate download for approved applications

### 4. Session Management
- Automatic session validation
- Cleanup on expiration
- Seamless user experience

## Environment Variables

Ensure these are configured:
```
VITE_API_URL=https://your-backend-api.com/api
```

## Testing Scenarios

1. **Email Validation**: Test invalid email formats
2. **OTP Flow**: Test OTP send/verify process
3. **Session Expiry**: Test automatic session cleanup
4. **Error Handling**: Test network errors and invalid OTPs
5. **Application Search**: Test valid/invalid application numbers
6. **Certificate Download**: Test download functionality

## Benefits

### For Users
- ✅ No account registration required
- ✅ Quick access via email
- ✅ Secure OTP verification
- ✅ Easy certificate downloads

### For System
- ✅ Reduced user registration friction
- ✅ Secure temporary access
- ✅ Automatic session cleanup
- ✅ Clear separation from regular users

## Next Steps

1. **Backend Integration**: Implement required API endpoints
2. **Email Service**: Configure OTP email delivery
3. **Testing**: Comprehensive testing with real backend
4. **Analytics**: Track guest usage patterns
5. **Enhancements**: Add multi-language support for OTP emails

---

**Note**: This implementation provides a complete guest authentication system that prioritizes security while maintaining excellent user experience. The OTP-only approach eliminates password management complexity for guest users.
