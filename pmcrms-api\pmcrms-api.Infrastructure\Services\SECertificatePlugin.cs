using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using System.Drawing;
using SDI = System.Drawing.Imaging;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using Document = QuestPDF.Fluent.Document;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Infrastructure.Services
{
    public class SECertificatePlugin : IPDFService
    {
        private readonly string _mangalFontPath;
        
        public SECertificatePlugin(string mangalFontPath = "Fonts/Mangal.ttf")
        {
            _mangalFontPath = mangalFontPath;
            QuestPDF.Settings.License = LicenseType.Community;
        }

        public async Task<byte[]> GenerateQRCodeAsync(string content)
        {
            // Create a simple placeholder instead of actual QR code
            var text = $"QR Code Content: {content}";
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(100, 100);
                    page.Margin(5);
                    page.Content().Text(text).FontSize(8);
                });
            });

            return await Task.FromResult(document.GeneratePdf());
        }

        public async Task<byte[]> GenerateRecommendedFormAsync(pmcrms_api.Domain.Entities.Application application, string currentSignatory)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontSize(11).FontFamily("Times New Roman"));

                    page.Header()
                        .Text("RECOMMENDED FORM")
                        .SemiBold().FontSize(20).FontFamily("Arial Black")
                        .AlignCenter();

                    page.Content()
                        .Column(column =>
                        {
                            // Application Details in English
                            column.Item().Text($"Application ID: {application.Id}");
                            column.Item().Text($"Applicant Name: {application.ApplicantName}");
                            
                            // Application Details in Marathi using Mangal font
                            column.Item().Text($"अर्जदाराचे नाव: {application.ApplicantName}")
                                .FontFamily("Mangal");

                            // Signature Section
                            column.Item().PaddingTop(20).Text("Signatures:");
                            foreach (var signature in application.WorkflowStateHistory
                                .Where(w => w.ActionByUser != null)
                                .OrderBy(w => w.TransitionDate))
                            {
                                column.Item().Text($"{signature.ActionByUser.FirstName} {signature.ActionByUser.LastName} - {signature.TransitionDate:dd/MM/yyyy}");
                            }

                            // Current Signatory
                            if (!string.IsNullOrEmpty(currentSignatory))
                            {
                                column.Item().PaddingTop(20)
                                    .Text($"Pending Signature: {currentSignatory}")
                                    .FontColor("#FF0000");
                            }
                        });

                    page.Footer()
                        .AlignRight()
                        .Text(x =>
                        {
                            x.Span("Page ");
                            x.CurrentPageNumber();
                            x.Span(" of ");
                            x.TotalPages();
                        });
                });
            });

            return await Task.FromResult(document.GeneratePdf());
        }

        public async Task<byte[]> GenerateCertificateAsync(pmcrms_api.Domain.Entities.Application application)
        {
            // Generate QR code with application details
            var qrCode = await GenerateQRCodeAsync($"Application ID: {application.Id}\nStatus: {application.CurrentState}");

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontSize(12).FontFamily("Times New Roman"));

                    page.Header()
                        .Row(row =>
                        {
                            row.ConstantItem(100).Image(qrCode);
                            row.RelativeItem().AlignCenter().Text("SOCIO-ECONOMIC CERTIFICATE")
                                .SemiBold().FontSize(24).FontFamily("Arial Black");
                        });

                    page.Content()
                        .Column(column =>
                        {
                            // Certificate content in English
                            column.Item().Text("This is to certify that:")
                                .FontSize(14).FontFamily("Times New Roman");
                            
                            column.Item().PaddingTop(20)
                                .Text($"Mr./Mrs./Ms. {application.ApplicantName}");

                            // Certificate content in Marathi
                            column.Item().PaddingTop(20)
                                .Text("प्रमाणित करण्यात येते की:")
                                .FontFamily("Mangal").FontSize(14);
                            
                            column.Item().PaddingTop(10)
                                .Text($"श्री/श्रीमती/कु. {application.ApplicantName}")
                                .FontFamily("Mangal");

                            // Validation Section
                            column.Item().PaddingTop(40)
                                .Text($"Certificate Number: {application.Id}")
                                .FontSize(10);
                            
                            column.Item()
                                .Text($"Issue Date: {DateTime.Now:dd/MM/yyyy}")
                                .FontSize(10);
                            
                            column.Item()
                                .Text($"Valid Until: {DateTime.Now.AddYears(1):dd/MM/yyyy}")
                                .FontSize(10);
                        });

                    page.Footer()
                        .Row(row =>
                        {
                            row.RelativeItem().AlignRight()
                                .Text("Authorized Signatory")
                                .FontFamily("Arial Black");
                        });
                });
            });

            return document.GeneratePdf();
        }

        public async Task<byte[]> GenerateChallanAsync(pmcrms_api.Domain.Entities.Application application, Payment payment)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontSize(11).FontFamily("Times New Roman"));

                    page.Header()
                        .Text("PAYMENT CHALLAN")
                        .SemiBold().FontSize(20).FontFamily("Arial Black")
                        .AlignCenter();

                    page.Content()
                        .Column(column =>
                        {
                            // Challan Details in English
                            column.Item().Text($"Challan Number: {payment.Id}");
                            column.Item().Text($"Application ID: {application.Id}");
                            column.Item().Text($"Applicant Name: {application.ApplicantName}");
                            column.Item().Text($"Amount Paid: ₹{payment.Amount:N2}");
                            column.Item().Text($"Payment Date: {payment.PaymentDate:dd/MM/yyyy}");
                            column.Item().Text($"Payment Status: {payment.Status}");
                            
                            // Challan Details in Marathi
                            column.Item().PaddingTop(20)
                                .Text("चलन तपशील:")
                                .FontFamily("Mangal").FontSize(14);
                            
                            column.Item()
                                .Text($"अर्जदाराचे नाव: {application.ApplicantName}")
                                .FontFamily("Mangal");
                                
                            column.Item()
                                .Text($"भरलेली रक्कम: ₹{payment.Amount:N2}")
                                .FontFamily("Mangal");
                        });

                    page.Footer()
                        .AlignCenter()
                        .Text("This is a computer generated challan and does not require signature");
                });
            });

            return document.GeneratePdf();
        }
    }
}
