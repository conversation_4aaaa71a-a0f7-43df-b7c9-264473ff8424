import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuthStore } from '../store'

// Import dashboard components
import JuniorEngineerDashboard from '../pages/dashboard/JuniorEngineerDashboard'
import AssistantEngineerDashboard from '../pages/dashboard/AssistantEngineerDashboard'
import ExecutiveEngineerDashboard from '../pages/dashboard/ExecutiveEngineerDashboard'
import CityEngineerDashboard from '../pages/dashboard/CityEngineerDashboard'
import ClerkDashboard from '../pages/dashboard/ClerkDashboard'

const DashboardRouter: React.FC = () => {
  const { user } = useAuthStore()

  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Route users to their appropriate dashboard based on role
  switch (user.role) {
    case 'JRENGG_ARCH':
    case 'JRENGG_STRU':
    case 'JRENGG_LICE':
    case 'JRENGG_SUPER1':
    case 'JRENGG_SUPER2':
      return <JuniorEngineerDashboard />

    case 'ASSIENGG_ARCH':
    case 'ASSIENGG_STRU':
    case 'ASSIENGG_LICE':
    case 'ASSIENGG_SUPER1':
    case 'ASSIENGG_SUPER2':
      return <AssistantEngineerDashboard />

    case 'ExecutiveEngineer':
      return <ExecutiveEngineerDashboard />

    case 'CityEngineer':
      return <CityEngineerDashboard />

    case 'CLERK':
      return <ClerkDashboard />

    case 'PUBLIC_USER':
    default:
      // For public users, create a simple dashboard or redirect to application form
      return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Welcome to PMCRMS</h1>
            <p className="text-gray-600 mt-2">Hello {user.firstName} {user.lastName}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Submit New Application</h3>
              <p className="text-gray-600 mb-4">Apply for PMC construction permissions</p>
              <a
                href="/apply"
                className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-600 transition-colors"
              >
                Start Application
              </a>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Check Status</h3>
              <p className="text-gray-600 mb-4">Track your application progress</p>
              <a
                href="/status"
                className="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                Check Status
              </a>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">Your Profile</h3>
              <p className="text-gray-600 mb-4">Manage your account settings</p>
              <button
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                onClick={() => {
                  window.location.href = '/officer/profile'
                }}
              >
                View Profile
              </button>
            </div>
          </div>
        </div>
      )
  }
}

export default DashboardRouter
