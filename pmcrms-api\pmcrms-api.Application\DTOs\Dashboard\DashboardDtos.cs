namespace pmcrms_api.Application.DTOs.Dashboard;

// Dashboard DTOs
public class DashboardResponse
{
    public bool Success { get; set; }
    public DashboardData Data { get; set; } = null!;
}

public class DashboardData
{
    public int TotalApplications { get; set; }
    public int Pending { get; set; }
    public int Approved { get; set; }
    public int Rejected { get; set; }
    public int InProgress { get; set; }
    public List<int> MonthlyTrend { get; set; } = new();
    public Dictionary<string, int> PositionBreakdown { get; set; } = new();
    public List<RecentApplicationDto> RecentApplications { get; set; } = new();
    public SystemHealthDto SystemHealth { get; set; } = null!;
}

public class RecentApplicationDto
{
    public string Id { get; set; } = string.Empty;
    public string ApplicationNumber { get; set; } = string.Empty;
    public string ApplicantName { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime SubmittedDate { get; set; }
    public string AssignedTo { get; set; } = string.Empty;
}

public class SystemHealthDto
{
    public string Status { get; set; } = string.Empty;
    public string Uptime { get; set; } = string.Empty;
    public DateTime LastBackup { get; set; }
}

// Analytics DTOs
public class AnalyticsResponse
{
    public bool Success { get; set; }
    public AnalyticsData Data { get; set; } = null!;
    public string TimeFrame { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

public class AnalyticsData
{
    public Dictionary<string, int> ApplicationsByStatus { get; set; } = new();
    public Dictionary<string, int> ApplicationsByPosition { get; set; } = new();
    public ProcessingTimeAnalysis ProcessingTimeAnalysis { get; set; } = null!;
    public Dictionary<string, WorkflowStageData> WorkflowStageAnalysis { get; set; } = new();
}

public class ProcessingTimeAnalysis
{
    public int AverageProcessingDays { get; set; }
    public int FastestProcessing { get; set; }
    public int SlowestProcessing { get; set; }
}

public class WorkflowStageData
{
    public int Count { get; set; }
    public int AverageDays { get; set; }
}
