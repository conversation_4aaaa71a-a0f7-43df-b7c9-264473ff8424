using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using RestSharp;
using System.Linq.Expressions;
using DomainEntities = pmcrms_api.Domain.Entities;

namespace pmcrms_api.Infrastructure.Services
{
    public class EasebuzzPaymentService : IPaymentService
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly string _key;
        private readonly string _salt;
        private readonly string _env;
        private readonly string _baseUrl;

        public EasebuzzPaymentService(
            IConfiguration configuration,
            IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            
            _key = _configuration["Easebuzz:Key"] ?? throw new ArgumentNullException("Easebuzz:Key");
            _salt = _configuration["Easebuzz:Salt"] ?? throw new ArgumentNullException("Easebuzz:Salt");
            _env = _configuration["Easebuzz:Environment"] ?? "test";
            _baseUrl = _env.ToLower() == "prod" 
                ? "https://pay.easebuzz.in" 
                : "https://testpay.easebuzz.in";
        }

        public async Task<(bool success, string? paymentUrl, string? error)> InitiatePaymentAsync(
            Transaction transaction,
            string returnUrl,
            string notifyUrl)
        {
            try
            {
                var application = await _unitOfWork.Repository<DomainEntities.Application>()
                    .GetByIdAsync(transaction.ApplicationEntityId);
                
                if (application == null)
                    return (false, null, "Application not found");

                var parameters = new Dictionary<string, string>
                {
                    ["key"] = _key,
                    ["txnid"] = transaction.TransactionId,
                    ["amount"] = transaction.Amount,
                    ["firstname"] = application.ApplicantName,
                    ["email"] = application.EmailAddress ?? "<EMAIL>",
                    ["phone"] = application.MobileNumber ?? "1234567890",
                    ["productinfo"] = $"Application {application.ApplicationNumber} Payment",
                    ["surl"] = returnUrl,
                    ["furl"] = returnUrl,
                    ["udf1"] = transaction.Id.ToString(),
                    ["udf2"] = application.Id.ToString()
                };

                // Generate hash
                var hashString = GenerateHash(parameters);
                parameters["hash"] = hashString;

                var client = new RestClient(_baseUrl);
                var request = new RestRequest("/payment/initiateLink", Method.Post);
                
                foreach (var param in parameters)
                {
                    request.AddParameter(param.Key, param.Value);
                }

                var response = await client.ExecuteAsync(request);

                if (!response.IsSuccessful)
                    return (false, null, $"Failed to initiate payment: {response.ErrorMessage}");

                // Parse response and extract payment URL
                // Assuming response contains payment URL directly, adjust as per actual API response
                var paymentUrl = response.Content;

                // Update transaction with initiated status
                transaction.Status = "INITIATED";
                transaction.UpdatedAt = DateTime.UtcNow;
                _unitOfWork.Repository<Transaction>().Update(transaction);
                await _unitOfWork.SaveChangesAsync();

                return (true, paymentUrl, null);
            }
            catch (Exception ex)
            {
                return (false, null, $"Error initiating payment: {ex.Message}");
            }
        }

        public async Task<(bool success, string? error)> HandleCallbackAsync(
            Dictionary<string, string> callbackParams)
        {
            try
            {
                if (!VerifyHash(callbackParams))
                    return (false, "Invalid hash in callback");

                var transactionNumber = callbackParams["txnid"];
                var status = callbackParams["status"]?.ToUpper();
                var easebuzzTransactionId = callbackParams["easepayid"];
                var paymentMode = callbackParams["mode"];
                var bankRef = callbackParams["bank_ref_num"];
                var error = callbackParams["error_Message"];

                var transactions = await _unitOfWork.Repository<Transaction>()
                    .FindAsync(t => t.TransactionId == transactionNumber);

                var transaction = transactions.FirstOrDefault();
                if (transaction == null)
                    return (false, "Transaction not found");

                transaction.Status = status ?? "UNKNOWN";
                transaction.TransactionId = easebuzzTransactionId; // Updated property name
                transaction.PaymentMode = paymentMode;
                transaction.BankRefNumber = bankRef; // Updated property name
                transaction.ErrorMessage = error;
                transaction.UpdatedAt = DateTime.UtcNow;
                transaction.TransactionDesc = System.Text.Json.JsonSerializer.Serialize(callbackParams); // Use TransactionDesc instead of RawResponse

                _unitOfWork.Repository<Transaction>().Update(transaction);

                if (status == "SUCCESS")
                {
                    // Update application status if payment successful
                    var application = await _unitOfWork.Repository<DomainEntities.Application>()
                        .GetByIdAsync(transaction.ApplicationEntityId); // Updated property name
                    
                    if (application != null)
                    {
                        // Assuming you have a workflow service to handle state transitions
                        // await _workflowService.TransitionToAsync(application, WorkflowStateType.PaymentCompleted);
                    }
                }

                await _unitOfWork.SaveChangesAsync();
                return (true, null);
            }
            catch (Exception ex)
            {
                return (false, $"Error processing callback: {ex.Message}");
            }
        }

        public async Task<(bool success, string? error)> VerifyPaymentAsync(string transactionId)
        {
            try
            {
                var parameters = new Dictionary<string, string>
                {
                    ["key"] = _key,
                    ["txnid"] = transactionId
                };

                var hashString = GenerateHash(parameters);
                parameters["hash"] = hashString;

                var client = new RestClient(_baseUrl);
                var request = new RestRequest("/payment/transaction", Method.Post);
                
                foreach (var param in parameters)
                {
                    request.AddParameter(param.Key, param.Value);
                }

                var response = await client.ExecuteAsync(request);

                if (!response.IsSuccessful)
                    return (false, $"Failed to verify payment: {response.ErrorMessage}");

                // Parse response and verify status
                // Implement according to Easebuzz API response format

                return (true, null);
            }
            catch (Exception ex)
            {
                return (false, $"Error verifying payment: {ex.Message}");
            }
        }

        private string GenerateHash(Dictionary<string, string> parameters)
        {
            var hashString = new StringBuilder();
            
            // Add key
            hashString.Append(_key);

            // Add required parameters in specific order
            var requiredParams = new[]
            {
                "txnid", "amount", "firstname", "email",
                "phone", "productinfo", "surl", "furl"
            };

            foreach (var param in requiredParams)
            {
                hashString.Append('|');
                if (parameters.ContainsKey(param))
                    hashString.Append(parameters[param]);
            }

            // Add salt
            hashString.Append('|');
            hashString.Append(_salt);

            // Generate SHA512 hash
            using var sha512 = SHA512.Create();
            var hashBytes = sha512.ComputeHash(Encoding.UTF8.GetBytes(hashString.ToString()));
            
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
        }

        private bool VerifyHash(Dictionary<string, string> parameters)
        {
            if (!parameters.ContainsKey("hash"))
                return false;

            var receivedHash = parameters["hash"];
            var computedHash = GenerateHash(parameters);

            return string.Equals(receivedHash, computedHash, StringComparison.OrdinalIgnoreCase);
        }
    }
}
