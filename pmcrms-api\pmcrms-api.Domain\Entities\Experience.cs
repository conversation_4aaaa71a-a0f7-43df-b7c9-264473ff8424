using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

/// <summary>
/// Experience Collection Entity as per PromptFile5.pdf [642bd626b101005f73b02ba11]
/// </summary>
public class Experience
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string CompanyName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Position { get; set; } = string.Empty;

    [Required]
    public int YearsOfExperience { get; set; }

    public string? CertificateFilePath { get; set; } // File path for Certificate

    [Required]
    public DateTime FromDate { get; set; }

    [Required]
    public DateTime ToDate { get; set; }

    /// <summary>
    /// Month difference between FromDate and ToDate
    /// </summary>
    public int MonthDifference => CalculateMonthDifference();

    /// <summary>
    /// Year difference between FromDate and ToDate
    /// </summary>
    public int YearDifference => CalculateYearDifference();

    /// <summary>
    /// Whether this experience meets the required criteria
    /// </summary>
    public bool ExperienceRequired { get; set; }

    /// <summary>
    /// Computed field - calculates total experience from FromDate and ToDate
    /// </summary>
    public string TotalExperience => CalculateTotalExperience();

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;

    private string CalculateTotalExperience()
    {
        var totalDays = (ToDate - FromDate).TotalDays;
        var years = (int)(totalDays / 365.25);
        var months = (int)((totalDays % 365.25) / 30.44);
        return $"{years} years {months} months";
    }

    private int CalculateMonthDifference()
    {
        var years = ToDate.Year - FromDate.Year;
        var months = ToDate.Month - FromDate.Month;
        return (years * 12) + months;
    }

    private int CalculateYearDifference()
    {
        var age = ToDate.Year - FromDate.Year;
        if (ToDate < FromDate.AddYears(age))
            age--;
        return age;
    }
}
