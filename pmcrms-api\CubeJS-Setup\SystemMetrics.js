// System Performance Metrics Cube
// This cube provides system-wide performance, usage, and health metrics

cube(`SystemMetrics`, {
  sql: `
    SELECT 
      -- Application metrics
      (SELECT COUNT(*) FROM Applications) as TotalApplications,
      (SELECT COUNT(*) FROM Applications WHERE CreatedDate >= DATEADD(day, -30, GETUTCDATE())) as ApplicationsLastMonth,
      (SELECT COUNT(*) FROM Applications WHERE CreatedDate >= DATEADD(day, -7, GETUTCDATE())) as ApplicationsLastWeek,
      (SELECT COUNT(*) FROM Applications WHERE CreatedDate >= DATEADD(day, -1, GETUTCDATE())) as ApplicationsToday,
      
      -- Status distribution
      (SELECT COUNT(*) FROM Applications WHERE Status = 1) as DocumentVerificationPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 2) as JuniorEngineerPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 3) as AssistantEngineerPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 4) as ExecutiveEngineerPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 5) as FinalApproved,
      (SELECT COUNT(*) FROM Applications WHERE Status = 6) as CityEngineerPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 7) as PaymentPending,
      (SELECT COUNT(*) FROM Applications WHERE Status = 8) as Rejected,
      
      -- User metrics
      (SELECT COUNT(*) FROM Users) as TotalUsers,
      (SELECT COUNT(*) FROM Users WHERE Role = 'ADMIN') as AdminUsers,
      (SELECT COUNT(*) FROM Users WHERE Role = 'OFFICER') as OfficerUsers,
      (SELECT COUNT(*) FROM Users WHERE Role = 'GUEST') as GuestUsers,
      (SELECT COUNT(*) FROM Users WHERE IsActive = 1) as ActiveUsers,
      
      -- Workflow metrics
      (SELECT COUNT(*) FROM ApplicationWorkflowHistory) as TotalWorkflowTransitions,
      (SELECT COUNT(*) FROM ApplicationWorkflowHistory WHERE ChangedDate >= DATEADD(day, -30, GETUTCDATE())) as TransitionsLastMonth,
      (SELECT COUNT(*) FROM ApplicationWorkflowHistory WHERE IsAutomaticTransition = 1) as AutomaticTransitions,
      (SELECT COUNT(*) FROM ApplicationWorkflowHistory WHERE IsAutomaticTransition = 0) as ManualTransitions,
      
      -- Performance metrics
      (SELECT AVG(CAST(DATEDIFF(day, CreatedDate, COALESCE(UpdatedDate, GETUTCDATE())) AS FLOAT)) 
       FROM Applications WHERE Status = 5) as AvgApprovalTimedays,
      (SELECT AVG(CAST(DATEDIFF(day, CreatedDate, UpdatedDate) AS FLOAT)) 
       FROM Applications WHERE Status = 8) as AvgRejectionTimeDays,
      
      -- System health indicators
      CASE 
        WHEN (SELECT COUNT(*) FROM Applications WHERE CreatedDate >= DATEADD(day, -1, GETUTCDATE())) > 0 THEN 1 
        ELSE 0 
      END as HasRecentActivity,
      
      CASE 
        WHEN (SELECT COUNT(*) FROM Applications WHERE Status IN (1,2,3,4,6,7)) > 0 THEN 1 
        ELSE 0 
      END as HasPendingApplications,
      
      -- Time dimensions
      GETUTCDATE() as MetricDate,
      YEAR(GETUTCDATE()) as MetricYear,
      MONTH(GETUTCDATE()) as MetricMonth,
      DAY(GETUTCDATE()) as MetricDay,
      DATEPART(hour, GETUTCDATE()) as MetricHour
  `,

  measures: {
    // Application volume metrics
    totalApplications: {
      sql: `TotalApplications`,
      type: `max`,
      title: `Total Applications`,
      description: `Total number of applications in system`
    },

    applicationsLastMonth: {
      sql: `ApplicationsLastMonth`,
      type: `max`,
      title: `Applications Last Month`,
      description: `Applications created in last 30 days`
    },

    applicationsLastWeek: {
      sql: `ApplicationsLastWeek`,
      type: `max`,
      title: `Applications Last Week`,
      description: `Applications created in last 7 days`
    },

    applicationsToday: {
      sql: `ApplicationsToday`,
      type: `max`,
      title: `Applications Today`,
      description: `Applications created today`
    },

    // Status distribution metrics
    pendingApplications: {
      sql: `DocumentVerificationPending + JuniorEngineerPending + AssistantEngineerPending + ExecutiveEngineerPending + CityEngineerPending + PaymentPending`,
      type: `number`,
      title: `Pending Applications`,
      description: `Total applications pending processing`
    },

    approvedApplications: {
      sql: `FinalApproved`,
      type: `max`,
      title: `Approved Applications`,
      description: `Total approved applications`
    },

    rejectedApplications: {
      sql: `Rejected`,
      type: `max`,
      title: `Rejected Applications`,
      description: `Total rejected applications`
    },

    approvalRate: {
      sql: `
        CASE 
          WHEN (FinalApproved + Rejected) > 0 THEN 
            (CAST(FinalApproved AS FLOAT) * 100.0 / CAST(FinalApproved + Rejected AS FLOAT))
          ELSE 0 
        END
      `,
      type: `avg`,
      title: `Approval Rate (%)`,
      description: `Percentage of completed applications that were approved`
    },

    // User metrics
    totalUsers: {
      sql: `TotalUsers`,
      type: `max`,
      title: `Total Users`,
      description: `Total registered users`
    },

    activeUsers: {
      sql: `ActiveUsers`,
      type: `max`,
      title: `Active Users`,
      description: `Currently active users`
    },

    userUtilizationRate: {
      sql: `
        CASE 
          WHEN TotalUsers > 0 THEN 
            (CAST(ActiveUsers AS FLOAT) * 100.0 / CAST(TotalUsers AS FLOAT))
          ELSE 0 
        END
      `,
      type: `avg`,
      title: `User Utilization Rate (%)`,
      description: `Percentage of users that are active`
    },

    // Performance metrics
    averageApprovalTime: {
      sql: `AvgApprovalTimedays`,
      type: `avg`,
      title: `Average Approval Time (Days)`,
      description: `Average time to approve applications`
    },

    averageRejectionTime: {
      sql: `AvgRejectionTimeDays`,
      type: `avg`,
      title: `Average Rejection Time (Days)`,
      description: `Average time to reject applications`
    },

    // Workflow efficiency metrics
    totalTransitions: {
      sql: `TotalWorkflowTransitions`,
      type: `max`,
      title: `Total Workflow Transitions`,
      description: `Total workflow state changes`
    },

    transitionsLastMonth: {
      sql: `TransitionsLastMonth`,
      type: `max`,
      title: `Transitions Last Month`,
      description: `Workflow transitions in last 30 days`
    },

    automationRate: {
      sql: `
        CASE 
          WHEN TotalWorkflowTransitions > 0 THEN 
            (CAST(AutomaticTransitions AS FLOAT) * 100.0 / CAST(TotalWorkflowTransitions AS FLOAT))
          ELSE 0 
        END
      `,
      type: `avg`,
      title: `Automation Rate (%)`,
      description: `Percentage of transitions that are automatic`
    },

    // System health score
    systemHealthScore: {
      sql: `
        (
          CASE WHEN HasRecentActivity = 1 THEN 25 ELSE 0 END +
          CASE WHEN HasPendingApplications = 1 THEN 25 ELSE 0 END +
          CASE WHEN ActiveUsers > 0 THEN 25 ELSE 0 END +
          CASE WHEN TotalApplications > 0 THEN 25 ELSE 0 END
        )
      `,
      type: `avg`,
      title: `System Health Score`,
      description: `Overall system health score (0-100)`
    },

    // Growth metrics
    monthlyGrowthRate: {
      sql: `
        CASE 
          WHEN (TotalApplications - ApplicationsLastMonth) > 0 THEN 
            (CAST(ApplicationsLastMonth AS FLOAT) * 100.0 / CAST(TotalApplications - ApplicationsLastMonth AS FLOAT))
          ELSE 0 
        END
      `,
      type: `avg`,
      title: `Monthly Growth Rate (%)`,
      description: `Month-over-month application growth rate`
    },

    weeklyGrowthRate: {
      sql: `
        CASE 
          WHEN ApplicationsLastWeek > 0 AND (ApplicationsLastMonth - ApplicationsLastWeek) > 0 THEN 
            ((CAST(ApplicationsLastWeek AS FLOAT) / 7.0) / (CAST(ApplicationsLastMonth - ApplicationsLastWeek AS FLOAT) / 23.0) - 1) * 100.0
          ELSE 0 
        END
      `,
      type: `avg`,
      title: `Weekly Growth Rate (%)`,
      description: `Week-over-week application growth rate`
    }
  },

  dimensions: {
    metricDate: {
      sql: `MetricDate`,
      type: `time`,
      title: `Metric Date`,
      description: `Date when metrics were calculated`
    },

    metricYear: {
      sql: `MetricYear`,
      type: `number`,
      title: `Metric Year`,
      description: `Year of metric calculation`
    },

    metricMonth: {
      sql: `MetricMonth`,
      type: `number`,
      title: `Metric Month`,
      description: `Month of metric calculation`
    },

    systemStatus: {
      sql: `
        CASE 
          WHEN HasRecentActivity = 1 AND ActiveUsers > 0 AND TotalApplications > 0 THEN 'Healthy'
          WHEN HasRecentActivity = 1 AND ActiveUsers > 0 THEN 'Active'
          WHEN TotalApplications > 0 THEN 'Stable'
          ELSE 'Inactive'
        END
      `,
      type: `string`,
      title: `System Status`,
      description: `Overall system status assessment`
    },

    workloadLevel: {
      sql: `
        CASE 
          WHEN (DocumentVerificationPending + JuniorEngineerPending + AssistantEngineerPending + ExecutiveEngineerPending + CityEngineerPending + PaymentPending) > 100 THEN 'High'
          WHEN (DocumentVerificationPending + JuniorEngineerPending + AssistantEngineerPending + ExecutiveEngineerPending + CityEngineerPending + PaymentPending) > 50 THEN 'Medium'
          WHEN (DocumentVerificationPending + JuniorEngineerPending + AssistantEngineerPending + ExecutiveEngineerPending + CityEngineerPending + PaymentPending) > 0 THEN 'Low'
          ELSE 'None'
        END
      `,
      type: `string`,
      title: `Workload Level`,
      description: `Current system workload level`
    },

    performanceLevel: {
      sql: `
        CASE 
          WHEN AvgApprovalTimedays <= 7 THEN 'Excellent'
          WHEN AvgApprovalTimedays <= 14 THEN 'Good'
          WHEN AvgApprovalTimedays <= 21 THEN 'Fair'
          ELSE 'Needs Improvement'
        END
      `,
      type: `string`,
      title: `Performance Level`,
      description: `System performance assessment`
    },

    userEngagementLevel: {
      sql: `
        CASE 
          WHEN CAST(ActiveUsers AS FLOAT) / NULLIF(CAST(TotalUsers AS FLOAT), 0) >= 0.8 THEN 'High'
          WHEN CAST(ActiveUsers AS FLOAT) / NULLIF(CAST(TotalUsers AS FLOAT), 0) >= 0.6 THEN 'Medium'
          WHEN CAST(ActiveUsers AS FLOAT) / NULLIF(CAST(TotalUsers AS FLOAT), 0) >= 0.3 THEN 'Low'
          ELSE 'Very Low'
        END
      `,
      type: `string`,
      title: `User Engagement Level`,
      description: `Level of user engagement with system`
    },

    // Bottleneck identification
    primaryBottleneck: {
      sql: `
        CASE 
          WHEN DocumentVerificationPending >= ALL (JuniorEngineerPending, AssistantEngineerPending, ExecutiveEngineerPending, CityEngineerPending, PaymentPending) THEN 'Document Verification'
          WHEN JuniorEngineerPending >= ALL (DocumentVerificationPending, AssistantEngineerPending, ExecutiveEngineerPending, CityEngineerPending, PaymentPending) THEN 'Junior Engineer Review'
          WHEN AssistantEngineerPending >= ALL (DocumentVerificationPending, JuniorEngineerPending, ExecutiveEngineerPending, CityEngineerPending, PaymentPending) THEN 'Assistant Engineer Review'
          WHEN ExecutiveEngineerPending >= ALL (DocumentVerificationPending, JuniorEngineerPending, AssistantEngineerPending, CityEngineerPending, PaymentPending) THEN 'Executive Engineer Review'
          WHEN CityEngineerPending >= ALL (DocumentVerificationPending, JuniorEngineerPending, AssistantEngineerPending, ExecutiveEngineerPending, PaymentPending) THEN 'City Engineer Review'
          WHEN PaymentPending >= ALL (DocumentVerificationPending, JuniorEngineerPending, AssistantEngineerPending, ExecutiveEngineerPending, CityEngineerPending) THEN 'Payment Processing'
          ELSE 'No Bottleneck'
        END
      `,
      type: `string`,
      title: `Primary Bottleneck`,
      description: `Stage with highest pending applications`
    }
  },

  segments: {
    healthySystem: {
      sql: `${CUBE}.HasRecentActivity = 1 AND ${CUBE}.ActiveUsers > 0 AND ${CUBE}.TotalApplications > 0`,
      title: `Healthy System`,
      description: `System showing healthy activity levels`
    },

    highWorkload: {
      sql: `(${CUBE}.DocumentVerificationPending + ${CUBE}.JuniorEngineerPending + ${CUBE}.AssistantEngineerPending + ${CUBE}.ExecutiveEngineerPending + ${CUBE}.CityEngineerPending + ${CUBE}.PaymentPending) > 50`,
      title: `High Workload`,
      description: `System under high processing load`
    },

    goodPerformance: {
      sql: `${CUBE}.AvgApprovalTimedays <= 14`,
      title: `Good Performance`,
      description: `System performing within acceptable time limits`
    },

    highApprovalRate: {
      sql: `(${CUBE}.FinalApproved * 100.0 / NULLIF(${CUBE}.FinalApproved + ${CUBE}.Rejected, 0)) >= 70`,
      title: `High Approval Rate`,
      description: `System with high application approval rate`
    }
  },

  preAggregations: {
    // Real-time system overview
    systemOverview: {
      measures: [
        CUBE.totalApplications,
        CUBE.pendingApplications,
        CUBE.approvedApplications,
        CUBE.rejectedApplications,
        CUBE.approvalRate,
        CUBE.systemHealthScore
      ],
      dimensions: [
        CUBE.systemStatus,
        CUBE.workloadLevel,
        CUBE.performanceLevel
      ],
      refreshKey: {
        every: `10 minutes`
      }
    },

    // Historical performance tracking
    performanceHistory: {
      measures: [
        CUBE.averageApprovalTime,
        CUBE.averageRejectionTime,
        CUBE.approvalRate,
        CUBE.monthlyGrowthRate
      ],
      dimensions: [
        CUBE.performanceLevel,
        CUBE.systemStatus
      ],
      timeDimension: CUBE.metricDate,
      granularity: `day`,
      refreshKey: {
        every: `1 hour`
      }
    },

    // User engagement metrics
    userMetrics: {
      measures: [
        CUBE.totalUsers,
        CUBE.activeUsers,
        CUBE.userUtilizationRate
      ],
      dimensions: [
        CUBE.userEngagementLevel
      ],
      refreshKey: {
        every: `30 minutes`
      }
    },

    // Bottleneck analysis
    bottleneckAnalysis: {
      measures: [
        CUBE.pendingApplications,
        CUBE.totalTransitions,
        CUBE.automationRate
      ],
      dimensions: [
        CUBE.primaryBottleneck,
        CUBE.workloadLevel
      ],
      refreshKey: {
        every: `15 minutes`
      }
    }
  }
});
