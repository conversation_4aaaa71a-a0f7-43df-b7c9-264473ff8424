using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using pmcrms_api.Domain.Entities;
using ApplicationEntity = pmcrms_api.Domain.Entities.Application;

namespace pmcrms_api.Infrastructure.Data.Configurations;

public class ApplicationConfiguration : IEntityTypeConfiguration<ApplicationEntity>
{
    public void Configure(EntityTypeBuilder<ApplicationEntity> builder)
    {
        builder.ToTable("Applications");
        
        builder.HasKey(a => a.Id);
        
        builder.Property(a => a.ApplicationNumber)
            .IsRequired()
            .HasMaxLength(50);
            
        // ApplicantName is now a computed property [NotMapped], no configuration needed
        
        builder.Property(a => a.EmailAddress)
            .IsRequired()
            .HasMaxLength(150);

        // Configure new personal information fields
        builder.Property(a => a.FirstName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(a => a.LastName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(a => a.MiddleName)
            .HasMaxLength(100);

        builder.Property(a => a.MotherName)
            .IsRequired()
            .HasMaxLength(150);

        builder.Property(a => a.MobileNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(a => a.Place)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(a => a.RoomNumber)
            .IsRequired()
            .HasMaxLength(50);

        // Configure enum properties
        builder.Property(a => a.BloodGroup)
            .HasConversion<string>();

        builder.Property(a => a.Gender)
            .HasConversion<string>();

        builder.Property(a => a.Position)
            .HasConversion<string>();
            
        // Remove Purpose property as it's not in updated entity
        // builder.Property(a => a.Purpose)
        //     .IsRequired()
        //     .HasMaxLength(500);
            
        builder.Property(a => a.Amount)
            .HasPrecision(18, 2);
            
        builder.Property(a => a.SubmissionDate)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.HasIndex(a => a.ApplicationNumber)
            .IsUnique();
            
        // Relationship with User
        builder.HasOne(a => a.User)
            .WithMany()
            .HasForeignKey(a => a.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
