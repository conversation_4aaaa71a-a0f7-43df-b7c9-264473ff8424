import React, { useEffect, useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { AdminAuthService } from '../lib/adminAuth'
import { Loader2 } from 'lucide-react'

interface AdminProtectedRouteProps {
  children: React.ReactNode
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const location = useLocation()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // First check if we have a token
        const hasToken = AdminAuthService.isAuthenticated()
        
        if (!hasToken) {
          console.log('No admin token found')
          setIsAuthenticated(false)
          setIsLoading(false)
          return
        }

        console.log('Admin token found, user is authenticated')
        setIsAuthenticated(true)
        
        // TODO: Uncomment this when backend token verification is ready
        // const isValid = await AdminAuthService.verifyToken()
        // setIsAuthenticated(isValid)
      } catch (error) {
        console.error('Auth check failed:', error)
        setIsAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Verifying authentication...</span>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    // Redirect to admin login with return path
    return <Navigate to="/admin/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}

export default AdminProtectedRoute
