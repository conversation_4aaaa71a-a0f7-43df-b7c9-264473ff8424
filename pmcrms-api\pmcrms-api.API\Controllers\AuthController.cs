using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using pmcrms_api.Application.DTOs.Auth;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using pmcrms_api.Infrastructure.Data;
using pmcrms_api.Infrastructure.Services;
using System.Net;
using System.Security.Claims;
using ApplicationEntity = pmcrms_api.Domain.Entities.Application;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Authentication and authorization endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Tags("Authentication")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEmailService _emailService;
    private readonly ApplicationDbContext _context;

    public AuthController(IAuthService authService, IUnitOfWork unitOfWork, IEmailService emailService, ApplicationDbContext context)
    {
        _authService = authService;
        _unitOfWork = unitOfWork;
        _emailService = emailService;
        _context = context;
    }

    /// <summary>
    /// Send OTP to user's email for authentication
    /// </summary>
    /// <param name="request">OTP request containing user email</param>
    /// <returns>Success message when OTP is sent</returns>
    /// <response code="200">OTP sent successfully</response>
    /// <response code="400">Invalid email or request</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("user/send-otp")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SendOtp([FromBody] SendOtpRequest request)
    {
        // TODO: Implement OTP generation and sending
        return Ok(new { message = "OTP sent successfully" });
    }

    /// <summary>
    /// Verify OTP provided by user
    /// </summary>
    /// <param name="request">OTP verification request</param>
    /// <returns>Success message when OTP is verified</returns>
    /// <response code="200">OTP verified successfully</response>
    /// <response code="400">Invalid OTP or request</response>
    /// <response code="401">OTP expired or invalid</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("user/verify-otp")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> VerifyOtp([FromBody] VerifyOtpRequest request)
    {
        // TODO: Implement OTP verification
        return Ok(new { message = "OTP verified successfully" });
    }

    /// <summary>
    /// Send OTP to guest for certificate access
    /// </summary>
    /// <param name="request">Guest OTP request containing application number and mobile number</param>
    /// <returns>Success message when OTP is sent</returns>
    /// <response code="200">OTP sent successfully</response>
    /// <response code="400">Invalid application number or mobile number</response>
    /// <response code="404">Application not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("guest/send-otp")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> SendGuestOtp([FromBody] SendGuestOtpRequest request)
    {
        if (string.IsNullOrEmpty(request.Email))
        {
            return BadRequest(new { message = "Email is required" });
        }
        // Generate 6-digit OTP
        var otp = new Random().Next(100000, 999999).ToString();
        // Send OTP via real email
        var subject = "Your OTP Code";
        var body = $"Your OTP code is <b>{otp}</b>. It is valid for 5 minutes.";
        await _emailService.SendEmailAsync(request.Email, subject, body, true);
        return Ok(new {
            message = "OTP sent successfully to email"
        });
    }

    /// <summary>
    /// Verify OTP provided by guest for certificate access
    /// </summary>
    /// <param name="request">Guest OTP verification request</param>
    /// <returns>Temporary token for certificate access</returns>
    /// <response code="200">OTP verified successfully, access granted</response>
    /// <response code="400">Invalid OTP or request</response>
    /// <response code="401">OTP expired or invalid</response>
    /// <response code="404">Application not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("guest/verify-otp")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> VerifyGuestOtp([FromBody] VerifyGuestOtpRequest request)
    {
        if (string.IsNullOrEmpty(request.Identifier) || string.IsNullOrEmpty(request.Otp) || request.Otp.Length != 6)
        {
            return BadRequest(new { message = "Email and valid 6-digit OTP are required" });
        }
        // TODO: Validate OTP from cache/db
        // Create temporary guest user
        var guestUser = new User
        {
            Id = -1,
            Email = request.Identifier,
            FirstName = "Guest",
            LastName = "User",
            Role = new Role { Name = "Guest" }
        };
        var token = await _authService.GenerateJwtToken(guestUser);
        return Ok(new {
            message = "OTP verified successfully",
            accessToken = token,
            expiresIn = 1800 // 30 minutes
        });
    }

    /// <summary>
    /// Authenticate a guest user
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>Authentication response with JWT token</returns>
    /// <response code="200">Login successful</response>
    /// <response code="401">Invalid credentials</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("guest/login")]
    [ProducesResponseType(typeof(AuthResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<AuthResponse>> GuestLogin([FromBody] LoginRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null || !_authService.VerifyPassword(request.Password, user.Password))
        {
            return Unauthorized(new { message = "Invalid credentials" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        var token = await _authService.GenerateJwtToken(user);
        var userData = new {
            id = user.Id,
            email = user.Email,
            firstName = user.FirstName,
            lastName = user.LastName,
            role = user.Role?.Name ?? "Unknown"
        };
        return Ok(new { 
            success = true, 
            data = new { 
                user = userData, 
                token = token, 
                refreshToken = (string?)null 
            }, 
            message = "Login successful" 
        });
    }

    /// <summary>
    /// Authenticate an officer user
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>Authentication response with JWT token</returns>
    /// <response code="200">Login successful</response>
    /// <response code="401">Invalid credentials</response>
    /// <response code="403">User does not have officer privileges</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officer/login")]
    [ProducesResponseType(typeof(AuthResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.Forbidden)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<AuthResponse>> OfficerLogin([FromBody] LoginRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null || !_authService.VerifyPassword(request.Password, user.Password))
        {
            return Unauthorized(new { success = false, message = "Invalid credentials" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        if (user.Role?.Name == "User")
        {
            return Forbid();
        }

        var token = await _authService.GenerateJwtToken(user);
        var authResponse = new AuthResponse(token, user.Email, user.FirstName, user.LastName, user.Role?.Name ?? "Unknown");
        return Ok(new { success = true, data = authResponse, message = "Login successful" });
    }

    /// <summary>
    /// Set password for an officer user
    /// </summary>
    /// <param name="request">Password setting request</param>
    /// <returns>Success message when password is set</returns>
    /// <response code="200">Password set successfully</response>
    /// <response code="400">Password validation failed or passwords don't match</response>
    /// <response code="404">User not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officer/set-password")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SetOfficerPassword([FromBody] SetPasswordRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null)
        {
            return NotFound(new { message = "User not found" });
        }

        if (request.Password != request.ConfirmPassword)
        {
            return BadRequest(new { message = "Passwords do not match" });
        }

        user.Password = _authService.HashPassword(request.Password);
        _unitOfWork.Repository<User>().Update(user);
        await _unitOfWork.SaveChangesAsync();

        return Ok(new { message = "Password set successfully" });
    }

    /// <summary>
    /// Check officer status to determine if password setup is needed
    /// </summary>
    /// <param name="request">Email to check</param>
    /// <returns>Status indicating whether password setup is needed</returns>
    /// <response code="200">Status check successful</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officer/check-status")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> CheckOfficerStatus([FromBody] CheckStatusRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null)
        {
            return NotFound(new { success = false, message = "Officer not found" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        // Check if user is an officer (not a regular user)
        if (user.Role?.Name == "User")
        {
            return NotFound(new { success = false, message = "Officer not found" });
        }

        // Check if password is set (not null or empty)
        bool needsPasswordSetup = string.IsNullOrEmpty(user.Password);

        return Ok(new { success = true, data = new { needsPasswordSetup }, message = "Status checked successfully" });
    }

    /// <summary>
    /// Validate setup token for officer password setup
    /// </summary>
    /// <param name="request">Token validation request</param>
    /// <returns>Token validation result with officer info</returns>
    /// <response code="200">Token is valid</response>
    /// <response code="400">Invalid or expired token</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officer/validate-setup-token")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> ValidateSetupToken([FromBody] ValidateSetupTokenRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null)
        {
            return NotFound(new { success = false, message = "Officer not found" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        // Check if user is an officer (not a regular user)
        if (user.Role?.Name == "User")
        {
            return NotFound(new { success = false, message = "Officer not found" });
        }

        // Validate the reset token
        if (user.ResetToken != request.Token || user.ResetTokenExpiry < DateTime.UtcNow)
        {
            return BadRequest(new { success = false, message = "Invalid or expired token" });
        }

        var tokenValidationResponse = new
        {
            isValid = true,
            email = user.Email,
            firstName = user.FirstName,
            lastName = user.LastName,
            role = user.Role?.Name ?? "Unknown"
        };

        return Ok(new { success = true, data = tokenValidationResponse, message = "Token is valid" });
    }

    /// <summary>
    /// Set password using setup token
    /// </summary>
    /// <param name="request">Password setup request with token</param>
    /// <returns>Success message when password is set</returns>
    /// <response code="200">Password set successfully</response>
    /// <response code="400">Invalid token or password validation failed</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("officer/setup-password-with-token")]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> SetupPasswordWithToken([FromBody] SetupPasswordWithTokenRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null)
        {
            return NotFound(new { success = false, message = "Officer not found" });
        }

        // Validate the reset token
        if (user.ResetToken != request.Token || user.ResetTokenExpiry < DateTime.UtcNow)
        {
            return BadRequest(new { success = false, message = "Invalid or expired token" });
        }

        if (request.Password != request.ConfirmPassword)
        {
            return BadRequest(new { success = false, message = "Passwords do not match" });
        }

        if (string.IsNullOrEmpty(request.Password) || request.Password.Length < 8)
        {
            return BadRequest(new { success = false, message = "Password must be at least 8 characters long" });
        }

        // Hash and set the password
        user.Password = _authService.HashPassword(request.Password);
        user.ResetToken = null; // Clear the token after use
        user.ResetTokenExpiry = null;
        
        await _unitOfWork.SaveChangesAsync();

        return Ok(new { success = true, message = "Password set successfully" });
    }

    /// <summary>
    /// Get current officer profile
    /// </summary>
    /// <returns>Officer profile information</returns>
    /// <response code="200">Profile retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("officer/profile")]
    [Authorize]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> GetOfficerProfile()
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(currentUserId);

            if (user == null)
            {
                return NotFound(new { success = false, message = "Officer not found" });
            }

            var userWithRole = await _context.Users
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == currentUserId);

            if (userWithRole == null)
            {
                return NotFound(new { success = false, message = "Officer profile not found" });
            }

            var profile = new
            {
                id = userWithRole.Id,
                email = userWithRole.Email,
                firstName = userWithRole.FirstName,
                lastName = userWithRole.LastName,
                phoneNumber = userWithRole.PhoneNumber,
                role = userWithRole.Role?.Name,
                isActive = userWithRole.IsActive,
                createdAt = userWithRole.CreatedAt,
                lastLogin = userWithRole.LastLogin
            };

            return Ok(new { success = true, data = profile, message = "Profile retrieved successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve profile", error = ex.Message });
        }
    }

    /// <summary>
    /// Update officer profile
    /// </summary>
    /// <param name="request">Profile update request</param>
    /// <returns>Success message when profile is updated</returns>
    /// <response code="200">Profile updated successfully</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="404">Officer not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("officer/profile")]
    [Authorize]
    [ProducesResponseType(typeof(object), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<IActionResult> UpdateOfficerProfile([FromBody] UpdateOfficerProfileRequest request)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(currentUserId);

            if (user == null)
            {
                return NotFound(new { success = false, message = "Officer not found" });
            }

            // Validate input
            if (string.IsNullOrWhiteSpace(request.FirstName) || string.IsNullOrWhiteSpace(request.LastName))
            {
                return BadRequest(new { success = false, message = "First name and last name are required" });
            }

            // Update profile information
            user.FirstName = request.FirstName.Trim();
            user.LastName = request.LastName.Trim();
            user.PhoneNumber = request.PhoneNumber?.Trim();

            _unitOfWork.Repository<User>().Update(user);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new { success = true, message = "Profile updated successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to update profile", error = ex.Message });
        }
    }

    /// <summary>
    /// Authenticate an admin user
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>Authentication response with JWT token</returns>
    /// <response code="200">Login successful</response>
    /// <response code="401">Invalid credentials</response>
    /// <response code="403">User does not have admin privileges</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("admin/login")]
    [ProducesResponseType(typeof(AdminLoginResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.Forbidden)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> AdminLogin([FromBody] LoginRequest request)
    {
        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
        var user = users.FirstOrDefault();

        if (user == null || !_authService.VerifyPassword(request.Password, user.Password))
        {
            return Unauthorized(new { success = false, message = "Invalid credentials" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        if (user.Role?.Name != "Admin")
        {
            return Forbid();
        }

        var token = await _authService.GenerateJwtToken(user);
        
        return Ok(new AdminLoginResponse
        {
            Success = true,
            Data = new AdminLoginData
            {
                Token = token,
                User = new AdminUser
                {
                    Id = user.Id,
                    Email = user.Email,
                    Role = user.Role.Name,
                    FirstName = user.FirstName,
                    LastName = user.LastName
                }
            },
            Message = "Login successful"
        });
    }

    /// <summary>
    /// Verify admin token
    /// </summary>
    /// <returns>Token verification response</returns>
    /// <response code="200">Token is valid</response>
    /// <response code="401">Token is invalid or expired</response>
    [HttpGet("admin/verify")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(TokenVerificationResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    public async Task<ActionResult> VerifyAdminToken()
    {
        var userEmail = User.Identity?.Name;
        if (string.IsNullOrEmpty(userEmail))
        {
            return Unauthorized(new { success = false, message = "Invalid token" });
        }

        var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == userEmail);
        var user = users.FirstOrDefault();

        if (user == null)
        {
            return Unauthorized(new { success = false, message = "User not found" });
        }

        // Load the Role if it's null
        if (user.Role == null)
        {
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Id == user.RoleId);
            user.Role = roles.FirstOrDefault();
        }

        return Ok(new TokenVerificationResponse
        {
            Success = true,
            Data = new TokenVerificationData
            {
                Valid = true,
                User = new AdminUser
                {
                    Id = user.Id,
                    Email = user.Email,
                    Role = user.Role?.Name ?? "Unknown"
                }
            }
        });
    }

    /// <summary>
    /// Get current user ID from JWT token claims
    /// </summary>
    /// <returns>Current user ID</returns>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }
}
