import axios from 'axios'
import type { ApiResponse } from '../types/types'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5048/api'

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Check for regular auth token first
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    } else {
      // Check for guest session
      const guestSession = localStorage.getItem('guest_session')
      if (guestSession) {
        try {
          const sessionData = JSON.parse(guestSession)
          if (sessionData.token) {
            config.headers.Authorization = `Bearer ${sessionData.token}`
          } else if (sessionData.email) {
            // If no token, send email as guest identifier
            config.headers['X-Guest-Email'] = sessionData.email
          }
        } catch (error) {
          console.error('Error parsing guest session:', error)
        }
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API helper functions
export const api = {
  get: <T>(url: string, params?: any): Promise<ApiResponse<T>> =>
    apiClient.get(url, { params }).then(res => res.data),
    
  post: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.post(url, data).then(res => res.data),
    
  put: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.put(url, data).then(res => res.data),
    
  patch: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.patch(url, data).then(res => res.data),
    
  delete: <T>(url: string): Promise<ApiResponse<T>> =>
    apiClient.delete(url).then(res => res.data),

  // File upload with progress
  uploadFile: <T>(url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> =>
    apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    }).then(res => res.data),

  // Download file
  downloadFile: (url: string, filename?: string): Promise<void> =>
    apiClient.get(url, {
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
}

// Guest Authentication API
export const guestAuth = {
  sendOtp: (email: string) => 
    api.post<{ message: string }>('/auth/guest/send-otp', { Email: email }),
    
  verifyOtp: (email: string, otp: string) => 
    api.post<{ message: string; accessToken: string; expiresIn: number }>('/auth/guest/verify-otp', { Identifier: email, otp }),
    
  getMyApplications: (accessToken: string) => 
    apiClient.get<any[]>('/applications/guest/my-applications', {
      headers: { Authorization: `Bearer ${accessToken}` }
    }),
    
  getApplicationStatus: (applicationNumber: string, accessToken: string) => 
    apiClient.get<any>(`/applications/guest/${applicationNumber}`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    }),
    
  downloadCertificate: (applicationNumber: string, accessToken: string) => 
    apiClient.get(`/applications/guest/${applicationNumber}/certificate`, {
      headers: { Authorization: `Bearer ${accessToken}` },
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `certificate-${applicationNumber}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
}

export default apiClient
