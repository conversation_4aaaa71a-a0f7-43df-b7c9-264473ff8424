namespace pmcrms_api.Application.DTOs.Reports
{
    /// <summary>
    /// Base report response with metadata
    /// </summary>
    public class BaseReportResponse
    {
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public string GeneratedBy { get; set; } = string.Empty;
        public ReportPeriod Period { get; set; } = new();
    }

    /// <summary>
    /// Report period information
    /// </summary>
    public class ReportPeriod
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Applications report response
    /// </summary>
    public class ApplicationsReportResponse : BaseReportResponse
    {
        public ApplicationSummary Summary { get; set; } = new();
        public List<ApplicationReportItem> Applications { get; set; } = new();
        public List<ChartDataPoint> StatusChart { get; set; } = new();
        public List<TrendDataPoint> SubmissionTrend { get; set; } = new();
    }

    /// <summary>
    /// Application summary statistics
    /// </summary>
    public class ApplicationSummary
    {
        public int TotalApplications { get; set; }
        public int SubmittedApplications { get; set; }
        public int UnderReviewApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public int RejectedApplications { get; set; }
        public int PendingPaymentApplications { get; set; }
        public int CompletedApplications { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public double ApprovalRate { get; set; }
    }

    /// <summary>
    /// Individual application report item
    /// </summary>
    public class ApplicationReportItem
    {
        public int Id { get; set; }
        public string ApplicationNumber { get; set; } = string.Empty;
        public string ApplicantName { get; set; } = string.Empty;
        public string ApplicantEmail { get; set; } = string.Empty;
        public string Purpose { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime SubmissionDate { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public int ProcessingDays { get; set; }
    }

    /// <summary>
    /// Approved applications report response
    /// </summary>
    public class ApprovedApplicationsReportResponse : BaseReportResponse
    {
        public ApprovedApplicationsSummary Summary { get; set; } = new();
        public List<ApprovedApplicationItem> ApprovedApplications { get; set; } = new();
        public List<ChartDataPoint> MonthlyApprovals { get; set; } = new();
        public List<ChartDataPoint> AmountDistribution { get; set; } = new();
    }

    /// <summary>
    /// Approved applications summary
    /// </summary>
    public class ApprovedApplicationsSummary
    {
        public int TotalApproved { get; set; }
        public decimal TotalApprovedAmount { get; set; }
        public decimal AverageApprovalTime { get; set; }
        public decimal AverageAmount { get; set; }
        public int FastestApprovalDays { get; set; }
        public int SlowestApprovalDays { get; set; }
    }

    /// <summary>
    /// Approved application item
    /// </summary>
    public class ApprovedApplicationItem
    {
        public int Id { get; set; }
        public string ApplicationNumber { get; set; } = string.Empty;
        public string ApplicantName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime SubmissionDate { get; set; }
        public DateTime ApprovalDate { get; set; }
        public int ApprovalDays { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Application analytics response
    /// </summary>
    public class ApplicationAnalyticsResponse : BaseReportResponse
    {
        public AnalyticsSummary Summary { get; set; } = new();
        public List<ChartDataPoint> StatusDistribution { get; set; } = new();
        public List<TrendDataPoint> SubmissionTrend { get; set; } = new();
        public List<TrendDataPoint> ApprovalTrend { get; set; } = new();
        public List<ChartDataPoint> AmountRangeDistribution { get; set; } = new();
        public List<ChartDataPoint> ProcessingTimeDistribution { get; set; } = new();
        public List<TopApplicant> TopApplicants { get; set; } = new();
    }

    /// <summary>
    /// Analytics summary
    /// </summary>
    public class AnalyticsSummary
    {
        public int TotalApplications { get; set; }
        public double ApprovalRate { get; set; }
        public double RejectionRate { get; set; }
        public double AverageProcessingDays { get; set; }
        public decimal TotalRequestedAmount { get; set; }
        public decimal TotalApprovedAmount { get; set; }
        public decimal AverageRequestAmount { get; set; }
    }

    /// <summary>
    /// Top applicant data
    /// </summary>
    public class TopApplicant
    {
        public string ApplicantName { get; set; } = string.Empty;
        public string ApplicantEmail { get; set; } = string.Empty;
        public int ApplicationCount { get; set; }
        public decimal TotalAmount { get; set; }
        public double ApprovalRate { get; set; }
    }

    /// <summary>
    /// Successful transactions report response
    /// </summary>
    public class SuccessfulTransactionsReportResponse : BaseReportResponse
    {
        public TransactionSummary Summary { get; set; } = new();
        public List<TransactionReportItem> Transactions { get; set; } = new();
        public List<TrendDataPoint> DailyRevenue { get; set; } = new();
        public List<ChartDataPoint> PaymentMethodDistribution { get; set; } = new();
    }

    /// <summary>
    /// Failed transactions report response
    /// </summary>
    public class FailedTransactionsReportResponse : BaseReportResponse
    {
        public FailedTransactionSummary Summary { get; set; } = new();
        public List<FailedTransactionItem> FailedTransactions { get; set; } = new();
        public List<ChartDataPoint> FailureReasons { get; set; } = new();
        public List<TrendDataPoint> FailureTrend { get; set; } = new();
    }

    /// <summary>
    /// Transaction summary
    /// </summary>
    public class TransactionSummary
    {
        public int TotalTransactions { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
        public int UniqueUsers { get; set; }
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// Failed transaction summary
    /// </summary>
    public class FailedTransactionSummary
    {
        public int TotalFailedTransactions { get; set; }
        public decimal TotalFailedAmount { get; set; }
        public double FailureRate { get; set; }
        public string MostCommonFailureReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Transaction report item
    /// </summary>
    public class TransactionReportItem
    {
        public int Id { get; set; }
        public string PaymentNumber { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public string ApplicationNumber { get; set; } = string.Empty;
        public string ApplicantName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Failed transaction item
    /// </summary>
    public class FailedTransactionItem
    {
        public int Id { get; set; }
        public string PaymentNumber { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public DateTime AttemptDate { get; set; }
        public string FailureReason { get; set; } = string.Empty;
        public string ApplicationNumber { get; set; } = string.Empty;
        public string ApplicantName { get; set; } = string.Empty;
    }

    /// <summary>
    /// User activity report response
    /// </summary>
    public class UserActivityReportResponse : BaseReportResponse
    {
        public UserActivitySummary Summary { get; set; } = new();
        public List<UserActivityItem> UserActivities { get; set; } = new();
        public List<ChartDataPoint> UserTypeDistribution { get; set; } = new();
        public List<TrendDataPoint> LoginTrend { get; set; } = new();
        public List<TopActiveUser> TopActiveUsers { get; set; } = new();
    }

    /// <summary>
    /// User activity summary
    /// </summary>
    public class UserActivitySummary
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int NewUsersInPeriod { get; set; }
        public double ActivityRate { get; set; }
        public int TotalLogins { get; set; }
        public double AverageLoginsPerUser { get; set; }
    }

    /// <summary>
    /// User activity item
    /// </summary>
    public class UserActivityItem
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public DateTime LastLoginDate { get; set; }
        public int LoginCount { get; set; }
        public int ApplicationsSubmitted { get; set; }
        public bool IsActive { get; set; }
        public DateTime JoinDate { get; set; }
    }

    /// <summary>
    /// Top active user
    /// </summary>
    public class TopActiveUser
    {
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int ActivityScore { get; set; }
        public int LoginCount { get; set; }
        public int ApplicationCount { get; set; }
    }

    /// <summary>
    /// Chart data point for visualizations
    /// </summary>
    public class ChartDataPoint
    {
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public int Count { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    /// <summary>
    /// Trend data point for time-series charts
    /// </summary>
    public class TrendDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public int Count { get; set; }
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// Export request parameters
    /// </summary>
    public class ExportRequest
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Format { get; set; } = "excel"; // excel, pdf
        public bool IncludeCharts { get; set; } = true;
        public List<string> Columns { get; set; } = new();
    }
}
