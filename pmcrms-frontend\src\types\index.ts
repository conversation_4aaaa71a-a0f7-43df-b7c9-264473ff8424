// User and Authentication Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  role: UserRole
  isEmailVerified: boolean
  createdAt: string
  updatedAt: string
}

export type UserRole = 
  | 'PUBLIC_USER'
  | 'JRENGG_ARCH'
  | 'JRENGG_STRU'
  | 'JRENGG_LICE'
  | 'JRENGG_SUPER1'
  | 'JRENGG_SUPER2'
  | 'ASSIENGG_ARCH'
  | 'ASSIENGG_STRU'
  | 'ASSIENGG_LICE'
  | 'ASSIENGG_SUPER1'
  | 'ASSIENGG_SUPER2'
  | 'EXECUTIVE_ENGINEER'
  | 'CITY_ENGINEER'
  | 'CLERK'
  | 'OFFLINE_PAYMENT_OFFICER'
  | 'ADMIN'

export type PositionType = 
  | 'Architect'
  | 'License Engineer'
  | 'Structural Engineer'
  | 'Supervisor1'
  | 'Supervisor2'

// Workflow States
export type WorkflowState = 
  | 'DOCUMENT_VERIFICATION_PENDING'
  | 'JUNIOR_ENGINEER_PENDING'
  | 'ASSISTANT_ENGINEER_PENDING'
  | 'EXECUTIVE_ENGINEER_PENDING'
  | 'CITY_ENGINEER_PENDING'
  | 'PAYMENT_PENDING'
  | 'CLERK_PENDING'
  | 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING'
  | 'CITY_DIGITAL_SIGNATURE_PENDING'
  | 'FINAL_APPROVE'
  | 'REJECTED'

// Application Types
export interface Application {
  id: string
  applicationNumber: string
  positionType: PositionType
  currentStatus: WorkflowState
  applicantInfo: ApplicantInfo
  addresses: Address[]
  experiences: Experience[]
  qualifications: Qualification[]
  documents: Document[]
  payments: Payment[]
  workflowHistory: WorkflowHistoryEntry[]
  createdAt: string
  updatedAt: string
  submittedAt?: string
  completedAt?: string
}

export interface ApplicantInfo {
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  dateOfBirth: string
  gender: 'Male' | 'Female' | 'Other'
  aadhaarNumber?: string
  panNumber?: string
}

export interface Address {
  id: string
  type: 'Permanent' | 'Local'
  addressLine1: string
  addressLine2?: string
  city: string
  state: string
  pinCode: string
  country: string
}

export interface Experience {
  id: string
  companyName: string
  designation: string
  startDate: string
  endDate?: string
  isCurrentJob: boolean
  description?: string
  certificateUrl?: string
}

export interface Qualification {
  id: string
  degree: string
  institution: string
  university: string
  yearOfPassing: string
  percentage?: number
  certificateUrl?: string
}

export interface Document {
  id: string
  type: DocumentType
  fileName: string
  fileUrl: string
  uploadedAt: string
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED'
  comments?: string
}

export type DocumentType = 
  | 'PHOTO'
  | 'SIGNATURE'
  | 'AADHAAR'
  | 'PAN'
  | 'EDUCATIONAL_CERTIFICATE'
  | 'EXPERIENCE_CERTIFICATE'
  | 'OTHER'

export interface Payment {
  id: string
  amount: number
  currency: string
  status: PaymentStatus
  method: PaymentMethod
  transactionId?: string
  razorpayPaymentId?: string
  razorpayOrderId?: string
  paidAt?: string
  createdAt: string
}

export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED'
export type PaymentMethod = 'ONLINE' | 'OFFLINE' | 'DD' | 'CASH'

export interface WorkflowHistoryEntry {
  id: string
  fromStatus: WorkflowState
  toStatus: WorkflowState
  actionBy: User
  actionAt: string
  comments?: string
  documents?: Document[]
}

// Form Types
export interface ApplicationFormData {
  applicantInfo: ApplicantInfo
  addresses: Address[]
  experiences: Experience[]
  qualifications: Qualification[]
  positionType: PositionType
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T> {
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
}

export interface OTPLoginRequest {
  phoneNumber: string
  otp: string
}

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  password: string
}

export interface AuthResponse {
  token: string
  refreshToken: string
  user: User
  expiresAt: string
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  isRead: boolean
  createdAt: string
  actionUrl?: string
}

// Dashboard Types
export interface DashboardStats {
  totalApplications: number
  pendingApplications: number
  approvedApplications: number
  rejectedApplications: number
}

// Filter and Search Types
export interface ApplicationFilters {
  status?: WorkflowState[]
  positionType?: PositionType[]
  dateFrom?: string
  dateTo?: string
  searchQuery?: string
  assignedToMe?: boolean
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'APPLICATION_STATUS_CHANGE' | 'NEW_NOTIFICATION' | 'USER_ACTIVITY'
  payload: any
  timestamp: string
}

// Export all types
export type {
  // Add any additional type exports here
}
