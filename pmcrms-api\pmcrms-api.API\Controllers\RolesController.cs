using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using System.Net;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// Role management endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
[Tags("Roles")]
[Produces("application/json")]
public class RolesController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public RolesController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    /// <summary>
    /// Get all available roles
    /// </summary>
    /// <returns>List of all roles</returns>
    /// <response code="200">Roles retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(RolesResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<RolesResponse>> GetRoles()
    {
        try
        {
            var roles = await _unitOfWork.Repository<Role>().GetAllAsync();

            var roleDtos = roles.Select(r => new RoleDto
            {
                Id = r.Id,
                Name = r.Name,
                DisplayName = GetDisplayName(r.Name)
            }).ToList();

            return Ok(new RolesResponse
            {
                Success = true,
                Data = roleDtos
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve roles", error = ex.Message });
        }
    }

    /// <summary>
    /// Get role by ID
    /// </summary>
    /// <param name="id">Role ID</param>
    /// <returns>Role details</returns>
    /// <response code="200">Role found</response>
    /// <response code="404">Role not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(RoleDetailResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<RoleDetailResponse>> GetRole(int id)
    {
        try
        {
            var role = await _unitOfWork.Repository<Role>().GetByIdAsync(id);

            if (role == null)
            {
                return NotFound(new { success = false, message = "Role not found" });
            }

            var roleDto = new RoleDto
            {
                Id = role.Id,
                Name = role.Name,
                DisplayName = GetDisplayName(role.Name)
            };

            return Ok(new RoleDetailResponse
            {
                Success = true,
                Data = roleDto
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve role", error = ex.Message });
        }
    }

    private static string GetDisplayName(string roleName)
    {
        return roleName switch
        {
            "ADMIN" => "System Administrator",
            "JUNIOR_ENGINEER" => "Junior Engineer",
            "ASSISTANT_ENGINEER" => "Assistant Engineer",
            "EXECUTIVE_ENGINEER" => "Executive Engineer",
            "CITY_ENGINEER" => "City Engineer",
            "CLERK" => "Clerk",
            "User" => "Guest User",
            _ => roleName
        };
    }
}

// Role DTOs
public class RolesResponse
{
    public bool Success { get; set; }
    public List<RoleDto> Data { get; set; } = new();
}

public class RoleDetailResponse
{
    public bool Success { get; set; }
    public RoleDto Data { get; set; } = null!;
}

public class RoleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
}
