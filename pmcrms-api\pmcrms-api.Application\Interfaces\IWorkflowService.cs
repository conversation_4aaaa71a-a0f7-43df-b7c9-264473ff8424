using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Application.Interfaces;

public interface IWorkflowService
{
    Task<bool> CanTransitionToAsync(WorkflowStateType currentState, WorkflowStateType nextState, string roleName);
    Task<IEnumerable<WorkflowStateType>> GetAvailableStatesAsync(WorkflowStateType currentState, string roleName);
    Task<bool> ValidateTransitionAsync(Domain.Entities.Application application, WorkflowStateType nextState, string roleName);
    Task<Domain.Entities.Application> TransitionToAsync(Domain.Entities.Application application, WorkflowStateType nextState, string roleName, string? remarks = null);
    Task InitializeWorkflowAsync();
}
