using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using System.Net;

namespace pmcrms_api.API.Controllers;

/// <summary>
/// User management endpoints for admin dashboard
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
[Tags("Users")]
[Produces("application/json")]
public class UsersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public UsersController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    /// <summary>
    /// Get all users with filtering and pagination
    /// </summary>
    /// <param name="role">Filter by user role</param>
    /// <param name="status">Filter by active status</param>
    /// <param name="search">Search term</param>
    /// <param name="page">Page number</param>
    /// <param name="limit">Items per page</param>
    /// <returns>List of users</returns>
    /// <response code="200">Users retrieved successfully</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(UsersResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<UsersResponse>> GetUsers(
        [FromQuery] string? role = null,
        [FromQuery] string? status = null,
        [FromQuery] string? search = null,
        [FromQuery] int page = 1,
        [FromQuery] int limit = 50)
    {
        try
        {
            var users = await _unitOfWork.Repository<User>().GetAllAsync();

            // Apply filters
            var filteredUsers = users.AsQueryable();

            if (!string.IsNullOrEmpty(role))
            {
                filteredUsers = filteredUsers.Where(u => u.Role != null && u.Role.Name.Equals(role, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(status))
            {
                var isActive = status.Equals("active", StringComparison.OrdinalIgnoreCase);
                filteredUsers = filteredUsers.Where(u => u.IsActive == isActive);
            }

            if (!string.IsNullOrEmpty(search))
            {
                filteredUsers = filteredUsers.Where(u =>
                    u.FirstName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    u.LastName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    u.Email.Contains(search, StringComparison.OrdinalIgnoreCase));
            }

            var totalCount = filteredUsers.Count();

            // Apply pagination
            var paginatedUsers = filteredUsers
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToList();

            // Map to response DTOs
            var userDtos = paginatedUsers.Select(u => new UserDto
            {
                Id = u.Id,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Email = u.Email,
                PhoneNumber = u.PhoneNumber ?? "N/A",
                Role = u.Role?.Name ?? "Unknown",
                IsActive = u.IsActive,
                CreatedDate = u.CreatedAt,
                LastLogin = u.LastLogin,
                Department = "N/A", // TODO: Add department to User entity
                AssignedApplicationsCount = 0 // TODO: Count assigned applications
            }).ToList();

            return Ok(new UsersResponse
            {
                Success = true,
                Data = userDtos,
                TotalCount = totalCount
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve users", error = ex.Message });
        }
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User details</returns>
    /// <response code="200">User found</response>
    /// <response code="404">User not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(UserDetailResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<UserDetailResponse>> GetUser(int id)
    {
        try
        {
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(id);

            if (user == null)
            {
                return NotFound(new { success = false, message = "User not found" });
            }

            var userDetail = new UserDto
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber ?? "N/A",
                Role = user.Role?.Name ?? "Unknown",
                IsActive = user.IsActive,
                CreatedDate = user.CreatedAt,
                LastLogin = user.LastLogin,
                Department = "N/A",
                AssignedApplicationsCount = 0
            };

            return Ok(new UserDetailResponse
            {
                Success = true,
                Data = userDetail
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to retrieve user", error = ex.Message });
        }
    }

    /// <summary>
    /// Create new user
    /// </summary>
    /// <param name="request">User creation request</param>
    /// <returns>Created user details</returns>
    /// <response code="201">User created successfully</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(UserDetailResponse), (int)HttpStatusCode.Created)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<UserDetailResponse>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            // Check if user already exists
            var existingUsers = await _unitOfWork.Repository<User>().FindAsync(u => u.Email == request.Email);
            if (existingUsers.Any())
            {
                return BadRequest(new { success = false, message = "User with this email already exists" });
            }

            // Get role
            var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Name == request.Role);
            var role = roles.FirstOrDefault();
            if (role == null)
            {
                return BadRequest(new { success = false, message = "Invalid role" });
            }

            // Create user
            var user = new User
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                RoleId = role.Id,
                Role = role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                Password = BCrypt.Net.BCrypt.HashPassword("TempPassword@123") // Temporary password
            };

            await _unitOfWork.Repository<User>().AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            var userDto = new UserDto
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber ?? "N/A",
                Role = user.Role.Name,
                IsActive = user.IsActive,
                CreatedDate = user.CreatedAt,
                LastLogin = user.LastLogin,
                Department = "N/A",
                AssignedApplicationsCount = 0
            };

            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, new UserDetailResponse
            {
                Success = true,
                Data = userDto
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to create user", error = ex.Message });
        }
    }

    /// <summary>
    /// Update user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="request">User update request</param>
    /// <returns>Updated user details</returns>
    /// <response code="200">User updated successfully</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="404">User not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(UserDetailResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<UserDetailResponse>> UpdateUser(int id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { success = false, message = "User not found" });
            }

            // Update user properties
            user.FirstName = request.FirstName;
            user.LastName = request.LastName;
            user.PhoneNumber = request.PhoneNumber;
            user.IsActive = request.IsActive;

            if (!string.IsNullOrEmpty(request.Role))
            {
                var roles = await _unitOfWork.Repository<Role>().FindAsync(r => r.Name == request.Role);
                var role = roles.FirstOrDefault();
                if (role != null)
                {
                    user.RoleId = role.Id;
                    user.Role = role;
                }
            }

            _unitOfWork.Repository<User>().Update(user);
            await _unitOfWork.SaveChangesAsync();

            var userDto = new UserDto
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber ?? "N/A",
                Role = user.Role?.Name ?? "Unknown",
                IsActive = user.IsActive,
                CreatedDate = user.CreatedAt,
                LastLogin = user.LastLogin,
                Department = "N/A",
                AssignedApplicationsCount = 0
            };

            return Ok(new UserDetailResponse
            {
                Success = true,
                Data = userDto
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to update user", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success message</returns>
    /// <response code="200">User deleted successfully</response>
    /// <response code="404">User not found</response>
    /// <response code="401">Unauthorized access</response>
    /// <response code="500">Internal server error</response>
    [HttpDelete("{id}")]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult> DeleteUser(int id)
    {
        try
        {
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { success = false, message = "User not found" });
            }

            // Soft delete by setting IsActive to false
            user.IsActive = false;
            _unitOfWork.Repository<User>().Update(user);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new { success = true, message = "User deleted successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Failed to delete user", error = ex.Message });
        }
    }
}

// User Management DTOs
public class UsersResponse
{
    public bool Success { get; set; }
    public List<UserDto> Data { get; set; } = new();
    public int TotalCount { get; set; }
}

public class UserDetailResponse
{
    public bool Success { get; set; }
    public UserDto Data { get; set; } = null!;
}

public class UserDto
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? LastLogin { get; set; }
    public string Department { get; set; } = string.Empty;
    public int AssignedApplicationsCount { get; set; }
}

public class CreateUserRequest
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Role { get; set; } = string.Empty;
}

public class UpdateUserRequest
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}
