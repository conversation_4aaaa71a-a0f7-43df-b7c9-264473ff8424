import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// English translations
const enTranslations = {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    submit: 'Submit',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    download: 'Download',
    upload: 'Upload',
    search: 'Search',
    filter: 'Filter',
    clear: 'Clear',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
  },
  auth: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    phoneNumber: 'Phone Number',
    otp: 'OTP',
    forgotPassword: 'Forgot Password?',
    loginWithOTP: 'Login with OTP',
    loginWithEmail: 'Login with Email',
    welcomeBack: 'Welcome Back',
    createAccount: 'Create Account',
  },
  application: {
    applicationNumber: 'Application Number',
    status: 'Status',
    position: 'Position',
    submit: 'Submit Application',
    personalInfo: 'Personal Information',
    address: 'Address',
    experience: 'Experience',
    qualifications: 'Qualifications',
    documents: 'Documents',
    review: 'Review',
    firstName: 'First Name',
    lastName: 'Last Name',
    dateOfBirth: 'Date of Birth',
    gender: 'Gender',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    aadhaar: 'Aadhaar Number',
    pan: 'PAN Number',
    permanent: 'Permanent',
    local: 'Local',
    sameAsPermanent: 'Same as Permanent Address',
    addressLine1: 'Address Line 1',
    addressLine2: 'Address Line 2',
    city: 'City',
    state: 'State',
    pinCode: 'PIN Code',
    country: 'Country',
    company: 'Company',
    designation: 'Designation',
    startDate: 'Start Date',
    endDate: 'End Date',
    currentJob: 'Currently Working Here',
    degree: 'Degree',
    institution: 'Institution',
    university: 'University',
    yearOfPassing: 'Year of Passing',
    percentage: 'Percentage',
  },
  dashboard: {
    dashboard: 'Dashboard',
    applications: 'Applications',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    total: 'Total',
    recent: 'Recent Applications',
    statistics: 'Statistics',
    workload: 'Workload',
    notifications: 'Notifications',
  },
  workflow: {
    documentVerification: 'Document Verification',
    juniorEngineerReview: 'Junior Engineer Review',
    assistantEngineerReview: 'Assistant Engineer Review',
    executiveEngineerReview: 'Executive Engineer Review',
    cityEngineerReview: 'City Engineer Review',
    paymentPending: 'Payment Pending',
    clerkProcessing: 'Clerk Processing',
    digitalSignature: 'Digital Signature',
    finalApproval: 'Final Approval',
    rejected: 'Rejected',
    approve: 'Approve',
    reject: 'Reject',
    comments: 'Comments',
  },
  positions: {
    architect: 'Architect',
    licenseEngineer: 'License Engineer',
    structuralEngineer: 'Structural Engineer',
    supervisor1: 'Supervisor 1',
    supervisor2: 'Supervisor 2',
  },
  errors: {
    required: 'This field is required',
    invalidEmail: 'Please enter a valid email address',
    invalidPhone: 'Please enter a valid phone number',
    invalidDate: 'Please enter a valid date',
    fileRequired: 'Please upload a file',
    fileTooLarge: 'File size is too large',
    invalidFileType: 'Invalid file type',
    networkError: 'Network error. Please check your connection.',
    serverError: 'Server error. Please try again later.',
    unauthorized: 'You are not authorized to perform this action.',
    notFound: 'The requested resource was not found.',
  },
}

// Marathi translations
const mrTranslations = {
  common: {
    loading: 'लोड होत आहे...',
    error: 'त्रुटी',
    success: 'यशस्वी',
    submit: 'सबमिट करा',
    cancel: 'रद्द करा',
    save: 'सेव करा',
    edit: 'संपादन',
    delete: 'हटवा',
    view: 'पहा',
    download: 'डाउनलोड',
    upload: 'अपलोड',
    search: 'शोधा',
    filter: 'फिल्टर',
    clear: 'साफ करा',
    back: 'मागे',
    next: 'पुढे',
    previous: 'मागील',
    confirm: 'पुष्टी करा',
    yes: 'होय',
    no: 'नाही',
  },
  auth: {
    login: 'लॉगिन',
    logout: 'लॉगआउट',
    register: 'नोंदणी करा',
    email: 'ईमेल',
    password: 'पासवर्ड',
    phoneNumber: 'फोन नंबर',
    otp: 'ओटीपी',
    forgotPassword: 'पासवर्ड विसरलात?',
    loginWithOTP: 'ओटीपी ने लॉगिन करा',
    loginWithEmail: 'ईमेलने लॉगिन करा',
    welcomeBack: 'परत स्वागत',
    createAccount: 'खाते तयार करा',
  },
  application: {
    applicationNumber: 'अर्ज क्रमांक',
    status: 'स्थिती',
    position: 'पद',
    submit: 'अर्ज सबमिट करा',
    personalInfo: 'वैयक्तिक माहिती',
    address: 'पत्ता',
    experience: 'अनुभव',
    qualifications: 'पात्रता',
    documents: 'कागदपत्रे',
    review: 'पुनरावलोकन',
    firstName: 'पहिले नाव',
    lastName: 'आडनाव',
    dateOfBirth: 'जन्मतारीख',
    gender: 'लिंग',
    male: 'पुरुष',
    female: 'स्त्री',
    other: 'इतर',
    aadhaar: 'आधार क्रमांक',
    pan: 'पॅन क्रमांक',
    permanent: 'कायमचा',
    local: 'स्थानिक',
    sameAsPermanent: 'कायमच्या पत्त्यासारखेच',
    addressLine1: 'पत्ता ओळ 1',
    addressLine2: 'पत्ता ओळ 2',
    city: 'शहर',
    state: 'राज्य',
    pinCode: 'पिन कोड',
    country: 'देश',
    company: 'कंपनी',
    designation: 'पदनाम',
    startDate: 'सुरुवातीची तारीख',
    endDate: 'समाप्तीची तारीख',
    currentJob: 'सध्या येथे काम करत आहे',
    degree: 'पदवी',
    institution: 'संस्था',
    university: 'विद्यापीठ',
    yearOfPassing: 'उत्तीर्ण वर्ष',
    percentage: 'टक्केवारी',
  },
  dashboard: {
    dashboard: 'डॅशबोर्ड',
    applications: 'अर्ज',
    pending: 'प्रलंबित',
    approved: 'मंजूर',
    rejected: 'नाकारले',
    total: 'एकूण',
    recent: 'अलीकडील अर्ज',
    statistics: 'आकडेवारी',
    workload: 'कामाचा भार',
    notifications: 'सूचना',
  },
  workflow: {
    documentVerification: 'कागदपत्र पडताळणी',
    juniorEngineerReview: 'कनिष्ठ अभियंता पुनरावलोकन',
    assistantEngineerReview: 'सहायक अभियंता पुनरावलोकन',
    executiveEngineerReview: 'कार्यकारी अभियंता पुनरावलोकन',
    cityEngineerReview: 'शहर अभियंता पुनरावलोकन',
    paymentPending: 'पेमेंट प्रलंबित',
    clerkProcessing: 'लिपिक प्रक्रिया',
    digitalSignature: 'डिजिटल स्वाक्षरी',
    finalApproval: 'अंतिम मंजुरी',
    rejected: 'नाकारले',
    approve: 'मंजूर करा',
    reject: 'नाकारा',
    comments: 'टिप्पण्या',
  },
  positions: {
    architect: 'वास्तुविशारद',
    licenseEngineer: 'परवाना अभियंता',
    structuralEngineer: 'संरचनात्मक अभियंता',
    supervisor1: 'पर्यवेक्षक 1',
    supervisor2: 'पर्यवेक्षक 2',
  },
  errors: {
    required: 'हे फील्ड आवश्यक आहे',
    invalidEmail: 'कृपया वैध ईमेल पत्ता प्रविष्ट करा',
    invalidPhone: 'कृपया वैध फोन नंबर प्रविष्ट करा',
    invalidDate: 'कृपया वैध तारीख प्रविष्ट करा',
    fileRequired: 'कृपया फाइल अपलोड करा',
    fileTooLarge: 'फाइलचा आकार खूप मोठा आहे',
    invalidFileType: 'अवैध फाइल प्रकार',
    networkError: 'नेटवर्क त्रुटी. कृपया आपले कनेक्शन तपासा.',
    serverError: 'सर्व्हर त्रुटी. कृपया पुन्हा प्रयत्न करा.',
    unauthorized: 'आपल्याला ही क्रिया करण्याची अधिकृतता नाही.',
    notFound: 'विनंती केलेला स्रोत सापडला नाही.',
  },
}

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslations,
      },
      mr: {
        translation: mrTranslations,
      },
    },
    lng: 'en', // default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  })

export default i18n
