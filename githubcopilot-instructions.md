

# Additional Implementation Details

## Data Transfer Objects (DTOs)

### Authentication DTOs
```csharp
// User Registration DTOs
public class UserRegistrationDto
{
    public string Email { get; set; }
    public string MobileNumber { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
}

// OTP DTOs
public class SendOtpDto
{
    public string Contact { get; set; }  // Email or Mobile
    public string Type { get; set; }     // "Email" or "Mobile"
}

public class VerifyOtpDto
{
    public string Contact { get; set; }
    public string Otp { get; set; }
}

// Officer Invitation DTOs
public class OfficerInvitationDto
{
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Role { get; set; }
}
```

### Application DTOs
```csharp
public class ApplicationSubmissionDto
{
    // Personal Info
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string MotherName { get; set; }
    public string MobileNumber { get; set; }
    public string EmailAddress { get; set; }
    public string Position { get; set; }
    public string BloodGroup { get; set; }
    public decimal Height { get; set; }
    public string Gender { get; set; }

    // Addresses
    public AddressDto LocalAddress { get; set; }
    public bool PermanentSameAsLocal { get; set; }
    public AddressDto PermanentAddress { get; set; }

    // Collections
    public List<ExperienceDto> Experiences { get; set; }
    public List<QualificationDto> Qualifications { get; set; }

    // Files
    public IFormFile ProfilePhoto { get; set; }
    public IFormFile SelfDeclarationForm { get; set; }
}
```

## FluentValidation Rules

### Example Validators
```csharp
public class ApplicationSubmissionValidator : AbstractValidator<ApplicationSubmissionDto>
{
    public ApplicationSubmissionValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty()
            .Matches("^[a-zA-Z]+(?: [a-zA-Z]+)*$");

        RuleFor(x => x.LastName)
            .NotEmpty()
            .Matches("^[a-zA-Z]+(?: [a-zA-Z]+)*$");

        RuleFor(x => x.MotherName)
            .NotEmpty()
            .Matches("^[a-zA-Z]+(?: [a-zA-Z]+)*$");

        RuleFor(x => x.MobileNumber)
            .NotEmpty()
            .Matches("^(\\+\\d{1,3}[- ]?)?\\d{10}$");

        RuleFor(x => x.EmailAddress)
            .NotEmpty()
            .EmailAddress();

        RuleFor(x => x.Height)
            .NotEmpty()
            .GreaterThan(0);
    }
}
```

## Exception Handling Middleware

### Custom Exceptions
```csharp
public class ApiException : Exception
{
    public int StatusCode { get; }
    
    public ApiException(string message, int statusCode = 500) : base(message)
    {
        StatusCode = statusCode;
    }
}

public class NotFoundException : ApiException
{
    public NotFoundException(string message) : base(message, 404)
    {
    }
}

public class ValidationException : ApiException
{
    public ValidationException(string message) : base(message, 400)
    {
    }
}
```

## Services Interface Definitions

### Digital Signature Service
```csharp
public interface IDigitalSignatureService
{
    Task<byte[]> SignPdfAsync(byte[] pdfData, string signerRole);
    Task<bool> VerifySignatureAsync(byte[] signedPdf);
    Task<byte[]> ApplyHsmSignatureAsync(byte[] pdfData, string certificateAlias);
}
```

### Payment Gateway Service
```csharp
public interface IPaymentGatewayService
{
    Task<PaymentInitiationResponse> InitiatePaymentAsync(PaymentRequest request);
    Task<PaymentVerificationResponse> VerifyPaymentAsync(string transactionId);
    Task<PaymentRefundResponse> ProcessRefundAsync(string transactionId);
}
```

## Database Configurations

### Entity Configurations
```csharp
public class ApplicationConfiguration : IEntityTypeConfiguration<Application>
{
    public void Configure(EntityTypeBuilder<Application> builder)
    {
        builder.HasKey(x => x.Id);
        
        builder.Property(x => x.FirstName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.LastName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.CertificateNumber)
            .HasMaxLength(50);

        builder.HasIndex(x => x.CertificateNumber)
            .IsUnique();
    }
}
```

## Additional Requirements

### Security Considerations
- Implement rate limiting for OTP requests
- Add request validation middleware
- Implement audit logging for all critical operations
- Add file upload virus scanning
- Implement concurrent application access control

### Performance Optimizations
- Add caching for frequently accessed data
- Implement lazy loading for large collections
- Add pagination for all list endpoints
- Implement async operations consistently

### Testing Requirements
- Unit tests for all business logic
- Integration tests for API endpoints
- E2E tests for critical workflows
- Performance tests for concurrent users

### Monitoring and Logging
- Implement structured logging
- Add performance metrics collection
- Set up health checks
- Configure error monitoring

### Additional Services Needed
1. **Caching Service**
```csharp
public interface ICacheService
{
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null);
    Task RemoveAsync(string key);
    Task<bool> ExistsAsync(string key);
}
```

2. **File Storage Service**
```csharp
public interface IFileStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType);
    Task<Stream> DownloadFileAsync(string fileId);
    Task DeleteFileAsync(string fileId);
}
```

3. **Notification Service**
```csharp
public interface INotificationService
{
    Task SendSmsAsync(string phoneNumber, string message);
    Task SendPushNotificationAsync(string userId, string title, string message);
    Task SendWhatsAppNotificationAsync(string phoneNumber, string message);
}
```

4. **Audit Service**
```csharp
public interface IAuditService
{
    Task LogActivityAsync(string userId, string action, string details);
    Task<IEnumerable<AuditLog>> GetUserActivityAsync(string userId, DateTime from, DateTime to);
    Task<IEnumerable<AuditLog>> GetApplicationAuditAsync(Guid applicationId);
}
```

### Additional Middleware Requirements
1. **Request/Response Logging Middleware**
2. **API Key Validation Middleware**
3. **Rate Limiting Middleware**
4. **IP Filtering Middleware**
5. **Request Validation Middleware**

### Background Jobs
1. Document Processing Queue
2. Email/SMS Queue
3. PDF Generation Queue
4. Cleanup Jobs

### API Versioning Strategy
- Use URL versioning (v1, v2)
- Implement API deprecation policy
- Version DTOs separately from entities

### Documentation Requirements
- API Documentation (Swagger)
- Database Schema Documentation
- Architecture Decision Records (ADRs)
- Deployment Guide
- User Manual
- API Integration Guide

This additional documentation covers the essential components that were missing from the original specification and will help ensure a more complete and robust implementation.
