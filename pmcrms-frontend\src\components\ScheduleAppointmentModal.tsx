import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Textarea } from './ui/textarea'
import { Calendar, User, MapPin, Building } from 'lucide-react'
import { useAuthStore } from '../store'
import api from '../lib/api'

interface ScheduleAppointmentModalProps {
  isOpen: boolean
  onClose: () => void
  application: {
    id: number
    applicationNumber: string
    firstName?: string
    lastName?: string
    applicantName?: string
    position: string
  }
  onScheduled: () => void
}

interface ScheduleAppointmentRequest {
  applicationId: number
  reviewDate: string
  contactPerson: string
  place: string
  roomNumber: string
  notes?: string
}

const ScheduleAppointmentModal: React.FC<ScheduleAppointmentModalProps> = ({
  isOpen,
  onClose,
  application,
  onScheduled
}) => {
  const { token } = useAuthStore()
  const [formData, setFormData] = useState<ScheduleAppointmentRequest>({
    applicationId: application.id,
    reviewDate: '',
    contactPerson: '',
    place: '',
    roomNumber: '',
    notes: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: keyof ScheduleAppointmentRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    if (!formData.reviewDate) {
      setError('Review date and time is required')
      return false
    }
    if (!formData.contactPerson) {
      setError('Contact person is required')
      return false
    }
    if (!formData.place) {
      setError('Place is required')
      return false
    }
    if (!formData.roomNumber) {
      setError('Room number is required')
      return false
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await api.post('/applications/schedule-appointment', formData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        onScheduled()
        onClose()
        // Reset form
        setFormData({
          applicationId: application.id,
          reviewDate: '',
          contactPerson: '',
          place: '',
          roomNumber: '',
          notes: ''
        })
      } else {
        setError(response.data.message || 'Failed to schedule appointment')
      }
    } catch (err: any) {
      console.error('Failed to schedule appointment:', err)
      setError(err.response?.data?.message || 'Failed to schedule appointment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setFormData({
      applicationId: application.id,
      reviewDate: '',
      contactPerson: '',
      place: '',
      roomNumber: '',
      notes: ''
    })
    setError(null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Schedule Appointment</span>
          </DialogTitle>
          <DialogDescription>
            Schedule document verification appointment for {application.firstName && application.lastName 
              ? `${application.firstName} ${application.lastName}` 
              : application.applicantName || 'Applicant'} 
            (Application #{application.applicationNumber})
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="reviewDate" className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Review Date</span>
              </Label>
              <Input
                id="reviewDate"
                type="datetime-local"
                value={formData.reviewDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('reviewDate', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactPerson" className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Contact Person</span>
              </Label>
              <Input
                id="contactPerson"
                value={formData.contactPerson}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('contactPerson', e.target.value)}
                placeholder="Officer name"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="place" className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Place</span>
              </Label>
              <Input
                id="place"
                value={formData.place}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('place', e.target.value)}
                placeholder="PMC Office, Department"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="roomNumber" className="flex items-center space-x-2">
                <Building className="h-4 w-4" />
                <span>Room Number</span>
              </Label>
              <Input
                id="roomNumber"
                value={formData.roomNumber}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('roomNumber', e.target.value)}
                placeholder="Room 101"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('notes', e.target.value)}
              placeholder="Additional instructions or notes for the appointment"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Scheduling...' : 'Schedule Appointment'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default ScheduleAppointmentModal
