namespace pmcrms_api.Application.Interfaces;

/// <summary>
/// Service for generating certificates
/// </summary>
public interface ICertificateService
{
    /// <summary>
    /// Generate certificate when payment is successful
    /// </summary>
    /// <param name="paymentId">Payment ID</param>
    /// <returns>Generated certificate as byte array</returns>
    Task<byte[]> GenerateCertificateAsync(int paymentId);
    
    /// <summary>
    /// Generate certificate for specific application
    /// </summary>
    /// <param name="applicationId">Application ID</param>
    /// <returns>Generated certificate as byte array</returns>
    Task<byte[]> GenerateCertificateForApplicationAsync(int applicationId);
}
