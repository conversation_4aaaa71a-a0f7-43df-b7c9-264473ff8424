using Microsoft.EntityFrameworkCore;
using pmcrms_api.Domain.Entities;

namespace pmcrms_api.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Domain.Entities.Application> Applications { get; set; }
    public DbSet<Transaction> Transactions { get; set; }
    public DbSet<WorkflowState> WorkflowStates { get; set; }
    public DbSet<WorkflowTransition> WorkflowTransitions { get; set; }
    public DbSet<WorkflowStateHistory> WorkflowStateHistory { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Document> Documents { get; set; }
    public DbSet<Appointment> Appointments { get; set; }
    public DbSet<DigitalSignature> DigitalSignatures { get; set; }
    
    // New entities as per prompt requirements
    public DbSet<Experience> Experiences { get; set; }
    public DbSet<Qualification> Qualifications { get; set; }
    public DbSet<Address> Addresses { get; set; }
    
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) 
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Register all entity configurations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }
}
