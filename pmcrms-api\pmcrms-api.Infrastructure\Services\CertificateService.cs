using pmcrms_api.Application.Interfaces;
using pmcrms_api.Domain.Entities;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using Microsoft.Extensions.Logging;
using DomainEntities = pmcrms_api.Domain.Entities;

namespace pmcrms_api.Infrastructure.Services;

/// <summary>
/// Service for generating PDF certificates
/// </summary>
public partial class CertificateService : ICertificateService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CertificateService> _logger;

    static CertificateService()
    {
        // Register fonts for certificate generation
        RegisterFonts();
    }

    public CertificateService(IUnitOfWork unitOfWork, ILogger<CertificateService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<byte[]> GenerateCertificateAsync(int paymentId)
    {
        try
        {
            _logger.LogInformation($"Generating certificate for payment ID: {paymentId}");

            // Get payment details
            var payment = await _unitOfWork.Repository<Payment>().GetByIdAsync(paymentId);
            if (payment == null)
            {
                throw new ArgumentException($"Payment with ID {paymentId} not found");
            }

            // Get associated application
            var applications = await _unitOfWork.Repository<Domain.Entities.Application>()
                .FindAsync(a => a.Id == payment.ApplicationId);
            var application = applications.FirstOrDefault();
            
            if (application == null)
            {
                throw new ArgumentException($"Application not found for payment ID {paymentId}");
            }

            // Get user details
            var users = await _unitOfWork.Repository<User>().FindAsync(u => u.Id == application.UserId);
            var user = users.FirstOrDefault();
            
            if (user == null)
            {
                throw new ArgumentException($"User not found for application");
            }

            return await GenerateCertificateForApplicationAsync(application.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error generating certificate for payment ID: {paymentId}");
            throw;
        }
    }

    public async Task<byte[]> GenerateCertificateForApplicationAsync(int applicationId)
    {
        try
        {
            _logger.LogInformation($"Generating certificate for application ID: {applicationId}");

            // Get application details
            var application = await _unitOfWork.Repository<Domain.Entities.Application>().GetByIdAsync(applicationId);
            if (application == null)
            {
                throw new ArgumentException($"Application with ID {applicationId} not found");
            }

            // Get user details
            var user = await _unitOfWork.Repository<User>().GetByIdAsync(application.UserId);
            if (user == null)
            {
                throw new ArgumentException($"User not found for application");
            }

            // Get payment details
            var payments = await _unitOfWork.Repository<Payment>()
                .FindAsync(p => p.ApplicationId == applicationId && p.Status == "Completed");
            var payment = payments.FirstOrDefault();

            if (payment == null)
            {
                throw new ArgumentException($"No completed payment found for application {applicationId}");
            }

            // Create certificate model
            var certificateModel = CreateCertificateModel(application, user, payment);

            // Generate PDF
            var certificate = new SECertificate(certificateModel);
            var pdfBytes = certificate.GeneratePdf();

            _logger.LogInformation($"Certificate generated successfully for application ID: {applicationId}");
            return pdfBytes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error generating certificate for application ID: {applicationId}");
            throw;
        }
    }

    private CertificateModel CreateCertificateModel(Domain.Entities.Application application, User user, Payment payment)
    {
        return new CertificateModel
        {
            CertificateNumber = $"PMC_{application.ApplicationNumber}_{DateTime.Now.Year}",
            Name = $"{user.FirstName} {user.LastName}",
            Address = GetFullAddress(application), // Get address from new Address collection
            Position = GetPositionInMarathi(application.Position.ToString()), // Use new Position enum
            FromDate = payment.PaymentDate,
            ToYear = payment.PaymentDate.AddYears(3).Year,
            IsPayment = true,
            TransactionDate = payment.PaymentDate.ToString("dd-MM-yyyy"),
            ChallanNumber = payment.PaymentNumber,
            Amount = payment.Amount.ToString("0.00"),
            QrCodeUrl = GenerateQrCode(application),
            Logo = GetDefaultLogo(),
            ProfilePhoto = GetDefaultProfilePhoto()
        };
    }

    private string GetPositionInMarathi(string positionType)
    {
        return positionType switch
        {
            "Architect" => "स्थापत्यशास्त्रज्ञ",
            "StructuralEngineer" => "स्ट्रक्चरल इंजिनिअर",
            "LicenseEngineer" => "लायसन्स इंजिनिअर",
            "Supervisor1" => "सुपरवायझर1",
            "Supervisor2" => "सुपरवायझर2",
            _ => "स्ट्रक्चरल इंजिनिअर"
        };
    }

    private string GenerateQrCode(Domain.Entities.Application application)
    {
        // Simple QR code SVG for verification
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z\" fill=\"#000000\"/></svg>";
    }

    private byte[] GetDefaultLogo()
    {
        // Return default PMC logo bytes or load from embedded resource
        // For now, return empty array - you should load actual logo
        return Array.Empty<byte>();
    }

    private Stream GetDefaultProfilePhoto()
    {
        // Return default profile photo or empty stream
        return new MemoryStream();
    }
    
    private string GetFullAddress(DomainEntities.Application application)
    {
        var localAddress = application.Addresses.FirstOrDefault(a => a.IsLocal);
        if (localAddress != null)
        {
            return $"{localAddress.FlatHouseNumber}, {localAddress.StreetAddress}, {localAddress.AddressLine}, {localAddress.City}, {localAddress.State}, {localAddress.Country} - {localAddress.PostalCode}";
        }
        return "Address not provided";
    }

    private static void RegisterFonts()
    {
        // Register fonts if available as embedded resources
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var fontNames = new[] { "Mangal.ttf", "times.ttf", "ariblk.ttf", "times-new-roman-grassetto.ttf" };
            
            foreach (var fontName in fontNames)
            {
                var fontStream = assembly.GetManifestResourceStream($"pmcrms_api.Infrastructure.Fonts.{fontName}");
                if (fontStream != null)
                {
                    FontManager.RegisterFont(fontStream);
                }
            }
        }
        catch (Exception)
        {
            // Fonts not available, use default fonts
        }
    }
}

/// <summary>
/// Certificate document generator using QuestPDF
/// </summary>
public class SECertificate : IDocument
{
    private readonly CertificateModel _model;

    public SECertificate(CertificateModel model)
    {
        _model = model;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public void Compose(IDocumentContainer container)
    {
        container.Page(page =>
        {
            page.Size(PageSizes.Legal);
            page.Margin(30);
            page.Header().Element(ComposeHeader);
            page.Content().Element(ComposeContent);
        });
    }

    private void ComposeHeader(IContainer container)
    {
        container.Column(column =>
        {
            // Header content similar to your original code
            column.Item().AlignCenter().Text("पुणे महानगरपालिका")
                .FontSize(16).Bold();
            
            column.Item().AlignCenter().Text($"{_model.Position} च्या कामासाठी परवाना")
                .FontSize(12);
                
            column.Item().Height(10);
        });
    }

    private void ComposeContent(IContainer container)
    {
        container.Column(column =>
        {
            // Certificate content
            column.Item().Text($"परवाना क्र. :- {_model.CertificateNumber}")
                .FontSize(10);
                
            column.Item().Text($"नाव :- {_model.Name}")
                .FontSize(10);
                
            column.Item().Text($"पत्ता :- {_model.Address}")
                .FontSize(10);
                
            column.Item().Text($"दिनांक :- {_model.FromDate:dd/MM/yyyy}")
                .FontSize(10);
                
            // Add more content as needed
            column.Item().PaddingTop(50).Text("शहर अभियंता पुणे महानगरपालिका")
                .AlignRight().FontSize(10);
        });
    }
}

/// <summary>
/// Model for certificate data
/// </summary>
public class CertificateModel
{
    public string CertificateNumber { get; set; } = string.Empty;
    public byte[] Logo { get; set; } = Array.Empty<byte>();
    public string QrCodeUrl { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public int ToYear { get; set; }
    public Stream ProfilePhoto { get; set; } = new MemoryStream();
    public bool IsPayment { get; set; }
    public string TransactionDate { get; set; } = string.Empty;
    public string ChallanNumber { get; set; } = string.Empty;
    public string Amount { get; set; } = string.Empty;
}
