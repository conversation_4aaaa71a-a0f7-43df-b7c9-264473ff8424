# Cube.js Schema Configuration for PMCRMS

This directory contains the Cube.js schema files that define the data model for analytics and reporting in the PMCRMS application.

## Setup Instructions

1. **Install Cube.js Server**
   ```bash
   npx cubejs-cli create pmcrms-analytics -d mssql
   cd pmcrms-analytics
   ```

2. **Configure Database Connection**
   Update `.env` file with your SQL Server connection:
   ```env
   CUBEJS_DB_TYPE=mssql
   CUBEJS_DB_HOST=your-server
   CUBEJS_DB_NAME=pmcrms_db
   CUBEJS_DB_USER=your-username
   CUBEJS_DB_PASS=your-password
   CUBEJS_DB_PORT=1433
   CUBEJS_API_SECRET=your-secret-key
   ```

3. **Copy Schema Files**
   Copy all `.js` files from this directory to `schema/` directory in your Cube.js project.

4. **Start Cube.js Server**
   ```bash
   npm run dev
   ```

5. **Update Frontend Environment**
   Add to your frontend `.env` file:
   ```env
   REACT_APP_CUBEJS_TOKEN=your-cube-token
   REACT_APP_CUBEJS_API_URL=http://localhost:4000/cubejs-api/v1
   ```

## Schema Files

- `Applications.js` - Main applications cube with measures and dimensions
- `Users.js` - Users/Officers cube for performance analytics
- `WorkflowHistory.js` - Application workflow tracking cube
- `Documents.js` - Document management analytics

## Available Measures and Dimensions

### Applications Cube
**Measures:**
- `Applications.count` - Total number of applications
- `Applications.pendingCount` - Applications in pending status
- `Applications.approvedCount` - Approved applications
- `Applications.rejectedCount` - Rejected applications
- `Applications.averageProcessingTime` - Average time to process

**Dimensions:**
- `Applications.status` - Workflow status
- `Applications.position` - Position type (Architect, Engineer, etc.)
- `Applications.assignedTo` - Assigned officer name
- `Applications.applicantName` - Applicant name
- `Applications.submittedDate` - Application submission date

### Common Queries

1. **Applications by Position (Pie Chart)**
   ```javascript
   {
     measures: ['Applications.count'],
     dimensions: ['Applications.position']
   }
   ```

2. **Applications by Workflow Stage (Bar Chart)**
   ```javascript
   {
     measures: ['Applications.count'],
     dimensions: ['Applications.status']
   }
   ```

3. **Drill-down by Position and Status**
   ```javascript
   {
     measures: ['Applications.count'],
     dimensions: ['Applications.status', 'Applications.position'],
     filters: [
       {
         member: 'Applications.position',
         operator: 'equals',
         values: ['Architect']
       }
     ]
   }
   ```

4. **Time Series Analysis**
   ```javascript
   {
     measures: ['Applications.count'],
     timeDimensions: [
       {
         dimension: 'Applications.submittedDate',
         granularity: 'month',
         dateRange: 'last 12 months'
       }
     ]
   }
   ```

## API Endpoints

Once Cube.js server is running, these endpoints will be available:

- `GET /cubejs-api/v1/load` - Execute queries
- `GET /cubejs-api/v1/meta` - Get schema metadata
- `POST /cubejs-api/v1/load` - Execute complex queries

## Integration with Frontend

The frontend components in `src/components/charts/` are already configured to work with these schemas. They will automatically switch from mock data to real Cube.js data once the server is running.

## Performance Optimization

1. **Pre-aggregations** - Defined in schema files for faster query performance
2. **Indexes** - Ensure database indexes on frequently queried columns
3. **Caching** - Cube.js automatically caches query results
4. **Partitioning** - Consider partitioning large tables by date

## Security

- API tokens are used for authentication
- Implement row-level security if needed
- Use HTTPS in production
- Restrict API access by IP if required
