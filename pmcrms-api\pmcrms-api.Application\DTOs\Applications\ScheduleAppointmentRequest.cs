using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Application.DTOs.Applications;

public class ScheduleAppointmentRequest
{
    [Required]
    public int ApplicationId { get; set; }
    
    [Required]
    public string ReviewDate { get; set; } = string.Empty; // Format: dd/MM/yyyy HH:mm
    
    [Required]
    [StringLength(100)]
    public string ContactPerson { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]  
    public string Place { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string RoomNumber { get; set; } = string.Empty;
    
    public string? Notes { get; set; }
}

public class ScheduleAppointmentResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public AppointmentDetails? Data { get; set; }
}

public class AppointmentDetails
{
    public int ApplicationId { get; set; }
    public string ApplicationNumber { get; set; } = string.Empty;
    public string ApplicantName { get; set; } = string.Empty;
    public string ReviewDate { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string Place { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string PreviousStatus { get; set; } = string.Empty;
    public string NewStatus { get; set; } = string.Empty;
    public DateTime ScheduledAt { get; set; }
}
