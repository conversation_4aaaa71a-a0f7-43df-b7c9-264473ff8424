import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Shield, ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react'
import { InputField, FormButton } from '../../components/ui/forms'
import { AdminAuthService, AdminCredentials, PasswordChangeRequest } from '../../lib/adminAuth'

const AdminLoginPage: React.FC = () => {
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showPasswordChange, setShowPasswordChange] = useState(false)
  const [isFirstTime, setIsFirstTime] = useState(false)
  
  // Login form state
  const [credentials, setCredentials] = useState<AdminCredentials>({
    email: '',
    password: ''
  })

  // Password change form state
  const [passwordData, setPasswordData] = useState<PasswordChangeRequest>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // Form validation errors
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    // Check if user is already authenticated
    if (AdminAuthService.isAuthenticated()) {
      navigate('/admin/dashboard', { replace: true })
      return
    }

    // Check admin setup status
    checkSetupStatus()
  }, [navigate])

  const checkSetupStatus = async () => {
    try {
      const status = await AdminAuthService.checkSetupStatus()
      setIsFirstTime(!status.isSetupComplete)
    } catch (error: any) {
      console.error('Failed to check setup status:', error)
      // If the API endpoint doesn't exist, assume setup is needed
      if (error.message.includes('404') || error.message.includes('Not Found')) {
        setIsFirstTime(true)
      }
    }
  }

  const validateLoginForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!credentials.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(credentials.email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (!credentials.password.trim()) {
      errors.password = 'Password is required'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validatePasswordForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!passwordData.currentPassword.trim()) {
      errors.currentPassword = 'Current password is required'
    }

    if (!passwordData.newPassword.trim()) {
      errors.newPassword = 'New password is required'
    } else if (passwordData.newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters long'
    }

    if (!passwordData.confirmPassword.trim()) {
      errors.confirmPassword = 'Please confirm your new password'
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    console.log('Login form submitted with:', credentials) // Debug log
    
    if (!validateLoginForm()) {
      console.log('Form validation failed') // Debug log
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      console.log('Attempting login...') // Debug log
      const result = await AdminAuthService.login(credentials)
      
      console.log('Login result:', result) // Debug log
      
      if (result && (result.token || result.success)) {
        console.log('Login successful, setting success message') // Debug log
        setSuccess('Login successful! Redirecting...')
        
        // Check if password change is required (only if API is available)
        try {
          const status = await AdminAuthService.checkSetupStatus()
          if (status.requiresPasswordChange) {
            console.log('Password change required') // Debug log
            setShowPasswordChange(true)
            setIsLoading(false)
            return
          }
        } catch (error) {
          // If setup status check fails, proceed to dashboard
          console.warn('Setup status check failed, proceeding to dashboard')
        }

        // Navigate immediately for demo mode or after successful API login
        console.log('Navigating to admin dashboard...') // Debug log
        setTimeout(() => {
          console.log('Executing navigation...') // Debug log
          navigate('/admin/dashboard', { replace: true })
        }, 500) // Reduced timeout for better UX
      } else {
        console.log('Login failed - no token or success flag') // Debug log
        setError(result?.message || 'Login failed - no token received')
      }
    } catch (error: any) {
      console.error('Login error caught:', error) // Debug log
      // Check if it's a 404 error (API endpoint doesn't exist)
      if (error.message.includes('404') || error.message.includes('Not Found')) {
        setError('Admin login is not yet implemented on the backend. The frontend is ready, but the backend API endpoints need to be created.')
      } else {
        setError(error.message || 'Login failed. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validatePasswordForm()) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await AdminAuthService.changePassword(passwordData)
      
      if (result.success) {
        setSuccess('Password changed successfully! Redirecting to dashboard...')
        setTimeout(() => {
          navigate('/admin/dashboard', { replace: true })
        }, 1500)
      } else {
        setError(result.message || 'Failed to change password')
      }
    } catch (error: any) {
      setError(error.message || 'Failed to change password')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInitializeAdmin = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await AdminAuthService.initializeAdmin()
      
      if (result.success) {
        setSuccess('Admin account created successfully!')
        setIsFirstTime(false)
        setCredentials({ email: result.user.email, password: 'Admin@123!' })
      } else {
        setError('Failed to initialize admin account')
      }
    } catch (error: any) {
      if (error.message.includes('404') || error.message.includes('Not Found')) {
        setError('Admin initialization is not yet implemented on the backend. Please contact the system administrator.')
      } else {
        setError(error.message || 'Failed to initialize admin account')
      }
    } finally {
      setIsLoading(false)
    }
  }

  if (showPasswordChange) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Shield className="h-12 w-12 text-primary" />
            </div>
            <CardTitle className="text-2xl">Change Password</CardTitle>
            <CardDescription>
              Please change your default password for security
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handlePasswordChange} className="space-y-4">
              <InputField
                label="Current Password"
                type="password"
                autoComplete="current-password"
                required
                value={passwordData.currentPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                error={validationErrors.currentPassword}
                placeholder="Enter current password"
              />

              <InputField
                label="New Password"
                type="password"
                autoComplete="new-password"
                required
                value={passwordData.newPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                error={validationErrors.newPassword}
                placeholder="Enter new password"
              />

              <InputField
                label="Confirm New Password"
                type="password"
                autoComplete="new-password"
                required
                value={passwordData.confirmPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                error={validationErrors.confirmPassword}
                placeholder="Confirm new password"
              />

              {error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {success && (
                <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-md">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">{success}</span>
                </div>
              )}

              <FormButton
                type="submit"
                variant="primary"
                size="lg"
                loading={isLoading}
                className="w-full"
              >
                Change Password
              </FormButton>
            </form>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Shield className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">Admin Login</CardTitle>
          <CardDescription>
            {isFirstTime ? 'Initialize Admin Account' : 'System administration access'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {isFirstTime ? (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  No admin account found. Click below to create the initial admin account.
                </p>
                <FormButton
                  onClick={handleInitializeAdmin}
                  variant="primary"
                  size="lg"
                  loading={isLoading}
                  className="w-full"
                >
                  Initialize Admin Account
                </FormButton>
              </div>
            </div>
          ) : (
            <form onSubmit={handleLogin} className="space-y-4">
              <InputField
                label="Email"
                type="email"
                autoComplete="email"
                required
                value={credentials.email}
                onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                error={validationErrors.email}
                placeholder="<EMAIL>"
              />

              <InputField
                label="Password"
                type="password"
                autoComplete="current-password"
                required
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                error={validationErrors.password}
                placeholder="Enter your password"
              />

              <FormButton
                type="submit"
                variant="primary"
                size="lg"
                loading={isLoading}
                className="w-full"
              >
                Sign In
              </FormButton>

              {/* Development bypass - remove in production */}
              <div className="text-center pt-4 border-t">
                <p className="text-xs text-gray-500 mb-2">Development Mode</p>
                <FormButton
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Simulate successful login for testing
                    localStorage.setItem('adminToken', 'demo-token-123')
                    localStorage.setItem('adminUser', JSON.stringify({
                      id: 1,
                      email: '<EMAIL>',
                      role: 'Admin'
                    }))
                    setSuccess('Demo login successful!')
                    setTimeout(() => navigate('/admin/dashboard', { replace: true }), 1000)
                  }}
                  className="w-full"
                >
                  Demo Dashboard Access
                </FormButton>
              </div>
            </form>
          )}

          {error && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          <div className="text-center">
            <Link to="/login" className="text-sm text-blue-600 hover:text-blue-800">
              Public User Login
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default AdminLoginPage
