// User and Authentication Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  role: UserRole
  isEmailVerified: boolean
  isPhoneVerified?: boolean
  profilePicture?: string
  createdAt: string
  updatedAt: string
}

export type UserRole = 
  | 'Admin'
  | 'PUBLIC_USER'
  | 'JRENGG_ARCH' 
  | 'JRENGG_STRU' 
  | 'JRENGG_LICE' 
  | 'JRENGG_SUPER1' 
  | 'JRENGG_SUPER2'
  | 'ASSIENGG_ARCH' 
  | 'ASSIENGG_STRU' 
  | 'ASSIENGG_LICE' 
  | 'ASSIENGG_SUPER1' 
  | 'ASSIENGG_SUPER2'
  | 'ExecutiveEngineer'
  | 'CityEngineer'
  | 'CLERK'
  | 'OFFLINE_PAYMENT_OFFICER'
  | 'ADMIN'

export interface AuthResponse {
  user: User
  token: string
  refreshToken: string
  expiresAt: string
}

export interface LoginResponse {
  success: boolean
  data: AuthResponse
  message?: string
}

// Guest Session Types
export interface GuestSession {
  email: string
  accessToken: string
  expiresAt: string
}

export interface OtpResponse {
  message: string
  sessionId: string
  expiresAt: string
}

// Application Types
export interface Application {
  id: string
  applicationNumber: string
  positionType: PositionType
  status: WorkflowStatus
  applicantId: string
  applicant: User
  personalInfo: PersonalInfo
  addresses: Address[]
  experiences: Experience[]
  qualifications: Qualification[]
  documents: Document[]
  payments: Payment[]
  workflowHistory: WorkflowHistory[]
  createdAt: string
  updatedAt: string
  submittedAt?: string
  completedAt?: string
}

export type PositionType = 'ARCHITECT' | 'LICENSE_ENGINEER' | 'STRUCTURAL_ENGINEER' | 'SUPERVISOR1' | 'SUPERVISOR2'

export type WorkflowStatus = 
  | 'DOCUMENT_VERIFICATION_PENDING'
  | 'JUNIOR_ENGINEER_PENDING'
  | 'ASSISTANT_ENGINEER_PENDING'
  | 'EXECUTIVE_ENGINEER_PENDING'
  | 'CITY_ENGINEER_PENDING'
  | 'PAYMENT_PENDING'
  | 'CLERK_PENDING'
  | 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING'
  | 'CITY_DIGITAL_SIGNATURE_PENDING'
  | 'FINAL_APPROVE'
  | 'REJECTED'

export interface PersonalInfo {
  firstName: string
  lastName: string
  dateOfBirth: string
  gender: 'MALE' | 'FEMALE' | 'OTHER'
  aadhaarNumber: string
  panNumber: string
  email: string
  phoneNumber: string
}

export interface Address {
  id: string
  type: 'PERMANENT' | 'LOCAL'
  addressLine1: string
  addressLine2?: string
  city: string
  state: string
  pinCode: string
  country: string
}

export interface Experience {
  id: string
  company: string
  designation: string
  startDate: string
  endDate?: string
  isCurrentJob: boolean
  description?: string
}

export interface Qualification {
  id: string
  degree: string
  institution: string
  university: string
  yearOfPassing: number
  percentage: number
  certificateDocument?: Document
}

export interface Document {
  id: string
  fileName: string
  fileType: string
  fileSize: number
  uploadedAt: string
  documentType: DocumentType
  url: string
  isVerified: boolean
  verifiedBy?: string
  verifiedAt?: string
  comments?: string
}

export type DocumentType = 
  | 'PHOTO'
  | 'SIGNATURE'
  | 'AADHAAR'
  | 'PAN'
  | 'EDUCATIONAL_CERTIFICATE'
  | 'EXPERIENCE_CERTIFICATE'
  | 'OTHER'

export interface Payment {
  id: string
  amount: number
  currency: string
  paymentMethod: 'ONLINE' | 'OFFLINE'
  status: PaymentStatus
  transactionId?: string
  paymentGateway?: string
  paidAt?: string
  receiptUrl?: string
}

export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED'

export interface WorkflowHistory {
  id: string
  status: WorkflowStatus
  assignedTo?: User
  assignedById?: string
  comments?: string
  createdAt: string
  completedAt?: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Form Types
export interface ApplicationFormData {
  personalInfo: PersonalInfo
  permanentAddress: Omit<Address, 'id' | 'type'>
  localAddress: Omit<Address, 'id' | 'type'>
  sameAsPermanent: boolean
  experiences: Omit<Experience, 'id'>[]
  qualifications: Omit<Qualification, 'id'>[]
  positionType: PositionType
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  isRead: boolean
  createdAt: string
  url?: string
}
