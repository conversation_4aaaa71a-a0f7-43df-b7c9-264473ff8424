{"name": "pmcrms-cubejs-analytics", "version": "1.0.0", "description": "Cube.js analytics server for PMCRMS application", "main": "index.js", "scripts": {"dev": "cubejs-server", "start": "NODE_ENV=production cubejs-server", "build": "echo 'No build step required'", "test": "echo 'No tests configured'", "playground": "cubejs-playground"}, "keywords": ["analytics", "cubejs", "pmcrms", "business-intelligence", "dashboard"], "author": "PMCRMS Development Team", "license": "MIT", "dependencies": {"@cubejs-backend/server": "^0.34.0", "@cubejs-backend/server-core": "^0.34.0", "@cubejs-backend/mssql-driver": "^0.34.0", "@cubejs-backend/postgres-driver": "^0.34.0", "@cubejs-backend/mysql-driver": "^0.34.0", "@cubejs-backend/redis-driver": "^0.34.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.0", "jsonwebtoken": "^9.0.0", "helmet": "^6.0.0", "express-rate-limit": "^6.0.0"}, "devDependencies": {"@cubejs-backend/playground": "^0.34.0", "nodemon": "^2.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "your-repository-url"}, "cubejs": {"apiSecret": "CHANGE_THIS_SECRET_IN_PRODUCTION", "dbType": "mssql", "dbHost": "localhost", "dbPort": "1433", "dbName": "PMCRMSDatabase", "webSocketsBasePath": "/", "playgroundEnabled": false, "telemetry": false}, "config": {"port": 4000, "host": "0.0.0.0"}}