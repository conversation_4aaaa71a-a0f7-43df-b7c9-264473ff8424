import React from 'react'
import { Bell, User, LogOut } from 'lucide-react'
import { useAuthStore } from '../store'
import { Button } from './ui/button'
import Logo from './Logo'

const TopBar: React.FC = () => {
  const { user, logout } = useAuthStore()

  const handleLogout = () => {
    logout()
    window.location.href = '/'
  }

  return (
    <div className="bg-white shadow-sm border-b border-gray-200 h-16 fixed w-full z-50">
      <div className="flex items-center justify-between h-full px-6">
        <div className="flex items-center">
          <Logo size="md" showText={true} />
        </div>
        
        <div className="flex items-center space-x-4">
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Bell className="h-5 w-5" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-700">
                {user?.firstName} {user?.lastName}
              </span>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-gray-600 hover:text-gray-900"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TopBar
