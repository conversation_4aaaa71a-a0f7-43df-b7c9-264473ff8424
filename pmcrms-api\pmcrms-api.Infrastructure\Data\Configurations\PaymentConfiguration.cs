using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using pmcrms_api.Domain.Entities;

namespace pmcrms_api.Infrastructure.Data.Configurations;

public class PaymentConfiguration : IEntityTypeConfiguration<Payment>
{
    public void Configure(EntityTypeBuilder<Payment> builder)
    {
        builder.ToTable("Payments");
        
        builder.HasKey(p => p.Id);
        
        builder.Property(p => p.PaymentNumber)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(p => p.Amount)
            .HasPrecision(18, 2);
            
        builder.Property(p => p.PaymentMethod)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(p => p.Status)
            .IsRequired()
            .HasMaxLength(20);
            
        builder.Property(p => p.PaymentDate)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.HasIndex(p => p.PaymentNumber)
            .IsUnique();
            
        // Relationship with Application
        builder.HasOne(p => p.Application)
            .WithMany()
            .HasForeignKey(p => p.ApplicationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
