Create a modern, responsive web application frontend for a Professional Municipal Corporation Registration Management System (PMCRMS) with the following specifications:

### **Core Application Architecture**
- **Framework**: React 18+ with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand or React Query for API state
- **Routing**: React Router v6
- **Form Handling**: React Hook Form with Zod validation
- **HTTP Client**: Axios with interceptors
- **Authentication**: JWT token-based with role-based access control

### **User Roles & Access Levels**
1. **Public Users** - Application submission and certificate download
2. **Junior Engineers** (<PERSON>ENG<PERSON>_ARCH, JRENGG_STRU, JRENGG_LICE, JRENGG_SUPER1, JRENGG_SUPER2)
3. **Assistant Engineers** (ASSIENGG_ARCH, ASSIENGG_STRU, ASSIENGG_LICE, ASSIENGG_SUPER1, ASSIENGG_SUPER2)
4. **Executive Engineers** - Review and digital signatures
5. **City Engineers** - Final approvals and digital signatures
6. **Clerks** - Post-payment processing
7. **Offline Payment Officers** - Payment verification
8. **Admins** - System administration and user management

### **Position Types for Applications**
- Architect
- License Engineer  
- Structural Engineer
- Supervisor1
- Supervisor2

### **Complete Workflow States**
1. DOCUMENT_VERIFICATION_PENDING
2. JUNIOR_ENGINEER_PENDING
3. ASSISTANT_ENGINEER_PENDING
4. EXECUTIVE_ENGINEER_PENDING
5. CITY_ENGINEER_PENDING
6. PAYMENT_PENDING
7. CLERK_PENDING
8. EXECUTIVE_DIGITAL_SIGNATURE_PENDING
9. CITY_DIGITAL_SIGNATURE_PENDING
10. FINAL_APPROVE
11. REJECTED



Generate the following pages and components for the PMCRMS frontend:

### **1. Public Pages**
**Landing Page (/)**
- Hero section with system overview
- Quick application status check
- Guest certificate download section
- User registration/login buttons

**Application Form (/apply)**
- Multi-step form wizard (Personal Info → Address → Experience → Qualifications → Review)
- Position selection (Architect, License Engineer, Structural Engineer, Supervisor1, Supervisor2)
- Address collection (Permanent and Local with "Same as Permanent" option)
- Experience collection with dynamic add/remove
- Qualification collection with file uploads
- Form validation with real-time feedback
- Progress indicator

**Application Status (/status)**
- Application number search
- Real-time workflow status display
- Timeline visualization of approval process
- Document download section

**Guest Login (/guest)**
- Simple login for certificate download
- Application number + basic details verification

### **2. Authentication Pages**
**User Login (/login)**
- Email/password authentication
- OTP-based login option
- Role-based redirection

**Officer Login (/officer/login)**
- Separate login for all officer types
- Password setup for invited officers

**Admin Login (/admin/login)**
- Admin authentication
- System setup access

### **3. Officer Dashboard Pages**
**Junior Engineer Dashboard (/dashboard/junior)**
- Assigned applications list
- Document verification interface  
- Appointment scheduling system
- Digital signature integration
- Filter by position type and status

**Assistant Engineer Dashboard (/dashboard/assistant)**
- Applications pending review
- Approval/rejection interface
- Digital signature for recommendation forms
- Workload management

**Executive Engineer Dashboard (/dashboard/executive)**
- Two-stage workflow (Review + Digital Signature)
- Recommendation form approval
- Certificate digital signature (HSM integration)
- Priority queue management

**City Engineer Dashboard (/dashboard/city)**
- Final approval interface
- Certificate digital signature
- System oversight tools
- Bulk operations

**Clerk Dashboard (/dashboard/clerk)**
- Post-payment application processing
- Email notification management
- Status update tools

**Admin Dashboard (/dashboard/admin)**
- User management (invite officers, manage roles)
- System configuration
- Workflow monitoring
- Reports and analytics


Create the following reusable components using shadcn/ui and Tailwind CSS:

### **Form Components**
- `ApplicationWizard` - Multi-step form with progress indicator
- `AddressInput` - Address form with auto-complete
- `ExperienceForm` - Dynamic experience entry with validation
- `QualificationForm` - File upload with preview
- `DocumentUpload` - Drag-and-drop file upload with progress

### **Dashboard Components**  
- `ApplicationCard` - Summary card with status badge
- `StatusTimeline` - Visual workflow progress
- `WorkflowActions` - Role-specific action buttons
- `DigitalSignature` - Signature pad integration
- `AppointmentScheduler` - Calendar-based scheduling

### **Data Display Components**
- `ApplicationTable` - Sortable, filterable data table
- `StatusBadge` - Color-coded status indicators
- `UserRoleBadge` - Role identification badges  
- `DocumentViewer` - PDF preview modal
- `NotificationCenter` - Real-time updates

### **Navigation Components**
- `RoleBasedNav` - Dynamic navigation based on user role
- `Breadcrumbs` - Page navigation trail
- `SidebarMenu` - Collapsible sidebar
- `TopBar` - Header with user info and notifications

### **Common UI Elements**
- `LoadingSpinner` - Various loading states
- `EmptyState` - No data placeholders
- `ErrorBoundary` - Error handling wrapper
- `ConfirmDialog` - Action confirmation modals
- `Toast` - Success/error notifications

Implement comprehensive API integration with the following services:

### **API Base Configuration**
```typescript
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:7243/api'

// HTTP client with interceptors for authentication and error handling
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
})

// Request interceptor for JWT token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle token expiration
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)


## 🎨 **Styling & Theme Prompt**
Color Palette
Primary: Blue (#1e40af) - Official government blue
Secondary: Green (#059669) - Success/approved states
Warning: Amber (#d97706) - Pending/review states
Danger: Red (#dc2626) - Rejected/error states
Neutral: Gray scale for backgrounds and text
Typography
Headings: Inter font family, bold weights
Body: Inter regular for readability
Code/Numbers: JetBrains Mono for application numbers
Layout Principles
Clean, minimal design with ample whitespace
Consistent spacing using Tailwind's spacing scale
Responsive design (mobile-first approach)
Accessibility compliance (WCAG 2.1 AA)
Component Styling
Rounded corners (rounded-lg) for cards and buttons
Subtle shadows for depth
Hover states with smooth transitions
Focus states for keyboard navigation
Loading states with skeleton loaders





## 🔐 **Security & Authentication Prompt**

Implement robust security measures:

Authentication Flow
Login with email/password or OTP
JWT token storage (secure, httpOnly if possible)
Automatic token refresh before expiration
Role-based route protection
Session timeout handling
Route Protection
Protected routes based on user roles
Redirect unauthorized users
Remember intended destination after login
Data Security
Input sanitization for all forms
File upload validation (type, size limits)
XSS prevention in dynamic content rendering
Secure file viewing (no direct downloads)
Error Handling
User-friendly error messages
Network error recovery
Graceful degradation for offline scenarios



## 📊 **Real-time Features Prompt**


Implement real-time functionality:

WebSocket Integration
Real-time application status updates
Notification delivery
Workflow progress updates
Multi-user collaboration indicators
Notification System
Toast notifications for immediate feedback
Email-style notification center
Push notifications for mobile PWA
Sound alerts for urgent updates
Live Data Updates
Auto-refresh application lists
Live status changes in workflows
Real-time user activity indicators
Automatic form saves (draft functionality)



## 📱 **Progressive Web App Prompt**


Configure the application as a PWA:

PWA Features
Service worker for caching
Offline functionality for viewing applications
App-like experience on mobile devices
Push notification support
Install prompt for desktop/mobile
Caching Strategy
Cache static assets (CSS, JS, images)
API response caching for frequently accessed data
Offline page for network failures
Background sync for form submissions


## 🧪 **Testing Prompt**


Implement comprehensive testing:

Testing Stack
Jest + React Testing Library for unit tests
Cypress for end-to-end testing
MSW (Mock Service Worker) for API mocking
Accessibility testing with jest-axe
Test Coverage Areas
Component rendering and interactions
Form validation and submissions
API integration and error handling
Authentication flows
Role-based access control
Workflow transitions



## 🚀 **Deployment Prompt**

Prepare the application for production deployment:

Build Configuration
Environment-specific configurations
Code splitting for optimal loading
Asset optimization and compression
Source map generation for debugging
Docker Configuration
Multi-stage Docker build
Nginx configuration for SPA routing
Health check endpoints
Environment variable injection
CI/CD Pipeline
Automated testing on pull requests
Build and deploy pipeline
Environment promotion workflow
Rollback capabilities


---

## 💡 **Additional Implementation Notes**

1. **Marathi Language Support**: Include i18n setup for bilingual interface (English/Marathi)

2. **Mobile Responsiveness**: Design-first approach for mobile devices (many users will access via phones)

3. **Print Functionality**: Proper print styles for certificates and forms

4. **Accessibility**: Full keyboard navigation, screen reader support, high contrast mode

5. **Performance**: Lazy loading, virtualized lists for large datasets, optimized images

6. **User Experience**: Loading states, empty states, error boundaries, optimistic updates

Use these prompts systematically to build a complete, professional frontend application that perfectly matches the comprehensive backend API we've developed! 🎯---

## 💡 **Additional Implementation Notes**

1. **Marathi Language Support**: Include i18n setup for bilingual interface (English/Marathi)

2. **Mobile Responsiveness**: Design-first approach for mobile devices (many users will access via phones)

3. **Print Functionality**: Proper print styles for certificates and forms

4. **Accessibility**: Full keyboard navigation, screen reader support, high contrast mode

5. **Performance**: Lazy loading, virtualized lists for large datasets, optimized images

6. **User Experience**: Loading states, empty states, error boundaries, optimistic updates

Use these prompts systematically to build a complete, professional frontend application that perfectly matches the comprehensive backend API we've developed!
