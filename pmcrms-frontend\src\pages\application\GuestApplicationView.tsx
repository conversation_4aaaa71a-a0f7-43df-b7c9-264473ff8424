import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { 
  ArrowLeft, 
  FileText, 
  User,
  MapPin,
  Shield,
  GraduationCap,
  Briefcase,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { useGuestAuth } from '../../hooks/useGuestAuth'
import Logo from '../../components/Logo'

// Application interface - updated to match actual API response
interface ApplicationDetails {
  applicationNumber: string
  firstName: string
  lastName: string
  position: string
  status: string
  submissionDate: string
  approvalDate?: string | null
  certificateNumber?: string | null
  hasCertificate: boolean
  // Optional fields that might be added later
  middleName?: string
  motherName?: string
  mobileNumber?: string
  emailId?: string
  birthDate?: string
  bloodGroup?: string
  height?: string
  gender?: string
  localAddress?: {
    flatHouseNumber: string
    streetAddress: string
    addressLine?: string
    country: string
    state: string
    city: string
    postalCode: string
  }
  permanentAddress?: {
    flatHouseNumber: string
    streetAddress: string
    address?: string
    country: string
    state: string
    city: string
    postalCode: string
  }
  permanentSameAsLocal?: boolean
  panNumber?: string
  aadharNumber?: string
  qualification?: {
    instituteName: string
    universityName: string
    courseSpecialization: string
    degreeProgram: string
    passingMonth: string
    passingYear: string
  }
  experiences?: Array<{
    companyName: string
    position: string
    yearsOfExperience: string
    fromDate: string
    toDate: string
  }>
  documents?: Array<{
    documentName: string
  }>
  updatedAt?: string
  guestEmail?: string
}

// Status configuration
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'FINAL_APPROVE':
    case 'APPROVED':
      return { 
        icon: CheckCircle, 
        color: 'text-green-600 bg-green-100 border-green-200', 
        label: 'Approved'
      }
    case 'REJECTED':
      return { 
        icon: XCircle, 
        color: 'text-red-600 bg-red-100 border-red-200', 
        label: 'Rejected'
      }
    case 'UNDER_REVIEW':
      return { 
        icon: Clock, 
        color: 'text-blue-600 bg-blue-100 border-blue-200', 
        label: 'Under Review'
      }
    case 'DOCUMENT_VERIFICATION_PENDING':
      return { 
        icon: AlertCircle, 
        color: 'text-yellow-600 bg-yellow-100 border-yellow-200', 
        label: 'Document Verification Pending'
      }
    case 'PAYMENT_PENDING':
      return { 
        icon: AlertCircle, 
        color: 'text-amber-600 bg-amber-100 border-amber-200', 
        label: 'Payment Pending'
      }
    default:
      return { 
        icon: Clock, 
        color: 'text-gray-600 bg-gray-100 border-gray-200', 
        label: 'Submitted'
      }
  }
}

// Utility function to format dates
const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateString
  }
}

const GuestApplicationView: React.FC = () => {
  const { applicationNumber } = useParams<{ applicationNumber: string }>()
  const navigate = useNavigate()
  const { guestSession } = useGuestAuth()
  const [application, setApplication] = useState<ApplicationDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check both hook state and localStorage directly
    const guestEmail = localStorage.getItem('guest_email')
    
    // Also try to get email from parsed session data
    let sessionEmail = null
    const sessionData = localStorage.getItem('guest_session')
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData)
        sessionEmail = parsedSession.email
      } catch (error) {
        console.error('Error parsing session data:', error)
      }
    }
    
    const hasValidSession = guestSession?.email || guestEmail || sessionEmail
    
    if (!hasValidSession) {
      console.log('No guest session found, redirecting to guest login')
      navigate('/guest')
      return
    }
    
    // Only fetch if we have session
    fetchApplicationDetails()
  }, [applicationNumber, guestSession, navigate])

  const fetchApplicationDetails = async () => {
    try {
      setLoading(true)
      setError(null)

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://localhost:7249/api'
      
      // Get guest email from hook, localStorage, or parsed session
      let guestEmail = guestSession?.email || localStorage.getItem('guest_email')
      
      // Try to get email from parsed session if not found above
      if (!guestEmail) {
        const sessionData = localStorage.getItem('guest_session')
        if (sessionData) {
          try {
            const parsedSession = JSON.parse(sessionData)
            guestEmail = parsedSession.email
          } catch (error) {
            console.error('Error parsing session data:', error)
          }
        }
      }
      
      if (!guestEmail) {
        setError('No guest session found')
        navigate('/guest')
        return
      }
      
      console.log('Using guest email for details fetch:', guestEmail)
      
      // Get access token from session data
      let accessToken = null
      const sessionData = localStorage.getItem('guest_session')
      if (sessionData) {
        try {
          const parsedSession = JSON.parse(sessionData)
          accessToken = parsedSession.accessToken
        } catch (error) {
          console.error('Error parsing session for access token:', error)
        }
      }
      
      if (!accessToken) {
        setError('No valid access token found')
        navigate('/guest')
        return
      }
      
      console.log('Making API call with access token')
      
      const response = await fetch(`${API_BASE_URL}/applications/guest/${applicationNumber}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      })

      if (!response.ok) {
        if (response.status === 404) {
          setError('Application not found')
          return
        }
        if (response.status === 401) {
          setError('Unauthorized access')
          return
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setApplication(data)

    } catch (err: any) {
      console.error('Error fetching application details:', err)
      setError(`Failed to load application details: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading application details...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link to="/dashboard/guest">
            <Button>Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Application Not Found</h3>
          <Link to="/dashboard/guest">
            <Button>Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  const statusConfig = getStatusConfig(application.status)
  const StatusIcon = statusConfig.icon

  const totalExperience = application.experiences?.reduce((sum, exp) => {
    return sum + parseFloat(exp.yearsOfExperience || '0')
  }, 0) || 0

  // Helper function to get display value - shows actual data or placeholder
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Logo size="sm" showText />
              <div className="hidden sm:block h-6 w-px bg-gray-300" />
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold text-gray-900">Application Details</h1>
                <p className="text-sm text-gray-500">{application.applicationNumber}</p>
              </div>
            </div>
            
            <Link to="/dashboard/guest">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Application Header */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${statusConfig.color}`}>
                  <StatusIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">{statusConfig.label}</span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Application Details</h2>
                  <p className="text-gray-600">{application.applicationNumber}</p>
                </div>
              </div>
              <div className="text-right text-sm text-gray-600">
                <p>Submitted: {formatDate(application.submissionDate)}</p>
                {application.approvalDate && (
                  <p>Approved: {formatDate(application.approvalDate)}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Read-Only Application Form - All Steps in Single View */}
        <form className="space-y-8">
          
          {/* Step 1: Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Step 1: Basic Information</span>
              </CardTitle>
              <CardDescription>Personal details and contact information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Position */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Position Applied For <span className="text-red-500">*</span>
                </label>
                <Input value={application.position || ''} readOnly className="bg-gray-50" />
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <Input value={application.firstName || ''} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                  <Input 
                    value={application.localAddress?.addressLine || ''} 
                    readOnly 
                    className={`${application.middleName ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                  />
                  {!application.middleName && (
                    <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <Input value={application.lastName || ''} readOnly className="bg-gray-50" />
                </div>
              </div>

              {/* Mother's Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mother's Name <span className="text-red-500">*</span>
                </label>
                <Input 
                  value={application.motherName || ''} 
                  readOnly 
                  className="bg-gray-50" 
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mobile Number <span className="text-red-500">*</span>
                  </label>
                  <Input 
                    value={application.mobileNumber || ''} 
                    readOnly 
                    className="bg-gray-50" 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email ID <span className="text-red-500">*</span>
                  </label>
                  <Input 
                    value={application.emailId || ''} 
                    readOnly 
                    className="bg-gray-50" 
                  />
                </div>
              </div>

              {/* Personal Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Birth Date <span className="text-red-500">*</span>
                  </label>
                  <Input 
                    value={application.localAddress?.addressLine || ''} 
                    readOnly 
                    className="bg-gray-50" 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Blood Group <span className="text-red-500">*</span>
                  </label>
                  <Input 
                    value={application.bloodGroup || ''} 
                    readOnly 
                    className="bg-gray-50" 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Height (in feet) <span className="text-red-500">*</span>
                  </label>
                  <Input 
                    value={application.height || ''} 
                    readOnly 
                    className="bg-gray-50" 
                  />
                </div>
              </div>

              {/* Gender */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gender <span className="text-red-500">*</span>
                </label>
                <Input 
                  value={application.localAddress?.addressLine || ''} 
                  readOnly 
                  className="bg-gray-50" 
                />
              </div>
            </CardContent>
          </Card>

          {/* Step 2: Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="w-5 h-5" />
                <span>Step 2: Address Information</span>
              </CardTitle>
              <CardDescription>Local and permanent address details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Local Address */}
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-4">Local Address</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Flat/House Number <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.flatHouseNumber || ''} 
                        readOnly 
                        className="bg-gray-50" 
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Street Address <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.streetAddress || ''} 
                        readOnly 
                        className="bg-gray-50" 
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Address Line</label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.localAddress?.addressLine ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                    />
                    {!application.localAddress?.addressLine && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.localAddress?.country ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.localAddress?.country && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        State <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.localAddress?.state ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.localAddress?.state && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        City <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.localAddress?.city ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.localAddress?.city && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Postal Code <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.localAddress?.postalCode ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.localAddress?.postalCode && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Permanent Address */}
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-4">Permanent Address</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Flat/House Number <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.flatHouseNumber ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.flatHouseNumber && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Street Address <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.streetAddress ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.streetAddress && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.permanentAddress?.address ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                    />
                    {!application.permanentAddress?.address && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.country ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.country && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        State <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.state ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.state && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        City <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.city ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.city && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Postal Code <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.permanentAddress?.postalCode ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`} 
                      />
                      {!application.permanentAddress?.postalCode && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: Identity Documents */}
          <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>Step 3: Identity Documents</span>
                </CardTitle>
                <CardDescription>PAN and Aadhar information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* PAN Information */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-4">PAN Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        PAN Number <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.panNumber ? 'bg-gray-50 font-mono uppercase' : 'bg-blue-50 border-blue-200 font-mono uppercase'}`} 
                      />
                      {!application.panNumber && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">PAN Attachment</label>
                      <div className="flex items-center space-x-2 p-3 border rounded-md bg-blue-50 border-blue-200">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <span className="text-sm text-gray-700">
                          pan_document.pdf
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    </div>
                  </div>
                </div>

                {/* Aadhar Information */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-4">Aadhar Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Aadhar Number <span className="text-red-500">*</span>
                      </label>
                      <Input 
                        value={application.localAddress?.addressLine || ''} 
                        readOnly 
                        className={`${application.aadharNumber ? 'bg-gray-50 font-mono' : 'bg-blue-50 border-blue-200 font-mono'}`} 
                      />
                      {!application.aadharNumber && (
                        <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Aadhar Attachment</label>
                      <div className="flex items-center space-x-2 p-3 border rounded-md bg-blue-50 border-blue-200">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <span className="text-sm text-gray-700">
                          aadhar_document.pdf
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

          {/* Step 4: Qualification */}
          <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <GraduationCap className="w-5 h-5" />
                  <span>Step 4: Qualification</span>
                </CardTitle>
                <CardDescription>Educational background information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Institute Name <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.instituteName ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.instituteName && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      University Name <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.universityName ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.universityName && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Specialization <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.courseSpecialization ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.courseSpecialization && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Degree Program <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.degreeProgram ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.degreeProgram && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Passing Month <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.passingMonth ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.passingMonth && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Passing Year <span className="text-red-500">*</span>
                    </label>
                    <Input 
                      value={application.localAddress?.addressLine || ''} 
                      readOnly 
                      className={`${application.qualification?.passingYear ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}
                    />
                    {!application.qualification?.passingYear && (
                      <p className="text-xs text-blue-600 mt-1">Sample data - not available in current API</p>
                    )}
                  </div>
                </div>

                {/* Qualification Documents */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Qualification Certificate</label>
                  <div className="flex items-center space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <span className="text-sm text-gray-700">Qualification Certificate (Uploaded)</span>
                  </div>
                </div>
              </CardContent>
            </Card>

          {/* Step 5: Experience */}
          {application.experiences && application.experiences.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="w-5 h-5" />
                  <span>Step 5: Experience</span>
                  <span className="ml-auto text-sm text-gray-500">
                    Total: {totalExperience.toFixed(1)} years
                  </span>
                </CardTitle>
                <CardDescription>Work experience and professional background</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {application.experiences.map((experience, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6 bg-gray-25">
                    <h4 className="text-md font-medium text-gray-900 mb-4">Experience {index + 1}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Company Name <span className="text-red-500">*</span>
                        </label>
                        <Input value={experience.companyName || ''} readOnly className="bg-gray-50" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Position <span className="text-red-500">*</span>
                        </label>
                        <Input value={experience.position || ''} readOnly className="bg-gray-50" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Years of Experience <span className="text-red-500">*</span>
                        </label>
                        <Input value={`${experience.yearsOfExperience || ''} years`} readOnly className="bg-gray-50" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          From Date <span className="text-red-500">*</span>
                        </label>
                        <Input value={experience.fromDate || ''} readOnly className="bg-gray-50" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          To Date <span className="text-red-500">*</span>
                        </label>
                        <Input value={experience.toDate || ''} readOnly className="bg-gray-50" />
                      </div>
                    </div>
                    
                    {/* Experience Certificate */}
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Experience Certificate</label>
                      <div className="flex items-center space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <span className="text-sm text-gray-700">Experience Certificate {index + 1} (Uploaded)</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="w-5 h-5" />
                  <span>Step 5: Experience</span>
                </CardTitle>
                <CardDescription>Work experience and professional background</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <Briefcase className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Experience details not available in this view</p>
                  <p className="text-sm">This information may be available in the complete application data</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 6: Documents */}
          {application.documents && application.documents.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Step 6: Documents</span>
                </CardTitle>
                <CardDescription>Required document attachments</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Document List */}
                <div className="space-y-4">
                  {application.documents.map((document, index) => (
                    <div key={index}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {document.documentName}
                      </label>
                      <div className="flex items-center space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <span className="text-sm text-gray-700">{document.documentName} (Uploaded)</span>
                        <span className="ml-auto text-xs text-green-600 font-medium">✓ Uploaded</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Step 6: Documents</span>
                </CardTitle>
                <CardDescription>Required document attachments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Document details not available in this view</p>
                  <p className="text-sm">This information may be available in the complete application data</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Application Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Application Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Application Number</label>
                  <Input value={application.localAddress?.addressLine || ''} readOnly className="bg-gray-50 font-mono" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Current Status</label>
                  <div className={`flex items-center space-x-2 px-3 py-2 rounded-md border ${statusConfig.color}`}>
                    <StatusIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">{statusConfig.label}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Submission Date</label>
                  <Input value={application.localAddress?.addressLine || ''} readOnly className="bg-gray-50" />
                </div>
                {application.approvalDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Approval Date</label>
                    <Input value={application.localAddress?.addressLine || ''} readOnly className="bg-gray-50" />
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Has Certificate</label>
                  <Input value={application.localAddress?.addressLine || ''} readOnly className="bg-gray-50" />
                </div>
                {application.certificateNumber && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Certificate Number</label>
                    <Input value={application.localAddress?.addressLine || ''} readOnly className="bg-gray-50 font-mono" />
                  </div>
                )}
              </div>

              {/* Status Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">Application Status Information</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      This is a read-only view of your submitted application. The information shown reflects 
                      the current status and available data in the system. Some sections may appear empty 
                      if detailed information is not yet available in this view.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  )
}

export default GuestApplicationView
