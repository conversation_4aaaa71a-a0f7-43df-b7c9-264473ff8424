import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs'
import { Badge } from '../../components/ui/badge'
import { FileText, Shield, CheckCircle, Clock, AlertCircle, User, MapPin, Calendar, Loader2 } from 'lucide-react'
import { useAuthStore } from '../../store'
import api from '../../lib/api'
import ApplicationDetailsModal from '../../components/ApplicationDetailsModal'

interface Application {
  id: number
  applicationNumber: string
  applicantName?: string
  firstName?: string
  lastName?: string
  position: string
  status?: string
  workflowState?: string
  submittedDate: string
  permanentAddress?: string
  phoneNumber?: string
  reviewDate?: string
  contactPerson?: string
  place?: string
  roomNumber?: string
}

const ExecutiveEngineerDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedApplicationId, setSelectedApplicationId] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState('executive-engineer-pending')
  const { token } = useAuthStore()

  const [executiveEngineerPendingApps, setExecutiveEngineerPendingApps] = useState<Application[]>([])
  const [executiveDigitalSignatureApps, setExecutiveDigitalSignatureApps] = useState<Application[]>([])
  const loadApplications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await api.get('/applications', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      
      console.log('Applications loaded:', response.data)
      
      // Handle the response structure - data is nested inside response.data.data
      const responseData = response.data
      if (responseData.success && Array.isArray(responseData.data)) {
        const allApps = responseData.data.map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState, // Use status field as workflowState
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        
        console.log('Processed applications:', allApps)
        
        const executiveEngApps = allApps.filter((app: Application) => 
          app.workflowState === 'EXECUTIVE_ENGINEER_PENDING'
        )
        
        console.log('Executive Engineer apps:', executiveEngApps)
        
        setExecutiveEngineerPendingApps(executiveEngApps)
        // Filter applications for digital signature stage
        const executiveDigitalSignature = allApps.filter((app: Application) =>
          app.workflowState === 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING'
        )
        setExecutiveDigitalSignatureApps(executiveDigitalSignature)
      } else {
        const allApps = (responseData || []).map((app: any) => ({
          ...app,
          id: typeof app.id === 'string' ? parseInt(app.id, 10) : app.id,
          workflowState: app.status || app.workflowState,
          firstName: app.applicantName?.split(' ')[0] || '',
          lastName: app.applicantName?.split(' ').slice(1).join(' ') || ''
        }))
        const executiveEngApps = allApps.filter((app: Application) =>
          app.workflowState === 'EXECUTIVE_ENGINEER_PENDING'
        )
        setExecutiveEngineerPendingApps(executiveEngApps)
        const executiveDigitalSignature = allApps.filter((app: Application) =>
          app.workflowState === 'EXECUTIVE_DIGITAL_SIGNATURE_PENDING'
        )
        setExecutiveDigitalSignatureApps(executiveDigitalSignature)
      }
    } catch (err: any) {
      console.error('Failed to load applications:', err)
      setError(err.response?.data?.message || 'Failed to load applications')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (token) {
      loadApplications()
    }
  }, [token])

  const handleViewDetails = (applicationId: number) => {
    console.log('handleViewDetails called with applicationId:', applicationId)
    setSelectedApplicationId(applicationId)
    setShowDetailsModal(true)
  }

  const handleCompleteReview = async (applicationId: number) => {
    try {
      const response = await api.post(`/applications/${applicationId}/complete-executive-review`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.data.success) {
        // Reload applications to show updated status
        loadApplications()
      }
    } catch (err: any) {
      console.error('Failed to complete review:', err)
      setError(err.response?.data?.message || 'Failed to complete review')
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Executive Engineer Dashboard</h1>
        <p className="text-gray-600">Final review and approve applications from Assistant Engineers</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Awaiting Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{executiveEngineerPendingApps.length}</div>
            <p className="text-xs text-muted-foreground">Applications for final review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">Applications approved today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Certificate Ready</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">Certificates ready this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1.8</div>
            <p className="text-xs text-muted-foreground">Avg days per application</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information - Remove after testing */}
      <Card className="mb-4">
        <CardContent className="pt-4">
          <div className="text-sm space-y-2">
            <div>Executive Engineer Pending Apps: {executiveEngineerPendingApps.length}</div>
            <div>
              <strong>Executive Engineer IDs:</strong> {executiveEngineerPendingApps.map(app => app.id).join(', ')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="executive-engineer-pending" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Executive Engineer Review ({executiveEngineerPendingApps.length})
          </TabsTrigger>
          <TabsTrigger value="executive-digital-signature-pending" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Digital Signature Pending ({executiveDigitalSignatureApps.length})
          </TabsTrigger>
        </TabsList>

        {/* Executive Engineer Pending Tab */}
        <TabsContent value="executive-engineer-pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Applications for Executive Engineer Review
              </CardTitle>
              <CardDescription>
                Applications approved by Assistant Engineers awaiting your final review and authorization
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading applications...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Applications</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadApplications} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : executiveEngineerPendingApps.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications for Review</h3>
                  <p className="text-gray-600 mb-4">
                    No applications are currently awaiting your final review.
                  </p>
                  <Button onClick={loadApplications} variant="outline">
                    Refresh
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {executiveEngineerPendingApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold">{app.firstName} {app.lastName}</span>
                          <span className="text-sm text-gray-500">#{app.applicationNumber}</span>
                        </div>
                        <Badge variant="outline" className="bg-purple-50 text-purple-800 border-purple-200">
                          Executive Review Needed
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>Position: {app.position}</span>
                        </div>
                        {app.permanentAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span>{app.permanentAddress.substring(0, 30)}...</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(app.submittedDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3 flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleViewDetails(app.id)}
                        >
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          View Documents
                        </Button>
                        <Button 
                          size="sm" 
                          className="bg-purple-600 hover:bg-purple-700 text-white"
                          onClick={() => handleCompleteReview(app.id)}
                        >
                          Authorize Application
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  <div className="text-center pt-4">
                    <Button onClick={loadApplications} variant="outline">
                      Refresh Applications
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Executive Engineer Digital Signature Tab */}
        <TabsContent value="executive-digital-signature-pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Applications Awaiting Digital Signature
              </CardTitle>
              <CardDescription>
                Applications authorized by you, pending your digital signature.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading applications...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Applications</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={loadApplications} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : executiveDigitalSignatureApps.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications for Digital Signature</h3>
                  <p className="text-gray-600 mb-4">
                    No applications are currently awaiting your digital signature.
                  </p>
                  <Button onClick={loadApplications} variant="outline">
                    Refresh
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {executiveDigitalSignatureApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-semibold">{app.firstName} {app.lastName}</span>
                          <span className="text-sm text-gray-500">#{app.applicationNumber}</span>
                        </div>
                        <Badge variant="outline" className="bg-green-50 text-green-800 border-green-200">
                          Digital Signature Needed
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>Position: {app.position}</span>
                        </div>
                        {app.permanentAddress && (
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span>{app.permanentAddress.substring(0, 30)}...</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(app.submittedDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleViewDetails(app.id)}
                        >
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          View Documents
                        </Button>
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700 text-white"
                          // onClick={() => handleDigitalSignature(app.id)}
                          disabled
                        >
                          Sign Digitally
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-4">
                    <Button onClick={loadApplications} variant="outline">
                      Refresh Applications
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Application Details Modal */}
      <ApplicationDetailsModal
        applicationId={selectedApplicationId}
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false)
          setSelectedApplicationId(null)
        }}
      />
    </div>
  )
}

export default ExecutiveEngineerDashboard
