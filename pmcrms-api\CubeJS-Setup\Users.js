// Users Analytics Cube
// This cube provides analytics for user management and performance

cube(`Users`, {
  sql: `
    SELECT 
      u.Id,
      u.FirstName,
      u.LastName,
      CONCAT(u.FirstName, ' ', u.LastName) as FullName,
      u.Email,
      u.PhoneNumber,
      u.CreatedAt,
      u.LastLogin,
      u.IsActive,
      r.Name as <PERSON>Name,
      CASE r.Name
        WHEN 'ADMIN' THEN 'System Administrator'
        WHEN 'JUNIOR_ENGINEER' THEN 'Junior Engineer'
        WHEN 'ASSISTANT_ENGINEER' THEN 'Assistant Engineer'
        WHEN 'EXECUTIVE_ENGINEER' THEN 'Executive Engineer'
        WHEN 'CITY_ENGINEER' THEN 'City Engineer'
        WHEN 'CLERK' THEN 'Clerk'
        ELSE r.Name
      END as RoleDisplayName,
      CASE 
        WHEN u.IsActive = 1 THEN 'Active'
        ELSE 'Inactive'
      END as Status,
      DATEDIFF(day, u.CreatedAt, GETUTCDATE()) as Days<PERSON><PERSON><PERSON>Created,
      CASE 
        WHEN u.LastLogin IS NULL THEN 0
        ELSE DATEDIFF(day, u.LastLogin, GETUTCDATE())
      END as DaysSinceLastLogin,
      (SELECT COUNT(*) FROM Applications WHERE AssignedToUserId = u.Id) as AssignedApplicationsCount,
      (SELECT COUNT(*) FROM Applications WHERE AssignedToUserId = u.Id AND CurrentState = 5) as CompletedApplicationsCount,
      YEAR(u.CreatedAt) as CreatedYear,
      MONTH(u.CreatedAt) as CreatedMonth,
      DATEPART(quarter, u.CreatedAt) as CreatedQuarter
    FROM Users u
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE r.Name != 'User' OR r.Name IS NULL  -- Exclude guest users
  `,

  measures: {
    count: {
      type: `count`,
      title: `Total Users`,
      description: `Total number of system users`
    },

    activeCount: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.IsActive = 1` }
      ],
      title: `Active Users`,
      description: `Number of active users`
    },

    inactiveCount: {
      type: `count`,
      filters: [
        { sql: `${CUBE}.IsActive = 0` }
      ],
      title: `Inactive Users`,
      description: `Number of inactive users`
    },

    totalAssignedApplications: {
      type: `sum`,
      sql: `AssignedApplicationsCount`,
      title: `Total Assigned Applications`,
      description: `Total applications assigned to all users`
    },

    totalCompletedApplications: {
      type: `sum`,
      sql: `CompletedApplicationsCount`,
      title: `Total Completed Applications`,
      description: `Total applications completed by all users`
    },

    averageAssignedApplications: {
      type: `avg`,
      sql: `AssignedApplicationsCount`,
      title: `Average Assigned Applications`,
      description: `Average number of applications per user`
    },

    averageCompletedApplications: {
      type: `avg`,
      sql: `CompletedApplicationsCount`,
      title: `Average Completed Applications`,
      description: `Average completed applications per user`
    },

    userProductivity: {
      type: `number`,
      sql: `
        CASE 
          WHEN SUM(AssignedApplicationsCount) > 0 THEN 
            (SUM(CompletedApplicationsCount) * 100.0 / SUM(AssignedApplicationsCount))
          ELSE 0 
        END
      `,
      title: `User Productivity (%)`,
      description: `Percentage of assigned applications completed`
    },

    averageDaysSinceLastLogin: {
      type: `avg`,
      sql: `DaysSinceLastLogin`,
      title: `Avg Days Since Last Login`,
      description: `Average days since users last logged in`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `number`,
      primaryKey: true,
      shown: false
    },

    fullName: {
      sql: `FullName`,
      type: `string`,
      title: `Full Name`,
      description: `Complete name of the user`
    },

    firstName: {
      sql: `FirstName`,
      type: `string`,
      title: `First Name`,
      description: `User's first name`
    },

    lastName: {
      sql: `LastName`,
      type: `string`,
      title: `Last Name`,
      description: `User's last name`
    },

    email: {
      sql: `Email`,
      type: `string`,
      title: `Email Address`,
      description: `User's email address`
    },

    roleName: {
      sql: `RoleName`,
      type: `string`,
      title: `Role`,
      description: `User's system role`
    },

    roleDisplayName: {
      sql: `RoleDisplayName`,
      type: `string`,
      title: `Role Display Name`,
      description: `User-friendly role name`
    },

    status: {
      sql: `Status`,
      type: `string`,
      title: `Account Status`,
      description: `Whether user account is active or inactive`
    },

    createdAt: {
      sql: `CreatedAt`,
      type: `time`,
      title: `Created Date`,
      description: `Date when user account was created`
    },

    lastLogin: {
      sql: `LastLogin`,
      type: `time`,
      title: `Last Login`,
      description: `Date of user's last login`
    },

    createdYear: {
      sql: `CreatedYear`,
      type: `number`,
      title: `Created Year`,
      description: `Year when user was created`
    },

    createdMonth: {
      sql: `CreatedMonth`,
      type: `number`,
      title: `Created Month`,
      description: `Month when user was created`
    },

    createdQuarter: {
      sql: `CreatedQuarter`,
      type: `number`,
      title: `Created Quarter`,
      description: `Quarter when user was created`
    },

    workloadCategory: {
      sql: `
        CASE 
          WHEN AssignedApplicationsCount = 0 THEN 'No Assignments'
          WHEN AssignedApplicationsCount <= 5 THEN 'Light (1-5)'
          WHEN AssignedApplicationsCount <= 15 THEN 'Moderate (6-15)'
          WHEN AssignedApplicationsCount <= 30 THEN 'Heavy (16-30)'
          ELSE 'Very Heavy (30+)'
        END
      `,
      type: `string`,
      title: `Workload Category`,
      description: `Categorized workload based on assigned applications`
    },

    loginFrequency: {
      sql: `
        CASE 
          WHEN LastLogin IS NULL THEN 'Never Logged In'
          WHEN DaysSinceLastLogin <= 1 THEN 'Daily'
          WHEN DaysSinceLastLogin <= 7 THEN 'Weekly'
          WHEN DaysSinceLastLogin <= 30 THEN 'Monthly'
          ELSE 'Rarely'
        END
      `,
      type: `string`,
      title: `Login Frequency`,
      description: `How frequently user logs in`
    },

    performanceCategory: {
      sql: `
        CASE 
          WHEN AssignedApplicationsCount = 0 THEN 'No Data'
          WHEN CompletedApplicationsCount * 100.0 / AssignedApplicationsCount >= 90 THEN 'Excellent'
          WHEN CompletedApplicationsCount * 100.0 / AssignedApplicationsCount >= 75 THEN 'Good'
          WHEN CompletedApplicationsCount * 100.0 / AssignedApplicationsCount >= 60 THEN 'Average'
          ELSE 'Below Average'
        END
      `,
      type: `string`,
      title: `Performance Category`,
      description: `Performance category based on completion rate`
    }
  },

  segments: {
    activeUsers: {
      sql: `${CUBE}.IsActive = 1`,
      title: `Active Users`,
      description: `Users with active accounts`
    },

    recentUsers: {
      sql: `${CUBE}.CreatedAt >= DATEADD(month, -3, GETUTCDATE())`,
      title: `Recent Users`,
      description: `Users created in last 3 months`
    },

    highPerformers: {
      sql: `
        ${CUBE}.AssignedApplicationsCount > 0 AND 
        (${CUBE}.CompletedApplicationsCount * 100.0 / ${CUBE}.AssignedApplicationsCount) >= 80
      `,
      title: `High Performers`,
      description: `Users with 80%+ completion rate`
    },

    adminUsers: {
      sql: `${CUBE}.RoleName = 'ADMIN'`,
      title: `Admin Users`,
      description: `Users with admin role`
    },

    engineerUsers: {
      sql: `${CUBE}.RoleName LIKE '%ENGINEER%'`,
      title: `Engineer Users`,
      description: `Users with engineer roles`
    },

    regularLoginUsers: {
      sql: `${CUBE}.DaysSinceLastLogin <= 7`,
      title: `Regular Login Users`,
      description: `Users who logged in within last week`
    }
  },

  preAggregations: {
    // Main user statistics
    main: {
      measures: [
        CUBE.count,
        CUBE.activeCount,
        CUBE.totalAssignedApplications,
        CUBE.totalCompletedApplications,
        CUBE.userProductivity
      ],
      dimensions: [CUBE.roleName, CUBE.status, CUBE.workloadCategory],
      timeDimension: CUBE.createdAt,
      granularity: `month`,
      refreshKey: {
        every: `30 minutes`
      }
    },

    // Role-wise breakdown
    roleBreakdown: {
      measures: [
        CUBE.count,
        CUBE.activeCount,
        CUBE.averageAssignedApplications,
        CUBE.userProductivity
      ],
      dimensions: [CUBE.roleName, CUBE.performanceCategory],
      refreshKey: {
        every: `1 hour`
      }
    },

    // User growth over time
    userGrowth: {
      measures: [CUBE.count],
      dimensions: [CUBE.roleName],
      timeDimension: CUBE.createdAt,
      granularity: `month`,
      refreshKey: {
        every: `2 hours`
      }
    },

    // Performance metrics
    performance: {
      measures: [
        CUBE.totalAssignedApplications,
        CUBE.totalCompletedApplications,
        CUBE.userProductivity
      ],
      dimensions: [CUBE.fullName, CUBE.roleName, CUBE.performanceCategory],
      refreshKey: {
        every: `1 hour`
      }
    }
  }
});
