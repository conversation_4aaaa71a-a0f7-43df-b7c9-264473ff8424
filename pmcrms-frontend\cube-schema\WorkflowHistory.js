cube(`WorkflowHistory`, {
  sql: `
    SELECT 
      Id,
      ApplicationId,
      FromStatus,
      ToStatus,
      ChangedBy,
      ChangedByUserId,
      ChangedDate,
      Remarks,
      IsAutomaticTransition,
      DATEDIFF(hour, LAG(ChangedDate) OVER (PARTITION BY ApplicationId ORDER BY ChangedDate), ChangedDate) as StageProcessingHours
    FROM ApplicationWorkflowHistory
  `,

  joins: {
    Applications: {
      relationship: `belongsTo`,
      sql: `${CUBE}.ApplicationId = ${Applications}.id`
    },

    Users: {
      relationship: `belongsTo`,
      sql: `${CUBE}.ChangedByUserId = ${Users}.id`
    }
  },

  measures: {
    count: {
      type: `count`,
      title: `Total Workflow Changes`,
      description: `Total number of workflow transitions`
    },

    averageStageTime: {
      type: `avg`,
      sql: `StageProcessingHours`,
      title: `Average Stage Processing Time (Hours)`,
      description: `Average time spent in each workflow stage`
    },

    totalStageTime: {
      type: `sum`,
      sql: `StageProcessingHours`,
      title: `Total Stage Processing Time (Hours)`
    },

    automaticTransitions: {
      type: `sum`,
      sql: `CASE WHEN IsAutomaticTransition = 1 THEN 1 ELSE 0 END`,
      title: `Automatic Transitions`,
      description: `Number of automatic workflow transitions`
    },

    manualTransitions: {
      type: `sum`,
      sql: `CASE WHEN IsAutomaticTransition = 0 THEN 1 ELSE 0 END`,
      title: `Manual Transitions`,
      description: `Number of manual workflow transitions`
    }
  },

  dimensions: {
    id: {
      sql: `Id`,
      type: `number`,
      primaryKey: true
    },

    applicationId: {
      sql: `ApplicationId`,
      type: `string`,
      title: `Application ID`
    },

    fromStatus: {
      sql: `FromStatus`,
      type: `string`,
      title: `From Status`,
      description: `Previous workflow status`
    },

    toStatus: {
      sql: `ToStatus`,
      type: `string`,
      title: `To Status`,
      description: `New workflow status`
    },

    changedBy: {
      sql: `ChangedBy`,
      type: `string`,
      title: `Changed By`,
      description: `User who made the status change`
    },

    changedByUserId: {
      sql: `ChangedByUserId`,
      type: `number`,
      title: `Changed By User ID`
    },

    remarks: {
      sql: `Remarks`,
      type: `string`,
      title: `Remarks`,
      description: `Comments or notes about the status change`
    },

    isAutomaticTransition: {
      sql: `IsAutomaticTransition`,
      type: `boolean`,
      title: `Is Automatic Transition`
    },

    changedDate: {
      sql: `ChangedDate`,
      type: `time`,
      title: `Changed Date`,
      description: `Date and time of status change`
    },

    stageProcessingHours: {
      sql: `StageProcessingHours`,
      type: `number`,
      title: `Stage Processing Hours`,
      description: `Hours spent in the previous stage`
    },

    // Derived dimensions
    changedMonth: {
      sql: `FORMAT(ChangedDate, 'yyyy-MM')`,
      type: `string`,
      title: `Changed Month`
    },

    transitionType: {
      sql: `
        CASE 
          WHEN FromStatus IS NULL THEN 'Initial Submission'
          WHEN ToStatus = 'REJECTED' THEN 'Rejection'
          WHEN ToStatus = 'FINAL_APPROVE' THEN 'Final Approval'
          WHEN ToStatus LIKE '%_PENDING' THEN 'Stage Progression'
          ELSE 'Other'
        END
      `,
      type: `string`,
      title: `Transition Type`
    },

    workflowDirection: {
      sql: `
        CASE 
          WHEN ToStatus = 'REJECTED' THEN 'Rejection'
          WHEN ToStatus = 'FINAL_APPROVE' THEN 'Approval'
          WHEN FromStatus IS NULL THEN 'Start'
          ELSE 'Progress'
        END
      `,
      type: `string`,
      title: `Workflow Direction`
    }
  },

  segments: {
    approvals: {
      sql: `${CUBE}.ToStatus = 'FINAL_APPROVE'`
    },

    rejections: {
      sql: `${CUBE}.ToStatus = 'REJECTED'`
    },

    automaticTransitions: {
      sql: `${CUBE}.IsAutomaticTransition = 1`
    },

    manualTransitions: {
      sql: `${CUBE}.IsAutomaticTransition = 0`
    },

    thisMonth: {
      sql: `${CUBE}.ChangedDate >= DATEADD(month, DATEDIFF(month, 0, GETUTCDATE()), 0)`
    },

    engineeringReviews: {
      sql: `${CUBE}.ToStatus IN ('JUNIOR_ENGINEER_PENDING', 'ASSISTANT_ENGINEER_PENDING', 'EXECUTIVE_ENGINEER_PENDING')`
    }
  },

  preAggregations: {
    dailyWorkflowStats: {
      measures: [
        CUBE.count,
        CUBE.averageStageTime,
        CUBE.automaticTransitions,
        CUBE.manualTransitions
      ],
      dimensions: [
        CUBE.fromStatus,
        CUBE.toStatus,
        CUBE.transitionType
      ],
      timeDimension: CUBE.changedDate,
      granularity: `day`,
      refreshKey: {
        every: `1 hour`,
      },
    },

    workflowPerformance: {
      measures: [
        CUBE.count,
        CUBE.averageStageTime
      ],
      dimensions: [
        CUBE.changedBy,
        CUBE.toStatus,
        CUBE.workflowDirection
      ],
      refreshKey: {
        every: `30 minutes`,
      },
    },

    monthlyWorkflowTrends: {
      measures: [
        CUBE.count,
        CUBE.automaticTransitions,
        CUBE.manualTransitions
      ],
      dimensions: [
        CUBE.transitionType,
        CUBE.workflowDirection
      ],
      timeDimension: CUBE.changedDate,
      granularity: `month`,
      refreshKey: {
        every: `1 day`,
      },
    }
  },

  dataSource: `default`
});
