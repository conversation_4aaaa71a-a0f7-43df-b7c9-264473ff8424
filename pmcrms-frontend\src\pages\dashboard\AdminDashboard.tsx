import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { 
  Users, 
  Settings, 
  BarChart3, 
  Shield, 
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Activity,
  Loader2
} from 'lucide-react'
import { AdminAPIService, DashboardStats } from '../../lib/adminAuth'
import AdminLayout from '../../components/AdminLayout'
import ReportsPreviewWidget from '../../components/ReportsPreviewWidget'

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Try to fetch from API first
      try {
        const dashboardStats = await AdminAPIService.getDashboardStats()
        setStats(dashboardStats)
        console.log('Dashboard data loaded from API:', dashboardStats)
      } catch (apiError: any) {
        console.warn('Dashboard API call failed, using mock data:', apiError.message)
        
        // Check if it's a 404 (endpoint not implemented)
        if (apiError.message.includes('404') || apiError.message.includes('Not Found')) {
          setError('Dashboard API endpoints not yet implemented on backend. Showing sample data.')
        } else {
          setError('Unable to connect to dashboard API. Showing sample data.')
        }
        
        // Use mock data for UI testing
        setStats({
          totalApplications: 1250,
          pending: 45, // Applications in various pending states
          approved: 980, // FINAL_APPROVE status
          rejected: 125, // Would need to add REJECTED status if needed
          inProgress: 100, // Applications in various review stages
          monthlyTrend: [120, 135, 98, 156, 142, 180, 165],
          positionBreakdown: {
            'Architect': 450,
            'License Engineer': 380,
            'Structural Engineer': 200,
            'Supervisor1': 120,
            'Supervisor2': 100
          },
          recentApplications: [
            {
              id: '1',
              applicationNumber: 'PMC-2025-0150',
              applicantName: 'John Doe',
              position: 'Architect',
              status: 'DOCUMENT_VERIFICATION_PENDING',
              submittedDate: new Date().toISOString()
            },
            {
              id: '2',
              applicationNumber: 'PMC-2025-0151',
              applicantName: 'Alice Johnson',
              position: 'License Engineer',
              status: 'FINAL_APPROVE',
              submittedDate: new Date().toISOString()
            },
            {
              id: '3',
              applicationNumber: 'PMC-2025-0152',
              applicantName: 'Robert Brown',
              position: 'Structural Engineer',
              status: 'JUNIOR_ENGINEER_PENDING',
              submittedDate: new Date().toISOString()
            }
          ],
          systemHealth: {
            status: 'Good',
            uptime: '99.9%',
            lastBackup: new Date().toISOString()
          }
        })
      }
    } catch (error: any) {
      console.error('Dashboard loading error:', error)
      setError('Failed to load dashboard. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading dashboard data...</span>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">System overview and quick actions</p>
        </div>

        {error && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="flex items-center justify-between p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-blue-600" />
                <div>
                  <span className="text-blue-800 font-medium">Demo Mode: </span>
                  <span className="text-blue-700">
                    {error.includes('404') || error.includes('Not Found') 
                      ? 'Backend dashboard APIs not implemented yet. Showing sample data for UI testing.'
                      : error}
                  </span>
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={loadDashboardData}
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
              >
                Retry API
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalApplications.toLocaleString() || '0'}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
              <Clock className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{stats?.pending || '0'}</div>
              <p className="text-xs text-muted-foreground">All pending stages</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Final Approved</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats?.approved || '0'}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.totalApplications ? Math.round((stats.approved / stats.totalApplications) * 100) : '0'}% completion rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Activity className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats?.systemHealth.status || 'Unknown'}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.systemHealth.uptime || '0%'} uptime
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Position Breakdown Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Applications by Position Type</CardTitle>
            <CardDescription>Distribution of applications across different position categories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.positionBreakdown && Object.entries(stats.positionBreakdown).map(([position, count]) => (
                <div key={position} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{position}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${stats.totalApplications ? (count / stats.totalApplications) * 100 : 0}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Applications Management
              </CardTitle>
              <CardDescription>
                View and manage all applications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• {stats?.pending || 0} applications pending review</p>
                <p>• {stats?.inProgress || 0} applications in progress</p>
                <p>• Bulk operations available</p>
              </div>
              <Button className="w-full" onClick={() => window.location.href = '/admin/applications'}>
                Manage Applications
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5" />
                User Management
              </CardTitle>
              <CardDescription>
                Manage users and officers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Create and manage officer accounts</p>
                <p>• Role-based access control</p>
                <p>• User activity monitoring</p>
              </div>
              <Button variant="outline" className="w-full">
                Manage Users
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="mr-2 h-5 w-5" />
                Reports & Analytics
              </CardTitle>
              <CardDescription>
                System performance insights
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Processing time analytics</p>
                <p>• Performance metrics</p>
                <p>• Custom report generation</p>
              </div>
              <Button variant="outline" className="w-full" onClick={() => window.location.href = '/admin/reports'}>
                View Reports
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Security & Audit
              </CardTitle>
              <CardDescription>
                Security monitoring and logs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Audit trail monitoring</p>
                <p>• Security policy enforcement</p>
                <p>• Access logging</p>
              </div>
              <Button variant="outline" className="w-full">
                Security Center
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                System Configuration
              </CardTitle>
              <CardDescription>
                Configure system settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Workflow configuration</p>
                <p>• Email templates</p>
                <p>• System parameters</p>
              </div>
              <Button variant="outline" className="w-full">
                System Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                System Health
              </CardTitle>
              <CardDescription>
                Monitor system performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Server status: {stats?.systemHealth.status || 'Unknown'}</p>
                <p>• Uptime: {stats?.systemHealth.uptime || 'N/A'}</p>
                <p>• Last backup: {stats?.systemHealth.lastBackup ? new Date(stats.systemHealth.lastBackup).toLocaleDateString() : 'N/A'}</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  View Logs
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  Backup Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Preview Widget */}
        <ReportsPreviewWidget />

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent System Activity</CardTitle>
            <CardDescription>Latest actions and updates in the system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-md">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Application #PMC-2025-0150 approved</p>
                  <p className="text-xs text-gray-500">Executive Engineer • 2 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-amber-50 rounded-md">
                <Clock className="h-5 w-5 text-amber-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium">New application submitted for review</p>
                  <p className="text-xs text-gray-500">Application #PMC-2025-0151 • 5 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-md">
                <Users className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium">New officer account created</p>
                  <p className="text-xs text-gray-500">Junior Engineer role • 15 minutes ago</p>
                </div>
              </div>
              
              <div className="text-center pt-4">
                <Button variant="outline" size="sm">
                  View All Activity
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}

export default AdminDashboard
