using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

/// <summary>
/// Qualification Collection Entity as per PromptFile5.pdf [642bd5a0b101005f73b02ba0D]
/// </summary>
public class Qualification
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string InstituteName { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string UniversityName { get; set; } = string.Empty;

    [Required]
    [StringLength(150)]
    public string CourseSpecialization { get; set; } = string.Empty;

    public string? CertificateFilePath { get; set; } // File path for Certificate

    public string? LastYearMarksheetFilePath { get; set; } // File path for LastYearMarksheet

    [Required]
    [StringLength(100)]
    public string DegreeProgram { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string PassingMonth { get; set; } = string.Empty;

    [Required]
    [StringLength(4)]
    public string PassingYear { get; set; } = string.Empty;

    // Navigation properties
    public int ApplicationId { get; set; }
    public Application Application { get; set; } = null!;
}
