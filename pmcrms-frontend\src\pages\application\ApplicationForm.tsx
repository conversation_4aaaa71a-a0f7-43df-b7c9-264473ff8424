import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { FileText, ArrowLeft } from 'lucide-react'

const ApplicationForm: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <FileText className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">Professional Registration Application</CardTitle>
          <CardDescription>
            Multi-step form for professional registration
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              Application form wizard will be implemented in the next phase
            </p>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">This form will include:</p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Personal Information</li>
                <li>• Address Details (Permanent & Local)</li>
                <li>• Work Experience</li>
                <li>• Educational Qualifications</li>
                <li>• Document Uploads</li>
                <li>• Application Review & Submit</li>
              </ul>
            </div>
            <div className="mt-6 space-y-2">
              <Link to="/status">
                <Button variant="outline" className="w-full">
                  Check Application Status
                </Button>
              </Link>
              <Link to="/login">
                <Button className="w-full">
                  Login to Continue
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Back to Home */}
      <Link 
        to="/" 
        className="fixed top-4 left-4 flex items-center space-x-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Back to Home</span>
      </Link>
    </div>
  )
}

export default ApplicationForm
