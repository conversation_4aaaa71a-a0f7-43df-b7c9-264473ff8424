using System.Net;
using System.Text.Json;
using pmcrms_api.Application.Exceptions;
using FluentValidation;

namespace pmcrms_api.API.Middleware
{
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ErrorHandlingMiddleware> _logger;
        private readonly IHostEnvironment _environment;

        public ErrorHandlingMiddleware(
            RequestDelegate next,
            ILogger<ErrorHandlingMiddleware> logger,
            IHostEnvironment environment)
        {
            _next = next;
            _logger = logger;
            _environment = environment;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        private Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            var code = HttpStatusCode.InternalServerError;
            var result = string.Empty;

            switch (exception)
            {
                case FluentValidation.ValidationException validationException:
                    code = HttpStatusCode.BadRequest;
                    result = JsonSerializer.Serialize(new { 
                        type = "ValidationFailure",
                        errors = validationException.Errors 
                    });
                    break;
                

                case BadRequestException badRequestException:
                    code = HttpStatusCode.BadRequest;
                    result = JsonSerializer.Serialize(new { 
                        type = "BadRequest",
                        message = badRequestException.Message 
                    });
                    break;

                case NotFoundException notFoundException:
                    code = HttpStatusCode.NotFound;
                    result = JsonSerializer.Serialize(new { 
                        type = "NotFound",
                        message = notFoundException.Message 
                    });
                    break;

                case UnauthorizedException unauthorizedException:
                    code = HttpStatusCode.Unauthorized;
                    result = JsonSerializer.Serialize(new { 
                        type = "Unauthorized",
                        message = unauthorizedException.Message 
                    });
                    break;

                case ForbiddenException forbiddenException:
                    code = HttpStatusCode.Forbidden;
                    result = JsonSerializer.Serialize(new { 
                        type = "Forbidden",
                        message = forbiddenException.Message 
                    });
                    break;

                default:
                    _logger.LogError(exception, "Unhandled exception occurred");
                    result = JsonSerializer.Serialize(new { 
                        type = "ServerError",
                        message = _environment.IsDevelopment() 
                            ? exception.ToString() 
                            : "An unexpected error occurred." 
                    });
                    break;
            }

            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)code;

            return context.Response.WriteAsync(result);
        }
    }

    public static class ErrorHandlingMiddlewareExtensions
    {
        public static IApplicationBuilder UseErrorHandling(
            this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ErrorHandlingMiddleware>();
        }
    }
}
