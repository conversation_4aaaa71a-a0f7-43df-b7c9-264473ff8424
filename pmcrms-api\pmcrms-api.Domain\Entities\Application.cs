using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using pmcrms_api.Domain.Enums;
using System.Text.RegularExpressions;

namespace pmcrms_api.Domain.Entities;

public class Application
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string ApplicationNumber { get; set; } = string.Empty;

    public WorkflowStateType CurrentState { get; set; }
    
    public List<WorkflowStateHistory> WorkflowStateHistory { get; set; } = new();
    
    // ===========================================
    // PDF FILES (Certificate, SelfDeclarationForm, Challan)
    // ===========================================
    public string? CertificateFilePath { get; set; }
    public string? SelfDeclarationFormFilePath { get; set; }
    public string? ChallanFilePath { get; set; }

    // ===========================================
    // DOCUMENT FILE PATHS
    // ===========================================
    public string? PanAttachmentFilePath { get; set; }
    public string? AadharAttachmentFilePath { get; set; }
    public string? CoaCertificateFilePath { get; set; }
    public string? ElectricityBillFilePath { get; set; }
    public string? StructuralEngineerDocFilePath { get; set; }
    public string? ProfilePictureFilePath { get; set; }

    // ===========================================
    // PERSONAL INFORMATION
    // ===========================================
    [Required]
    [StringLength(100)]
    [RegularExpression(@"^[a-zA-Z]+(?:[a-zA-Z]+)*$", ErrorMessage = "Invalid first name format")]
    public string FirstName { get; set; } = string.Empty;

    [StringLength(100)]
    [RegularExpression(@"^[a-zA-Z]+(?:[a-zA-Z]+)*$", ErrorMessage = "Invalid middle name format")]
    public string? MiddleName { get; set; }

    [Required]
    [StringLength(100)]
    [RegularExpression(@"^[a-zA-Z]+(?:[a-zA-Z]+)*$", ErrorMessage = "Invalid last name format")]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StringLength(150)]
    [RegularExpression(@"^[a-zA-Z]+(?: [a-zA-Z]+)*$", ErrorMessage = "Invalid mother name format")]
    public string MotherName { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    [RegularExpression(@"^(\+\d{1,3}[-]?)?\d{10}$", ErrorMessage = "Invalid mobile number format")]
    public string MobileNumber { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(150)]
    public string EmailAddress { get; set; } = string.Empty;

    public string? Password { get; set; }
    
    public bool TransactionMode { get; set; }
    
    public DateTime? TransactionDate { get; set; }
    
    public string? ReviewDate { get; set; }

    [StringLength(100)]
    public string? ContactPerson { get; set; }

    [Required]
    [StringLength(100)]
    public string Place { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string RoomNumber { get; set; } = string.Empty;

    [Required]
    public BloodGroup BloodGroup { get; set; }

    [Required]
    public decimal Height { get; set; }

    [Required]
    public Gender Gender { get; set; }

    [Required]
    public PositionType Position { get; set; }

    [Required]
    public DateTime BirthDate { get; set; }

    [Required]  
    [StringLength(10)]
    [RegularExpression(@"^[A-Z]{5}[0-9]{4}[A-Z]{1}$", ErrorMessage = "Invalid PAN format")]
    public string PanNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(12)]
    [RegularExpression(@"^\d{12}$", ErrorMessage = "Invalid Aadhar format")]
    public string AadharNumber { get; set; } = string.Empty;

    // COA fields for architects
    [StringLength(50)]
    public string? CoaCertificationNumber { get; set; }

    public DateTime? CoaValidity { get; set; }

    // ===========================================
    // ADDRESS INFORMATION
    // ===========================================
    public bool PermanentSameAsLocal { get; set; }

    // ===========================================
    // CERTIFICATE GENERATION FIELDS
    // ===========================================
    public string? CertificateNumber { get; set; }
    public bool IsPayment { get; set; }
    public string? QrCodeUrl { get; set; }
    public byte[]? ProfilePhoto { get; set; }
    public byte[]? Logo { get; set; }
    public byte[]? ExecEngSignature { get; set; }
    public byte[]? CityEngSignature { get; set; }

    // ===========================================
    // CERTIFICATE CONTENT FIELDS
    // ===========================================
    [NotMapped]
    public string ApplicantName => $"{FirstName} {MiddleName} {LastName}".Trim();
    
    public DateTime? FromDate { get; set; }
    public int? ToYear { get; set; }

    // ===========================================
    // PAYMENT INTEGRATION FIELDS
    // ===========================================
    public decimal Amount { get; set; }
    public string? ChallanNumber { get; set; }

    // ===========================================
    // SYSTEM FIELDS
    // ===========================================
    public DateTime SubmissionDate { get; set; }
    public DateTime? ApprovalDate { get; set; }

    // Assignment tracking fields
    public int? AssignedToUserId { get; set; }
    public User? AssignedTo { get; set; }
    public DateTime? AssignedAt { get; set; }

    // ===========================================
    // NAVIGATION PROPERTIES
    // ===========================================
    public int UserId { get; set; }
    public User User { get; set; } = null!;

    public int WorkflowStateId { get; set; }
    public WorkflowState WorkflowState { get; set; } = null!;

    // Collections
    public ICollection<Document> Documents { get; set; } = new List<Document>();
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
    public ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
    public ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    
    public ICollection<Experience> Experiences { get; set; } = new List<Experience>();
    public ICollection<Qualification> Qualifications { get; set; } = new List<Qualification>();
    public ICollection<Address> Addresses { get; set; } = new List<Address>();
}
