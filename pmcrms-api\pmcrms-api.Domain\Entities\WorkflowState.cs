using System.ComponentModel.DataAnnotations;

namespace pmcrms_api.Domain.Entities;

public class WorkflowState
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Description { get; set; }

    public int Order { get; set; }

    [Required]
    public bool IsFinal { get; set; }

    // Navigation properties
    public ICollection<Application> Applications { get; set; } = new List<Application>();
}
