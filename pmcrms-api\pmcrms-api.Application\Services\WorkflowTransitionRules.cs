using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Application.Services;

public class WorkflowTransitionRules
{
    /// <summary>
    /// Defines allowed workflow transitions based on current state and user role
    /// Following the complete workflow as specified in PromptFile5.pdf
    /// </summary>
    public static readonly Dictionary<(WorkflowStateType From, string Role), WorkflowStateType[]> AllowedTransitions = new()
    {
        // ===========================================
        // JUNIOR ENGINEER TRANSITIONS
        // ===========================================
        // Junior Engineers can schedule appointments and verify documents
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "JRENGG_ARCH"), new[] { WorkflowStateType.JUNIOR_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "JRENGG_STRU"), new[] { WorkflowStateType.JUNIOR_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "JRENGG_LICE"), new[] { WorkflowStateType.JUNIOR_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "JRENGG_SUPER1"), new[] { WorkflowStateType.JUNIOR_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "JRENGG_SUPER2"), new[] { WorkflowStateType.JUNIOR_ENGINEER_PENDING, WorkflowStateType.REJECTED } },

        // Junior Engineers can complete document verification and move to Assistant Engineer
        { (WorkflowStateType.JUNIOR_ENGINEER_PENDING, "JRENGG_ARCH"), new[] { WorkflowStateType.ASSISTANT_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.JUNIOR_ENGINEER_PENDING, "JRENGG_STRU"), new[] { WorkflowStateType.ASSISTANT_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.JUNIOR_ENGINEER_PENDING, "JRENGG_LICE"), new[] { WorkflowStateType.ASSISTANT_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.JUNIOR_ENGINEER_PENDING, "JRENGG_SUPER1"), new[] { WorkflowStateType.ASSISTANT_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.JUNIOR_ENGINEER_PENDING, "JRENGG_SUPER2"), new[] { WorkflowStateType.ASSISTANT_ENGINEER_PENDING, WorkflowStateType.REJECTED } },

        // ===========================================
        // ASSISTANT ENGINEER TRANSITIONS
        // ===========================================
        { (WorkflowStateType.ASSISTANT_ENGINEER_PENDING, "ASSIENGG_ARCH"), new[] { WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.ASSISTANT_ENGINEER_PENDING, "ASSIENGG_STRU"), new[] { WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.ASSISTANT_ENGINEER_PENDING, "ASSIENGG_LICE"), new[] { WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.ASSISTANT_ENGINEER_PENDING, "ASSIENGG_SUPER1"), new[] { WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.ASSISTANT_ENGINEER_PENDING, "ASSIENGG_SUPER2"), new[] { WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, WorkflowStateType.REJECTED } },

        // ===========================================
        // EXECUTIVE ENGINEER TRANSITIONS
        // ===========================================
        { (WorkflowStateType.EXECUTIVE_ENGINEER_PENDING, "ExecutiveEngineer"), new[] { WorkflowStateType.CITY_ENGINEER_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING, "ExecutiveEngineer"), new[] { WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING, WorkflowStateType.REJECTED } },

        // ===========================================
        // CITY ENGINEER TRANSITIONS
        // ===========================================
        { (WorkflowStateType.CITY_ENGINEER_PENDING, "CityEngineer"), new[] { WorkflowStateType.PAYMENT_PENDING, WorkflowStateType.REJECTED } },
        { (WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING, "CityEngineer"), new[] { WorkflowStateType.FINAL_APPROVE, WorkflowStateType.REJECTED } },

        // ===========================================
        // CLERK TRANSITIONS
        // ===========================================
        { (WorkflowStateType.CLERK_PENDING, "Clerk"), new[] { WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING, WorkflowStateType.REJECTED } },

        // ===========================================
        // PAYMENT SYSTEM TRANSITIONS
        // ===========================================
        { (WorkflowStateType.PAYMENT_PENDING, "OfflinePaymentOfficer"), new[] { WorkflowStateType.CLERK_PENDING, WorkflowStateType.REJECTED } },

        // ===========================================
        // ADMIN OVERRIDE TRANSITIONS
        // ===========================================
        { (WorkflowStateType.REJECTED, "Admin"), new[] { 
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, 
            WorkflowStateType.JUNIOR_ENGINEER_PENDING,
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING,
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING,
            WorkflowStateType.CITY_ENGINEER_PENDING,
            WorkflowStateType.PAYMENT_PENDING,
            WorkflowStateType.CLERK_PENDING,
            WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING,
            WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING
        }},

        // Admin can transition from any state to any state (for system maintenance)
        { (WorkflowStateType.DOCUMENT_VERIFICATION_PENDING, "Admin"), new[] { 
            WorkflowStateType.JUNIOR_ENGINEER_PENDING, 
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING, 
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING,
            WorkflowStateType.CITY_ENGINEER_PENDING,
            WorkflowStateType.PAYMENT_PENDING,
            WorkflowStateType.CLERK_PENDING,
            WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING,
            WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING,
            WorkflowStateType.FINAL_APPROVE,
            WorkflowStateType.REJECTED 
        }}
    };

    public static bool CanTransition(WorkflowStateType fromState, WorkflowStateType toState, string role)
    {
        if (AllowedTransitions.TryGetValue((fromState, role), out var allowedStates))
        {
            return allowedStates.Contains(toState);
        }
        return false;
    }

    public static WorkflowStateType[] GetAvailableStates(WorkflowStateType fromState, string role)
    {
        if (AllowedTransitions.TryGetValue((fromState, role), out var allowedStates))
        {
            return allowedStates;
        }
        return Array.Empty<WorkflowStateType>();
    }

    /// <summary>
    /// Gets the next logical state in the workflow sequence
    /// Used by the system for automatic transitions after successful operations
    /// </summary>
    public static WorkflowStateType? GetNextWorkflowState(WorkflowStateType currentState)
    {
        return currentState switch
        {
            WorkflowStateType.DOCUMENT_VERIFICATION_PENDING => WorkflowStateType.JUNIOR_ENGINEER_PENDING,
            WorkflowStateType.JUNIOR_ENGINEER_PENDING => WorkflowStateType.ASSISTANT_ENGINEER_PENDING,
            WorkflowStateType.ASSISTANT_ENGINEER_PENDING => WorkflowStateType.EXECUTIVE_ENGINEER_PENDING,
            WorkflowStateType.EXECUTIVE_ENGINEER_PENDING => WorkflowStateType.CITY_ENGINEER_PENDING,
            WorkflowStateType.CITY_ENGINEER_PENDING => WorkflowStateType.PAYMENT_PENDING,
            WorkflowStateType.PAYMENT_PENDING => WorkflowStateType.CLERK_PENDING,
            WorkflowStateType.CLERK_PENDING => WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING,
            WorkflowStateType.EXECUTIVE_DIGITAL_SIGNATURE_PENDING => WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING,
            WorkflowStateType.CITY_DIGITAL_SIGNATURE_PENDING => WorkflowStateType.FINAL_APPROVE,
            WorkflowStateType.FINAL_APPROVE => null, // Final state
            WorkflowStateType.REJECTED => null, // Terminal state
            _ => null
        };
    }
}
