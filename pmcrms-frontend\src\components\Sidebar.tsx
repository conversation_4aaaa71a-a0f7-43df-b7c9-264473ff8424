import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Home, 
  FileText, 
  Users, 
  Settings, 
  BarChart3,
  <PERSON><PERSON><PERSON>ck,
  CreditCard,
  Shield
} from 'lucide-react'
import { useAuthStore } from '../store'
import { cn } from '../lib/utils'

interface NavItem {
  label: string
  href: string
  icon: React.ComponentType<any>
  roles: string[]
}

const navItems: NavItem[] = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    roles: ['JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2', 
            'ASSIENGG_ARCH', 'ASSIENGG_STRU', 'ASSIENGG_LICE', 'ASSIENGG_SUPER1', 'ASSIENGG_SUPER2',
            'EXECUTIVE_ENGINEER', 'CITY_ENGINEER', '<PERSON><PERSON>R<PERSON>', 'ADMIN']
  },
  {
    label: 'Applications',
    href: '/dashboard/applications',
    icon: FileText,
    roles: ['JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2', 
            'ASSIENGG_ARCH', 'ASSIENGG_STRU', 'ASSIENGG_LICE', 'ASSIENGG_SUPER1', 'ASSIENGG_SUPER2',
            'EXECUTIVE_ENGINEER', 'CITY_ENGINEER']
  },
  {
    label: 'Document Verification',
    href: '/dashboard/verification',
    icon: FileCheck,
    roles: ['JRENGG_ARCH', 'JRENGG_STRU', 'JRENGG_LICE', 'JRENGG_SUPER1', 'JRENGG_SUPER2']
  },
  {
    label: 'Digital Signatures',
    href: '/dashboard/signatures',
    icon: Shield,
    roles: ['EXECUTIVE_ENGINEER', 'CITY_ENGINEER']
  },
  {
    label: 'Payment Processing',
    href: '/dashboard/payments',
    icon: CreditCard,
    roles: ['CLERK', 'OFFLINE_PAYMENT_OFFICER']
  },
  {
    label: 'User Management',
    href: '/dashboard/users',
    icon: Users,
    roles: ['ADMIN']
  },
  {
    label: 'Reports',
    href: '/dashboard/reports',
    icon: BarChart3,
    roles: ['ADMIN', 'EXECUTIVE_ENGINEER', 'CITY_ENGINEER']
  },
  {
    label: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    roles: ['ADMIN']
  },
]

const Sidebar: React.FC = () => {
  const { user } = useAuthStore()
  const location = useLocation()

  const filteredNavItems = navItems.filter(item => 
    user && item.roles.includes(user.role)
  )

  return (
    <div className="bg-white w-64 min-h-screen shadow-sm border-r border-gray-200 fixed left-0 top-16 z-40">
      <nav className="p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const Icon = item.icon
          const isActive = location.pathname === item.href || 
                          location.pathname.startsWith(item.href + '/')
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                isActive 
                  ? 'bg-primary text-white' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{item.label}</span>
            </Link>
          )
        })}
      </nav>
    </div>
  )
}

export default Sidebar
