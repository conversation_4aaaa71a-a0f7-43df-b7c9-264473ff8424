// Master Cube.js Configuration File
// This file registers all cube schemas and provides global configuration

module.exports = {
  // Database connection configuration
  database: {
    type: process.env.CUBEJS_DB_TYPE || 'mssql',
    host: process.env.CUBEJS_DB_HOST || 'localhost',
    port: process.env.CUBEJS_DB_PORT || 1433,
    username: process.env.CUBEJS_DB_USER || 'sa',
    password: process.env.CUBEJS_DB_PASS || 'your_password',
    database: process.env.CUBEJS_DB_NAME || 'PMCRMSDatabase',
    options: {
      encrypt: false, // Set to true if using Azure SQL Database
      trustServerCertificate: true
    }
  },

  // API configuration
  api: {
    port: process.env.CUBEJS_API_PORT || 4000,
    host: process.env.CUBEJS_API_HOST || '0.0.0.0',
    cors: {
      origin: [
        'http://localhost:3000', // React development server
        'http://localhost:8080', // Alternative dev server
        'https://your-frontend-domain.com' // Production frontend
      ],
      credentials: true
    }
  },

  // Schema configuration
  schema: {
    path: './CubeJS-Setup',
    files: [
      'Applications.js',
      'Users.js',
      'WorkflowHistory.js',
      'SystemMetrics.js'
    ]
  },

  // Cache configuration
  cache: {
    // Redis configuration for production
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_DB || 0
    },
    
    // In-memory cache for development
    type: process.env.NODE_ENV === 'production' ? 'redis' : 'memory',
    
    // Cache TTL settings
    defaultTtl: 3600, // 1 hour
    maxAge: 86400 // 24 hours
  },

  // Pre-aggregation configuration
  preAggregations: {
    // External storage for pre-aggregations (recommended for production)
    externalDriverFactory: () => {
      if (process.env.NODE_ENV === 'production') {
        return {
          type: 'postgres', // or 'mysql', 'mssql' based on your external DB
          host: process.env.PREAGG_DB_HOST || 'localhost',
          port: process.env.PREAGG_DB_PORT || 5432,
          username: process.env.PREAGG_DB_USER || 'postgres',
          password: process.env.PREAGG_DB_PASS || 'password',
          database: process.env.PREAGG_DB_NAME || 'pmcrms_preagg'
        };
      }
      return null; // Use same database for development
    },
    
    // Refresh worker configuration
    refreshWorker: {
      enabled: true,
      concurrency: 2,
      continueWaitTimeout: 3000,
      orphanedTimeout: 120000
    }
  },

  // Security configuration
  security: {
    // JWT configuration
    jwt: {
      key: process.env.CUBEJS_API_SECRET || 'your-secret-key-here',
      algorithms: ['HS256'],
      checkAuth: (req, auth) => {
        // Custom authentication logic
        // This should validate the JWT token from your ASP.NET Core API
        if (!auth) {
          throw new Error('Authorization header is required');
        }
        
        // Extract token from Bearer header
        const token = auth.replace('Bearer ', '');
        
        // Here you would validate the token with your ASP.NET Core API
        // For now, we'll allow all requests with a valid Bearer token format
        return {
          userId: 'extracted-from-token',
          role: 'extracted-from-token'
        };
      }
    },

    // Context security for multi-tenant scenarios
    contextToAppId: ({ userId, role }) => `app_${userId || 'default'}`,
    contextToOrchestratorId: ({ userId, role }) => `orchestrator_${role || 'default'}`
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.NODE_ENV === 'production' ? 'json' : 'dev',
    queries: process.env.LOG_QUERIES === 'true'
  },

  // Query optimization
  query: {
    // Query timeout (in seconds)
    timeout: 300,
    
    // Maximum query complexity
    maxComplexity: 1000,
    
    // Query caching
    queryCache: true,
    queryCacheTimeout: 3600
  },

  // Development configuration
  development: {
    // Enable playground in development
    devServer: process.env.NODE_ENV !== 'production',
    
    // Playground configuration
    playground: {
      enabled: process.env.NODE_ENV !== 'production',
      authSecret: process.env.CUBEJS_API_SECRET || 'your-secret-key-here'
    },
    
    // Hot reload for schema changes
    watchFiles: true
  },

  // Performance monitoring
  telemetry: {
    enabled: process.env.CUBEJS_TELEMETRY !== 'false',
    
    // Custom telemetry handler
    handler: (event, context) => {
      if (process.env.NODE_ENV === 'production') {
        // Send to your monitoring service
        console.log('Telemetry event:', event, context);
      }
    }
  },

  // Custom middleware
  middleware: [
    // Request logging middleware
    (req, res, next) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        console.log(`${req.method} ${req.url} - ${res.statusCode} (${duration}ms)`);
      });
      next();
    },

    // Rate limiting middleware
    (req, res, next) => {
      // Implement rate limiting logic here
      next();
    }
  ],

  // Error handling
  errorHandler: (error, req, res, next) => {
    console.error('Cube.js Error:', error);
    
    // Custom error response format
    const errorResponse = {
      error: {
        message: error.message,
        type: error.constructor.name,
        timestamp: new Date().toISOString()
      }
    };

    // Don't expose stack traces in production
    if (process.env.NODE_ENV !== 'production') {
      errorResponse.error.stack = error.stack;
    }

    res.status(error.status || 500).json(errorResponse);
  },

  // Health check endpoint
  healthCheck: {
    enabled: true,
    endpoint: '/health',
    checks: [
      {
        name: 'database',
        check: async () => {
          // Implement database connectivity check
          return { status: 'ok' };
        }
      },
      {
        name: 'cache',
        check: async () => {
          // Implement cache connectivity check
          return { status: 'ok' };
        }
      }
    ]
  }
};
