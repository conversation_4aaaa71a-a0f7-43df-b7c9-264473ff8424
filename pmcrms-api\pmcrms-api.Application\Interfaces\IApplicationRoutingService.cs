using pmcrms_api.Domain.Entities;
using pmcrms_api.Domain.Enums;

namespace pmcrms_api.Application.Interfaces;

public interface IApplicationRoutingService
{
    Task<User?> GetNextEngineerForPositionAsync(PositionType position, RoleType engineerLevel);
    Task<bool> AssignApplicationToEngineerAsync(Domain.Entities.Application application, User engineer);
    Task<IEnumerable<User>> GetAvailableEngineersForPositionAsync(PositionType position, RoleType engineerLevel);
    Task<Domain.Entities.Application> RouteApplicationAsync(Domain.Entities.Application application);
}
