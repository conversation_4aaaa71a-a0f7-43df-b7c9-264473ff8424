namespace pmcrms_api.Application.DTOs.Application;

/// <summary>
/// Request model for creating a new application
/// </summary>
public class CreateApplicationRequest
{
    // Personal Information
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string EmailAddress { get; set; } = string.Empty;
    public string MobileNumber { get; set; } = string.Empty;
    public string MotherName { get; set; } = string.Empty;
    public string Place { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string BloodGroup { get; set; } = string.Empty;
    public decimal Height { get; set; }
    public string Gender { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;

    // Address Information
    public AddressDto PermanentAddress { get; set; } = new();
    public AddressDto? LocalAddress { get; set; }
    public bool PermanentSameAsLocal { get; set; }

    // Experience Information
    public List<ExperienceDto> Experiences { get; set; } = new();
}

/// <summary>
/// Request model for updating application status
/// </summary>
public class UpdateStatusRequest
{
    /// <summary>
    /// New status for the application
    /// </summary>
    public string Status { get; set; } = string.Empty;
}
